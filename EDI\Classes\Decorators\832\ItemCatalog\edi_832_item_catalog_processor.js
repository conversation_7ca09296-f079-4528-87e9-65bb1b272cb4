/**
 * @description Class containing functions specific to Item Catalog processing
 *
 * @NApiVersion 2.1
 * <AUTHOR> <<EMAIL>>
 */

define([
  "exports",
  "require",
  "N/log",
  "../../../Models/Partner/Customer/edi_customer",
], function (/** @type {any} */ exports, /** @type {any} */ require) {
  const log = require("N/log");
  const {
    EDICustomer,
  } = require("../../../Models/Partner/Customer/edi_customer");

  /**
   * Item Catalog Processor Class
   *
   * @class
   * @typedef {import("../../../Interfaces/Decorators/832/edi_832_item_catalog").EDIItemCatalogQueryResult} EDIItemCatalogQueryResult
   * @typedef {import("../../../../../Classes/vlmd_custom_error_object").CustomErrorObject} CustomErrorObject
   * @typedef {import("../../../Interfaces/Models/Partner/edi_partner").EDIPartnerInterface} EDIPartnerInterface
   */
  class EDIItemCatalogProcessor {
    /** @param {{[key:string]: any}} params */
    constructor(params) {
      /** @type {CustomErrorObject} */
      this.customError = params.customError;
      /** @type {EDIPartnerInterface} */
      this.partner = params.partner;
      /** @type {{itemType: string, fileFields: {[fileField: string]: string}}} */
      this.currentItemObj = {
        itemType: "",
        fileFields: {
          SUPPLIER_ID: "",
          FILE_TYPE: "",
          ITEM_NUMBER: "",
          UNIT_OF_MEASURE: "",
          UNIQUE_ITEM_ID: "",
          BASE_ITEM_NUMBER: "",
          VARIATION_NAME_1: "",
          VARIATION_VALUE_1: "",
          VARIATION_NAME_2: "",
          VARIATION_VALUE_2: "",
          VARIATION_NAME_3: "",
          VARIATION_VALUE_3: "",
          VARIATION_NAME_4: "",
          VARIATION_VALUE_4: "",
          ORDERABLE: "",
          NAME: "",
          DESCRIPTION: "",
          KEYWORDS: "",
          STATUS_MSG: "",
          CONTENT_UNIT_QTY: "",
          CONTENT_UNIT_SIZE: "",
          FEATURES_LIST: "",
          SPECIFICATIONS_LIST: "",
          BRAND_NAME: "",
          LEAD_TIME: "",
          QTY_INCREMENT: "",
          IS_PROPRIETARY: "",
          IS_DROP_SHIP: "",
          NET_WEIGHT: "",
          NET_WEIGHT_UNIT_OF_MEASURE: "",
          MANUFACTURER_NAME: "",
          MANUFACTURER_ITEM_NUMBER: "",
          GTIN: "",
          DEVICE_IDENTIFIER: "",
          CATEGORY_NAME_1: "",
          CATEGORY_ID_1: "",
          CATEGORY_NAME_2: "",
          CATEGORY_ID_2: "",
          CATEGORY_NAME_3: "",
          CATEGORY_ID_3: "",
          CATEGORY_NAME_4: "",
          CATEGORY_ID_4: "",
          UNSPSC: "",
          GPC: "",
          HCPCS: "",
          FDA_PRODUCT_CODE_LIST: "",
          IMAGE_URL_1: "",
          IMAGE_URL_2: "",
          IMAGE_URL_3: "",
          IMAGE_URL_4: "",
          IMAGE_URL_5: "",
          IMAGE_URL_6: "",
          DOCUMENT_URL_1: "",
          DOCUMENT_NAME_1: "",
          DOCUMENT_URL_2: "",
          DOCUMENT_NAME_2: "",
          DOCUMENT_URL_3: "",
          DOCUMENT_NAME_3: "",
          VIDEO_URL_1: "",
          VIDEO_NAME_1: "",
          VIDEO_URL_2: "",
          VIDEO_NAME_2: "",
          VIDEO_URL_3: "",
          VIDEO_NAME_3: "",
          ACCESSORY_ITEM_NUMBERS: "",
          REPLACEMENT_ITEM_NUMBERS: "",
          COMPONENT_ITEM_NUMBERS: "",
          BREAKABLE: "",
          PRICE_UNIT_OF_MEASURE: "",
        },
      };
      /** @type {boolean} */
      this.includeDeactivateItem = false;
      /** @type {boolean} */
      this.includeIsReplacementFor = false;
      /** @type {string} */
      this.catalogString = "";
      /** @type {number} */
      this.numberOfItemsInCatalog = 0;
    }

    /**
     * Set all partner-specific values, called at getInputData and in map
     */
    setSoftwareSpecificValues() {
      this.includeDeactivateItem =
        (this.partner instanceof EDICustomer &&
          !!this.partner?.shouldIncludeDeactivateItem) ||
        // @ts-ignore Property 'processor' does not exist on type 'EDIItemCatalogProcessor'.ts(2339)
        !!this.processor?.partner?.shouldIncludeDeactivateItem;

      this.includeIsReplacementFor =
        (this.partner instanceof EDICustomer &&
          !!this.partner?.shouldIncludeIsReplacementFor) ||
        // @ts-ignore Property 'processor' does not exist on type 'EDIItemCatalogProcessor'.ts(2339)
        !!this.processor?.partner?.shouldIncludeIsReplacementFor;
    }

    /**
     * Item query string for SuiteQL
     *
     * @returns {string} Query string
     */
    getItemsQueryString() {
      try {
        //Don't change the order of these results, doing so will break the structure of the file that's generated.
        let activeItemsQueryString = /*sql*/ `SELECT 
          main.internalId,
          main.itemId,
          main.description,
          main.category,
          main.itemType,
          main.imageurl,
          main.parent_unit_is_each,
          main.abbreviation,
          main.vendorName,
          main.vendorItemName,
          main.deactivate,
          '"' || LISTAGG(main.isReplacementFor, ', ') WITHIN GROUP (ORDER BY main.isReplacementFor) || '"' AS isReplacementFor
       FROM (
          SELECT DISTINCT
             item.id AS internalId,
             item.itemid AS itemId,
             item.description AS description,
             BUILTIN.DF(item.class) AS category,
             item.itemtype AS itemType,
             CASE
                WHEN item.custitem_spl_image_link_1 IS NOT NULL THEN item.custitem_spl_image_link_1 
                WHEN item.matrixtype = 'CHILD' AND parentfile.url IS NOT NULL THEN 'https://5802576.app.netsuite.com/' || parentfile.url 
                WHEN file.url IS NOT NULL THEN 'https://5802576.app.netsuite.com/' || file.url 
                ELSE '' 
             END AS imageurl,
             CASE WHEN ut.id = 1 THEN 1 ELSE 0 END AS parent_unit_is_each,
             CASE
                WHEN item.itemtype = 'Kit' THEN
                   CASE WHEN item.custitem_spl_uom_abbreviation IS NOT NULL 
                      THEN item.custitem_spl_uom_abbreviation 
                      ELSE 'EA' 
                   END
                ELSE utu.abbreviation 
             END AS abbreviation,
             NVL(BUILTIN.DF(item.custitem_spl_manufacturer_name), '') AS vendorName,
             CASE WHEN item.custitem_spl_manufacturer_name IS NOT NULL THEN item.vendorname ELSE '' END AS vendorItemName,
             COALESCE(
                CASE WHEN onm.custrecord_old_item_id IS NOT NULL THEN BUILTIN.DF(onm.custrecord_old_item_id) END,
                CASE WHEN sn.field = 'INVTITEM.SNAME' THEN sn.oldvalue END,
                ''
             ) AS isReplacementFor,
             'FALSE' AS deactivate
          FROM item 
          LEFT OUTER JOIN item parent ON item.parent = parent.id 
          LEFT OUTER JOIN unitsTypeUom utu ON item.saleunit = utu.internalid 
          LEFT OUTER JOIN unitstype ut ON item.unitstype = ut.id 
          LEFT JOIN file ON item.storedisplayimage = file.id 
          LEFT JOIN file parentfile ON parent.storedisplayimage = parentfile.id 
          LEFT JOIN customrecord_old_new_item_mapping onm ON onm.custrecord_new_item_id = item.id 
          LEFT JOIN SystemNote sn ON sn.recordid = item.id 
             AND sn.field IN ('INVTITEM.SNAME')
             AND sn.date >= (SYSDATE - 60)
          WHERE item.custitem_spl_apprvd_for_itm_ctlg = 'T' 
             AND item.isinactive = 'F' 
             AND BUILTIN.MNFILTER(item.subsidiary, 'MN_INCLUDE', '', 'FALSE', NULL, '1') = 'T' 
             AND (item.matrixtype != 'PARENT' OR item.matrixtype IS NULL)
       --      AND item.itemid = 'ADL1562'
       ) main
       GROUP BY 
          main.internalId,
          main.itemId,
          main.description,
          main.category,
          main.itemType,
          main.imageurl,
          main.parent_unit_is_each,
          main.abbreviation,
          main.vendorName,
          main.vendorItemName,
          main.deactivate`;

        let inactiveItemsQueryString = /*sql*/ `
                  SELECT DISTINCT
                    item.id AS internalId,
                    item.itemid AS itemId,
                    item.description AS description,
                    BUILTIN.DF(item.class) AS category,
                    item.itemtype AS itemType,
                    CASE 
                        WHEN item.custitem_spl_image_link_1 IS NOT NULL THEN 
                            item.custitem_spl_image_link_1
                        WHEN item.matrixtype = 'CHILD' AND parentfile.url IS NOT NULL THEN 
                            'https://5802576.app.netsuite.com/' || parentfile.url
                        WHEN file.url IS NOT NULL THEN 
                            'https://5802576.app.netsuite.com/' || file.url
                        ELSE ''
                    END AS imageurl,
                    CASE 
                        WHEN ut.id = 1 THEN 1 ELSE 0 END AS parent_unit_is_each,
                    CASE 
                        WHEN item.itemtype = 'Kit' THEN
                            CASE 
                                WHEN item.custitem_spl_uom_abbreviation IS NOT NULL THEN 
                                    item.custitem_spl_uom_abbreviation
                                ELSE 'EA'
                            END
                        ELSE utu.abbreviation
                    END AS abbreviation,
                    NVL(BUILTIN.DF(item.custitem_spl_manufacturer_name), '') AS vendorName,
                    CASE 
                        WHEN item.custitem_spl_manufacturer_name IS NOT NULL THEN item.vendorname
                        ELSE ''
                    END AS vendorItemName,
                    'TRUE' AS deactivate,
                    '"' || COALESCE(
                        CASE WHEN onm.custrecord_old_item_id IS NOT NULL THEN BUILTIN.DF(onm.custrecord_old_item_id) END,
                        CASE WHEN sn.field = 'INVTITEM.SNAME' THEN sn.oldvalue END,
                        ''
                    ) || '"' AS isReplacementFor
                FROM
                    item
                LEFT OUTER JOIN item parent ON item.parent = parent.id
                LEFT OUTER JOIN unitsTypeUom utu ON item.saleunit = utu.internalid
                LEFT OUTER JOIN unitstype ut ON item.unitstype = ut.id
                LEFT JOIN file ON item.storedisplayimage = file.id
                LEFT JOIN file parentfile ON parent.storedisplayimage = parentfile.id
                LEFT JOIN customrecord_old_new_item_mapping onm ON onm.custrecord_new_item_id = item.id 
                LEFT JOIN SystemNote sn ON sn.recordid = item.id 
                   AND sn.field IN ('INVTITEM.SNAME')
                   AND sn.date >= (SYSDATE - 60)
                WHERE
                    item.custitem_spl_apprvd_for_itm_ctlg = 'F'
                    AND item.isinactive = 'T'
                    AND BUILTIN.MNFILTER( item.subsidiary, 'MN_INCLUDE', '', 'FALSE', NULL, '1' ) = 'T'
                    AND (item.matrixtype != 'PARENT' OR item.matrixtype IS NULL)
                    AND EXISTS (
                      SELECT 1 
                      FROM SystemNote sn 
                      WHERE sn.recordid = item.id 
                      AND sn.field = 'INVTITEM.BINACTIVE'
                      AND sn.date >= (SYSDATE - 14)
                  )`;

        log.audit(
          "Item Query String - Inactive Component",
          inactiveItemsQueryString
        );
        log.audit(
          "Item Query String - Active Component",
          activeItemsQueryString
        );

        return Boolean(this.partner instanceof EDICustomer && this.partner.shouldIncludeDeactivateItem)
          ? inactiveItemsQueryString + " UNION " + activeItemsQueryString
          : activeItemsQueryString;
      } catch (err) {
        throw this.customError?.updateError({
          errorType: this.customError?.ErrorTypes.NO_VALUE_RETURNED,
          summary: "QUERY_STRING_NOT_RETURNED",
          details: `Error getting query string: ${err}`,
        });
      }
    }

    /**
     * Validate Item Catalog Query Result
     * Throw an error if the result is invalid
     *
     * @param {EDIItemCatalogQueryResult} queryResultObj Query result
     * @returns {void}
     */
    validateQueryResultValues(queryResultObj) {
      try {
        const { itemNumber, description, category } = queryResultObj;

        if (!itemNumber || !description || !category) {
          throw this.customError?.updateError({
            errorType: this.customError?.ErrorTypes.INVALID_DATA,
            summary: "INVALID_RESULT_VALUE",
            details: `Missing value for item - itemNumber: ${itemNumber}, description: ${description}, category: ${category}`,
          });
        }

        if (itemNumber.includes(" ")) {
          this.customError?.updateError({
            errorType: this.customError?.ErrorTypes.INVALID_DATA,
            summary: "INVALID_ITEM_NUMBER",
            details: `${itemNumber} has a space in the item number, spaces will be removed in the item name for the catalog`,
          });
        }

        if (description.length > 255) {
          throw this.customError?.updateError({
            errorType: this.customError?.ErrorTypes.INVALID_DATA,
            summary: "INVALID_DESCRIPTION",
            details: `${itemNumber} has a description longer than the allowed amount of 255 characters. Please correct.`,
          });
        }
      } catch (err) {
        throw this.customError?.updateError({
          errorType: this.customError?.ErrorTypes.UNHANDLED_ERROR,
          summary: "ERROR_VALIDATING_QUERY_RESULT",
          details: "Error validating query results",
        });
      }
    }

    /**
     * Set item default values
     *
     * @returns {void}
     */
    setCurrentItemDefaultValues() {
      try {
        this.currentItemObj.fileFields["SUPPLIER_ID"] = "SPLN";
        this.currentItemObj.fileFields["FILE_TYPE"] = "FULL";
        this.currentItemObj.fileFields["ORDERABLE"] = "TRUE";
      } catch (err) {
        throw this.customError?.updateError({
          errorType: this.customError?.ErrorTypes.VALUE_NOT_SET,
          summary: "ERROR_SETTING_DEFAULT_VALUES",
          details: `Error setting item default values: ${JSON.stringify(err)}`,
        });
      }
    }

    /**
     * Set item values from query result
     *
     * @param {EDIItemCatalogQueryResult} queryResultObj Query result
     * @returns {void}
     */
    setCurrentItemSpecificValues(queryResultObj) {
      try {
        let {
          itemNumber,
          category,
          imageUrl,
          isReplacementFor,
          deactivate,
          vendorItemName,
          vendorName,
        } = queryResultObj;

        if (itemNumber.includes(" ")) {
          itemNumber = itemNumber.split(" ").join("");
        }

        this.currentItemObj.fileFields["ITEM_NUMBER"] = itemNumber;
        this.currentItemObj.fileFields["CATEGORY_NAME_1"] = category;
        this.currentItemObj.fileFields["CATEGORY_NAME_2"] = category;
        this.currentItemObj.fileFields["IMAGE_URL_1"] = imageUrl ?? "";
        this.currentItemObj.fileFields["MANUFACTURER_NAME"] = vendorName
          ?.split(",")
          .join("");
        this.currentItemObj.fileFields["MANUFACTURER_ITEM_NUMBER"] =
          vendorItemName;

        if (Boolean(this.partner instanceof EDICustomer && this.partner.shouldIncludeDeactivateItem)) {
          this.currentItemObj.fileFields["DEACTIVATE"] = deactivate;
        }

        if (Boolean(this.partner instanceof EDICustomer && this.partner.shouldIncludeIsReplacementFor)) {
          this.currentItemObj.fileFields["IS_REPLACEMENT_FOR"] =
            isReplacementFor;
        }
      } catch (err) {
        throw this.customError?.updateError({
          errorType: this.customError?.ErrorTypes.VALUE_NOT_SET,
          summary: "ERROR_SETTING_SPECIFIC_VALUES",
          details: `Error setting item specific values: ${JSON.stringify(err)}`,
        });
      }
    }

    /**
     * Set item description
     *
     * @param {string} description Item Description
     * @returns {void}
     */
    setDescription(description) {
      try {
        let formattedDescription = description
          .replace(/(\r\n|\n|\r)/gm, "")
          .trim(); //Replace white space and line breaks

        if (
          formattedDescription.includes('"') &&
          formattedDescription.includes(",") //Escape double quotes
        ) {
          formattedDescription = formattedDescription.split('"').join('""');
        }

        if (formattedDescription.includes(",")) {
          //Escape for commas
          formattedDescription = `"${formattedDescription}"`;
        }

        this.currentItemObj.fileFields["NAME"] = formattedDescription;
      } catch (err) {
        throw this.customError?.updateError({
          errorType: this.customError?.ErrorTypes.VALUE_NOT_SET,
          summary: "ERROR_SETTING_DESCRIPTION",
          details: `Error setting description ${err}`,
        });
      }
    }

    /**
     * Set Item unit of measure values
     *
     * @param {EDIItemCatalogQueryResult} queryResultObj Query result
     * @returns {void}
     */
    setUomValues(queryResultObj) {
      const { parentUnitIsEach, unitAbbreviation, itemType } = queryResultObj;
      try {
        if (
          unitAbbreviation.toUpperCase() == "EACH" ||
          unitAbbreviation.length == 2 ||
          itemType == "Kit"
        ) {
          this.currentItemObj.fileFields["UNIT_OF_MEASURE"] =
            unitAbbreviation.length == 2
              ? unitAbbreviation.replace("CS", "CA")
              : "EA";
          this.currentItemObj.fileFields["CONTENT_UNIT_QTY"] = "";
          this.currentItemObj.fileFields["CONTENT_UNIT_SIZE"] = "";

          return;
        }

        const unitAbbreviationArr = unitAbbreviation.split("/");

        /*These lines handle the difference in naming convention of different sub-unit abbreviations.
  
                  Old items are entered into NS under the primary unit type of "Each", (internal id 1) and follow this convention:
                      6 EA / CS
  
                  All other sub-units follow the new naming convention:
                      CS/6EA
                   */

        let abbreviation =
          (parentUnitIsEach
            ? unitAbbreviationArr.pop()
            : unitAbbreviationArr.shift()
          )?.trim() || "";

        const innerUnitDetails = (
          parentUnitIsEach
            ? unitAbbreviationArr[unitAbbreviationArr.length - 1] //2 EA / 2 CS (REMOVED / PR) - pull out 2 CS
            : unitAbbreviationArr[0]
        ) //(REMOVED CS/)3PK/10EA - pull out 3PK
          .trim();

        const contentUnitQuantity = innerUnitDetails.replace(/\D/g, ""); //Remove any letters
        let contentUnitSize = innerUnitDetails.replace(/[0-9]/g, ""); //Remove any integers

        //DSSI requires 'CASE' as CA instead
        abbreviation = abbreviation.replace("CS", "CA");
        contentUnitSize = contentUnitSize.replace("CS", "CA");

        this.currentItemObj.fileFields["UNIT_OF_MEASURE"] = abbreviation;
        this.currentItemObj.fileFields["CONTENT_UNIT_QTY"] =
          contentUnitQuantity.trim();
        this.currentItemObj.fileFields["CONTENT_UNIT_SIZE"] =
          contentUnitSize.trim();
      } catch (err) {
        throw this.customError?.updateError({
          errorType: this.customError?.ErrorTypes.VALUE_NOT_SET,
          summary: "ERROR_SETTING_UOM",
          details: `Error setting UOM values for item. parentUnitIsEach: ${parentUnitIsEach}, unitAbbreviation: ${unitAbbreviation}, itemType: ${itemType}: ${err}`,
        });
      }
    }

    /**
     * Retrieves the string version of the item
     *
     * @returns {string} Item catalog entry as string
     */
    getCurrentItemAsString() {
      try {
        return Object.values(this.currentItemObj.fileFields).join(",");
      } catch (err) {
        throw this.customError?.updateError({
          errorType: this.customError?.ErrorTypes.NO_VALUE_RETURNED,
          summary: "ERROR_GETTING_ITEM_STRING",
          details: `Error getting query string: ${err}`,
        });
      }
    }

    /**
     * Add IS_REPLACEMENT_FOR column based on the Partner (Customer)
     *
     * @returns {void}
     */
    addCustomColumnHeaders() {
      if (
        this.partner instanceof EDICustomer &&
        typeof this.partner.shouldIncludeDeactivateItem === "boolean" &&
        this.partner.shouldIncludeDeactivateItem
      ) {
        this.currentItemObj.fileFields["DEACTIVATE"] = "";
      }

      if (this.partner instanceof EDICustomer && this.partner.shouldIncludeIsReplacementFor) {
        this.currentItemObj.fileFields["IS_REPLACEMENT_FOR"] = "";
      }
    }

    /**
     * Generate the EDI File's header row
     *
     * @returns {string} Stringified header row
     */
    buildHeaderRow() {
      try {
        return Object.keys(this.currentItemObj.fileFields).join(",");
      } catch (err) {
        throw this.customError?.updateError({
          errorType: this.customError?.ErrorTypes.NO_VALUE_RETURNED,
          summary: "ERROR_BUILDING_HEADER_ROW",
          details: `Error getting query string: ${err}`,
        });
      }
    }
  }

  exports.EDIItemCatalogProcessor = EDIItemCatalogProcessor;
});
