/**
 * Copy Employee on SO Transaction Emails
 * 
 * </br><b>Deployed On:</b> Message
 * </br><b>Execution Context:</b> VIEW UE
 * </br><b>Event Type/Mode:</b> CREATE UE
 * </br><b>Entry Points:</b> beforeLoad
 *
 * @NApiVersion 2.1
 * @NScriptType UserEventScript
 * @NAmdConfig /SuiteScripts/config.json
 * 
 * <AUTHOR>
 * @module spl_copy_employee_on_email_ue
 */

define(["N/search","N/log"], function (search, log) {

	//Get Email Address for Sales Order/Item Fulfillment Confirmations from Customer Record
	//param {number} entityId - The customer ID
	//return {string} - The email address
	const getRecipientEmails = (entityId) => {

		log.debug({
			title: "entityId",
			details: entityId
		});

		let entityLookup = search.lookupFields({
			type: search.Type.ENTITY,
			id: entityId,
			columns: ["custentity_spl_order_confirmation_email"]
		});

		return entityLookup.custentity_spl_order_confirmation_email;
	}

	//Add Email Recipients to Message
	//param {string} recipientEmails - The email addresses
	//param {object} newRecord - The record
	//return {void}
	const addEmailRecipients = (recipientEmails, newRecord) => {

		try{

			log.debug({
				title: "recipientEmails",
				details: recipientEmails
			});

			//Split Email Addresses via semi-colon
			let soEmailRecipients = (recipientEmails).split(";");
			
			//Loop and Insert Email from Email Address for Sales Order/Item Fulfillment Confirmations
			for(let i = 0; i < soEmailRecipients.length; i++){
				newRecord.setSublistValue({
					sublistId: "otherrecipientslist",
					fieldId: "email",
					value: soEmailRecipients[i],
					line: i,
				});

				newRecord.setSublistValue({
					sublistId: "otherrecipientslist",
					fieldId: "cc",
					value: true,
					line: i,
				});
			}

		}
		catch(error){

			log.error({
				title: "27adc125-3af8-47a2-bffd-a2dfe5190bb2: Unexpected Error",
				details: error.toString()
			});

		}

	}

	//Get Transaction Type
	//param {number} transactionId - The transaction ID
	//return {string} - The transaction type
	const getTransactionType = (transactionId) => {

		log.debug({
			title: "transactionId",
			details: transactionId
		});

		let transactionLookup = search.lookupFields({
			type: search.Type.TRANSACTION,
			id: transactionId,
			columns: ["type"]
		});

		return transactionLookup.type[0].value;
	}

	
	const beforeLoad = (scriptContext) => {

		//Only run script on create
		if (scriptContext.type == scriptContext.UserEventType.CREATE) {
			
			let newRecord = scriptContext.newRecord;
			let transactionId = newRecord.getValue({ fieldId: "transaction" });

			//Only run if message is from a transaction
			if (transactionId) {

				try{

					const salesOrderTransactionType = "SalesOrd";

					//Get Transaction Type
					let transactionType = getTransactionType(parseInt(transactionId));

					log.debug({
						title: "transactionType",
						details: transactionType
					});

					//Only run script if Email is for Sales Order
					if (transactionType == salesOrderTransactionType) {

						let entityId = newRecord.getValue({ fieldId: "entity" });
						
						//Only run if there is an Entity
						if(entityId){
							//Get Email Address for Sales Order/Item Fulfillment Confirmations from Customer Record
							let recipientEmails = getRecipientEmails(parseInt(entityId));

							log.debug({
								title: "recipientEmails",
								details: recipientEmails
							});

							//Only run if there is Email Address for Sales Order/Item Fulfillment Confirmations
							if(recipientEmails){

								//Add Email Recipients to Message
								addEmailRecipients(recipientEmails,newRecord);

							}
						}
					}

				} 
				catch(error) {

					log.error({
						title: "55eb6fdd-5e33-45c6-bfef-41cc1e8bf29c: Unexpected Error",
						details: error.toString()
					});

				}
			}
		}
	};

	return { beforeLoad };
});
