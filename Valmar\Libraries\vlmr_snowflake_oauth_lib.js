/**
 * 
 * @NApiVersion 2.1
 * @NModuleScope Public
 * 
 * @description Library for Snowflake OAuth token/refresh token management
 * <AUTHOR>
 */

define(["N/https", "N/log", "N/query", "N/record", "N/runtime"], function (
  https,
  log,
  query,
  record,
  runtime
) {

  const SNOWFLAKE_CONFIG = {
    account: "jla60392.east-us-2.azure",
    database: "BT_VIEWS",
    schema: "PUBLIC",
    warehouse: "COMPUTE_WH",
    role: "ACCOUNTADMIN",
    oauth: {
      tokenUrl: "https://jla60392.east-us-2.azure.snowflakecomputing.com/oauth/token-request",
      clientId: "w/xowKES2WsrdZ3oLS6hVMja5A0=", 
      clientSecret: "f1Jagj7EXeDnzc0j0BorAXHmETIE/jWdsTAMZObbrQ4=", 
      customRecordType: "customrecord_snowflake_oauth",
      tokenFieldId: "custrecord_snowflake_oauth_token",
      refreshTokenFieldId: "custrecord_snowflake_refresh_token",
      expirationFieldId: "custrecord_snowflake_token_expiration"
    },
    tokenRecordId: (() => {
      try {
        const script = runtime.getCurrentScript();
        const accountId = runtime.accountId;
        const isSandbox = accountId.includes('_SB') || accountId.toLowerCase().includes('sandbox');

        const parameterName = isSandbox
          ? 'custscript_sandbox_token_record_id'
          : 'custscript_production_token_record_id';

        const tokenRecordId = script.getParameter({
          name: parameterName
        });

        return tokenRecordId;
      } catch (e) {
        log.error('fbaaae5d-825f-4221-9ad4-c0cbe461f59e : Error getting token record ID', e);
        return null;
      }
    })()
  };

  /**
   * Gets a valid OAuth token, refreshing if necessary
   * @returns {string|null} Valid OAuth token or null if unavailable
   */
  function getValidOAuthToken() {
    const validTokenData = getValidStoredOAuthToken();
    
    if (validTokenData && validTokenData.token) {
      return validTokenData.token;
    }
    
    const sqlQuery = `
      SELECT
        ${SNOWFLAKE_CONFIG.oauth.refreshTokenFieldId}
      FROM 
        ${SNOWFLAKE_CONFIG.oauth.customRecordType}
      WHERE
        name = 'Snowflake OAuth Token'
    `;
    
    const queryResults = query.runSuiteQL({
      query: sqlQuery
    }).asMappedResults();

    
    if (queryResults && queryResults.length > 0) {
      const refreshToken = queryResults[0][SNOWFLAKE_CONFIG.oauth.refreshTokenFieldId];

      if(!refreshToken){
        log.error("c8dc60c1-2c4d-47de-91b5-118d4c7aa4bb : Could not find refresh token!", queryResults);
      }

      if (refreshToken) {
        const newTokenData = refreshAndStoreNewToken(refreshToken);
        if (newTokenData && newTokenData.token) {
          return newTokenData.token;
        }
        else{
          log.error("13de7eb5-2f4d-48a6-8a93-9317e22fab04 : Error Refreshing", "Could not get new OAuth token");
        }
      }
    }
    else{
    log.error('fdfcf13c-0dd6-4cc4-925d-bb84304f92e2 : OAuth Token Unavailable', 'Could not obtain a valid OAuth token');
    return null;
    }
  }

  /**
   * Gets the stored OAuth token from the custom record
   * @returns {Object|null} Token data or null if not found
   */
  function getValidStoredOAuthToken() {
    try {
      const sqlQuery = `
        SELECT
          ${SNOWFLAKE_CONFIG.oauth.tokenFieldId},
          ${SNOWFLAKE_CONFIG.oauth.refreshTokenFieldId},
          ${SNOWFLAKE_CONFIG.oauth.expirationFieldId}
        FROM
          ${SNOWFLAKE_CONFIG.oauth.customRecordType}
        WHERE
        TO_CHAR(${SNOWFLAKE_CONFIG.oauth.expirationFieldId}, 'YYYY-MM-DD HH:MI:SS') > TO_CHAR(CURRENT_TIMESTAMP, 'YYYY-MM-DD HH:MI:SS')
      `;

      const queryResults = query.runSuiteQL({
        query: sqlQuery
      }).asMappedResults();

      if (queryResults && queryResults.length > 0) {
        const token = queryResults[0][SNOWFLAKE_CONFIG.oauth.tokenFieldId];
        const refreshToken = queryResults[0][SNOWFLAKE_CONFIG.oauth.refreshTokenFieldId];
        const expiration = queryResults[0][SNOWFLAKE_CONFIG.oauth.expirationFieldId];
        
        log.debug('OAuth Token Retrieved', 'Found valid token expiring on ' + expiration);
        return {
          token: token,
          refreshToken: refreshToken,
          expiration: expiration
        };
      }
      
      return null;
    } catch (e) {
      log.error('d84534d1-8c72-41c5-8712-a9366debc4ce : Error retrieving OAuth token', e);
      return null;
    }
  }

   /**
   * Refreshes the OAuth token using the refresh token
   * @param {string} refreshToken - The refresh token
   * @returns {Object|null} New token data or null if refresh failed
   */
  function refreshAndStoreNewToken(refreshToken) {
    try {
      const headers = {
        "Content-Type": "application/x-www-form-urlencoded",
        "Accept": "application/json"
      };
      
      const body = 
        "grant_type=refresh_token" +
        "&refresh_token=" + encodeURIComponent(refreshToken) +
        "&client_id=" + encodeURIComponent(SNOWFLAKE_CONFIG.oauth.clientId) +
        "&client_secret=" + encodeURIComponent(SNOWFLAKE_CONFIG.oauth.clientSecret);

      const response = https.post({
        url: SNOWFLAKE_CONFIG.oauth.tokenUrl,
        headers: headers,
        body: body
      });

      if (response.code === 200) {
        const tokenData = JSON.parse(response.body);
        
        const expirationDate = new Date();
        expirationDate.setSeconds(expirationDate.getSeconds() + tokenData.expires_in);

        storeOAuthToken({
          token: tokenData.access_token,
          refreshToken: tokenData.refresh_token || refreshToken, // Use new refresh token if provided
          expiration: expirationDate
        });
        
        return {
          token: tokenData.access_token,
          refreshToken: tokenData.refresh_token || refreshToken,
          expiration: expirationDate
        };
      } else {
        log.error('4128dccb-2fa5-4f7b-8845-da091ef3ce7a : OAuth refresh failed', response);
        return null;
      }
    } catch (e) {
      log.error('dbed4aff-ee0e-4d15-9a17-62ab5fcc3e61 : Error refreshing OAuth token', e);
      return null;
    }
  }

  /**
   * Stores the OAuth token in a custom record
   * @param {Object} tokenData - Object containing token, refreshToken and expiration
   */
  function storeOAuthToken(tokenData) {
    try {
      
        record.submitFields({
          type: SNOWFLAKE_CONFIG.oauth.customRecordType,
          id: SNOWFLAKE_CONFIG.tokenRecordId,
          values: {
            [SNOWFLAKE_CONFIG.oauth.tokenFieldId]: tokenData.token,
            [SNOWFLAKE_CONFIG.oauth.refreshTokenFieldId]: tokenData.refreshToken,
            [SNOWFLAKE_CONFIG.oauth.expirationFieldId]: tokenData.expiration
          }
        });
    
    } catch (e) {
      log.error('7c0ffdee-0d58-40eb-9827-2c961822262e : Error storing OAuth token', e);
      throw `6e662e33-f391-4400-af1d-c037ec33a9de : ${e}`;
    }
  }


  /**
   * Checks if a token needs to be refreshed (within specified minutes of expiration)
   * @param {number} minutesBeforeExpiration - How many minutes before expiration to refresh (default: 10)
   * @returns {Object} Object with needsRefresh boolean and token data if available
   */
  function checkTokenRefreshNeeded(minutesBeforeExpiration = 10) {
    try {
      const sqlQuery = `
        SELECT
          ${SNOWFLAKE_CONFIG.oauth.tokenFieldId},
          ${SNOWFLAKE_CONFIG.oauth.refreshTokenFieldId},
          ${SNOWFLAKE_CONFIG.oauth.expirationFieldId}
        FROM
          ${SNOWFLAKE_CONFIG.oauth.customRecordType}
        WHERE
          name = 'Snowflake OAuth Token'
      `;

      const queryResults = query.runSuiteQL({
        query: sqlQuery
      }).asMappedResults();

      if (queryResults && queryResults.length > 0) {
        const tokenData = queryResults[0];
        const expirationStr = tokenData[SNOWFLAKE_CONFIG.oauth.expirationFieldId];
        
        if (expirationStr) {
          const expiration = new Date(expirationStr);
          const now = new Date();
          const minutesUntilExpiration = (expiration - now) / (1000 * 60);
          
          return {
            needsRefresh: minutesUntilExpiration <= minutesBeforeExpiration,
            minutesUntilExpiration: minutesUntilExpiration,
            tokenData: tokenData,
            expiration: expiration
          };
        }
      }
      
      return {
        needsRefresh: true,
        minutesUntilExpiration: 0,
        tokenData: null,
        expiration: null
      };
    } catch (e) {
      log.error('67ee749a-0b57-4ac7-9b2e-550668d76315 : Error checking token refresh status', e);
      return {
        needsRefresh: true,
        minutesUntilExpiration: 0,
        tokenData: null,
        expiration: null,
        error: e.message
      };
    }
  }

  return {
    SNOWFLAKE_CONFIG: SNOWFLAKE_CONFIG,
    getValidStoredOAuthToken: getValidStoredOAuthToken,
    refreshAndStoreNewToken: refreshAndStoreNewToken,
    storeOAuthToken: storeOAuthToken,
    getValidOAuthToken: getValidOAuthToken,
    checkTokenRefreshNeeded: checkTokenRefreshNeeded
  };
});