/**
 * @description Iterates over vendor bills provided by saved search param, loads and saves the bill in order to trigger the UE that creates the Bill Credit and Rip records
 *
 * </br><b>Schedule:</b> Runs on demand
 *
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * @NAmdConfig /SuiteScripts/config.json
 *
 * <AUTHOR>
 * @module brdg_rip_create_records_mr
 */

define(["N/log", "N/runtime", "N/task"], (log, runtime, task) => {
  /**
   * Get input stage of the Map/Reduce script
   * Returns approved bills between the date range of the saved search (as of now, today)
   *
   * @param {import("@hitc/netsuite-types/N/types").EntryPoints.MapReduce.getInputDataContext} context Get input data context
   * @returns {object[]|undefined}
   */
  function getInputData() {
    try {
      const currentScript = runtime.getCurrentScript();
      const vendorBillSavedSearchId = currentScript.getParameter({
        name: "custscript_brdg_vendor_bill_saved_search",
      });

      if (vendorBillSavedSearchId) {
        return {
          type: "search",
          id: vendorBillSavedSearchId,
        };
      } else {
        log.error("Could not find a saved search parameter for this script!");
      }
    } catch (error) {
      log.error("Get Input Data error", error);
    }
  }

  /**
   * Map stage of the Map/Reduce script
   * Load-save the Vendor Bill to trigger the UE that creates the Bill Credit and Rip Records
   *
   * @param {import("@hitc/netsuite-types/N/types").EntryPoints.MapReduce.mapContext} context Map context
   * @returns {void}
   */
  function map(context) {
    const vendorBill = JSON.parse(context.value);
    try {
      if (vendorBill && vendorBill.id) {
        log.debug({
          title: "Processing Vendor Bill",
          details: vendorBill.id,
        });

        const createCreditMrTask = task.create({
          taskType: task.TaskType.MAP_REDUCE,
          scriptId: "customscript_brdg_rip_create_credit_mr",
          params: {
            custscript_rip_create_credit_bill_id: vendorBill.id,
          },
        });

        const taskId = createCreditMrTask.submit();

        log.audit({
          title: "MAP: Submitting BRDG RIP Create Credit Records MR",
          details: taskId,
        });

        context.write(vendorBill.id);
      }
    } catch (e) {
      log.error(
        `c20f4659-f340-47be-869f-c7ef23d0f4e8 : MAP_ERROR_PROCESSING_VENDOR_BILL_ID_${vendorBill?.id}`,
        e
      );
      context.write({
        key: "Processing Error(s):",
        value: `Error with item ${JSON.parse(context.value)}! ${e.message}`,
      });
    }
  }

  /**
   * The summarize stage of the Map/Reduce script.
   *
   * @param {import("@hitc/netsuite-types/N/types").EntryPoints.MapReduce.summarizeContext} summary Summarize context
   * @returns {void}
   */
  function summarize(context) {
    let vendorBillsUpdatedSuccessfullyCounter = 0;
    var vendorBillsUpdatedSuccessfully =
      getVendorBillsProcessedSuccessfully(context);
    var errorMessagesText = getErrorMessages(context);
    logResults();

    function getVendorBillsProcessedSuccessfully(summary) {
      let summaryText = ``;

      summary.output.iterator().each(function (key, value) {
        vendorBillsUpdatedSuccessfullyCounter++;
        summaryText += `${key}, `;

        return true;
      });

      return summaryText;
    }

    function getErrorMessages(summary) {
      let errorText = ``;

      summary.mapSummary.errors.iterator().each(function (key, value) {
        var errorMessage = JSON.parse(value).message;

        errorText += `${errorMessage}, 
				`;
        log.debug("Error Updating Vendor Bills", errorMessage);

        return true;
      });

      return errorText;
    }

    function logResults() {
      if (vendorBillsUpdatedSuccessfully) {
        log.debug({
          title: `Number of Vendor Bills Updated`,
          details: vendorBillsUpdatedSuccessfullyCounter,
        });

        log.debug({
          title: `Vendor Bills Updated Successfully`,
          details: vendorBillsUpdatedSuccessfully,
        });
      }

      if (errorMessagesText) {
        log.error({
          title: "Error Log",
          details: errorMessagesText,
        });
      }
    }
  }

  return {
    getInputData,
    map,
    summarize,
  };
});
