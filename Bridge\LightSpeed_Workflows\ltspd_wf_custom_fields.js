/**
 * @description Sets and validates the custom field value on orders coming from LS.
 * @NApiVersion 2.1
 * @NScriptType Suitelet
 * <AUTHOR>
 * @module ltspd_wf_custom_field
 */

define([
	"require",
	"N/log",
	"../../Classes/vlmd_custom_error_object",
	"../../Classes/brdg_ls_custom_field_object"
], function (require, log) {
	/** @type {import("../../Classes/vlmd_custom_error_object").CustomErrorObject} */

	const CustomErrorObject = require("../../Classes/vlmd_custom_error_object");
	const LSCustomFieldObject = require("../../Classes/brdg_ls_custom_field_object");
	const customErrorObject = new CustomErrorObject();

	function onRequest(context) {
		if (context.request.method === "POST") {
			const parsedJson = JSON.parse(context.request.body),
				useremail = parsedJson["user"]["email"],
				username = parsedJson["user"]["username"],
				storeName = parsedJson["retailer"]["domain_prefix"],
				retailStoreId = parsedJson["retailer_id"],
				lineItemsArr = parsedJson["sale"]["line_items"],
				saleId = parsedJson["sale"]["id"];

			// Exit script if not specific user
			// if (useremail != "<EMAIL>") {
			// 		return;
			// }

			log.debug({ title: "parsedJson", details: parsedJson });

			if (parsedJson["event_type"] == "sale.ready_for_payment") {
				
				const lsCustomFieldObject = new LSCustomFieldObject();

				try {
					let actionObj = {
						actions: [],
					};

					//Defines LTSPD custom field object
					let customFieldObj = [
						{name:"Order Type",id:"order_type",default: "Corporate Type",data: lsCustomFieldObject.CustomFields?.ORDER_TYPE},
						{name:"Order Source",id:"order_source", default: "Retail Order",data: lsCustomFieldObject.CustomFields?.ORDER_SOURCE}
					];
					
					log.debug({
						title: "customFieldObj",
						details: customFieldObj,
					});

					for (let i = 0; i < customFieldObj.length; i++) {

						let customFieldObjInCustomFields = parsedJson["sale"][
							"custom_fields"
						].find((obj) => obj.name == customFieldObj[i]?.id);

						log.debug({
							title: "customFieldObjInCustomFields",
							details: customFieldObjInCustomFields,
						});

						//Sets Custom Field Value
						let customFieldValue =
							customFieldObjInCustomFields &&
							customFieldObjInCustomFields["string_value"];

						log.debug({
							title: "customFieldValue",
							details: customFieldValue,
						});

						let customFieldInPayloadArr = lineItemsArr.filter((itemObj) => {
							const lineItemId = itemObj["product_id"];
							let itemIdFoundInCustomFieldObj =
								customFieldObj[i]?.data[retailStoreId][lineItemId];

							return (
								itemIdFoundInCustomFieldObj == true ||
								itemIdFoundInCustomFieldObj != null
							);
						});

						log.debug({
							title: "customFieldInPayloadArr",
							details: customFieldInPayloadArr,
						});

						let customFieldIdFromLine =
							customFieldInPayloadArr.length > 0
								? customFieldInPayloadArr[0]["product_id"]
								: false;

						log.debug({
							title: "customFieldIdFromLine",
							details: customFieldIdFromLine,
						});

						//Handle setting custom field
						if (
							!customFieldObjInCustomFields ||
							(customFieldIdFromLine &&
								customFieldIdFromLine != customFieldValue)
						) {
							//Validate if more than one custom field was chosen
							if (customFieldInPayloadArr.length > 1) {
								log.audit(
									"MULTIPLE "+ customFieldObj[i]?.name +" CHOSEN",
									`Store: ${storeName} User: ${username}, ${customFieldObj[i]?.name} IDs Chosen: ${customFieldInPayloadArr
										.map((obj) => obj?.id)
										.join(",")}`
								);

								actionObj.actions.push({
									type: "stop",
									title: "Multiple "+customFieldObj[i]?.name+" Chosen",
									message: `Please choose only one ${customFieldObj[i]?.name}.`,
									dismiss_label: "OK",
								});
							}

							//Set to default defined custom field value if none was chosen (customFieldObj[i].default)
							if (!customFieldIdFromLine) {
								log.audit(
									"NO "+customFieldObj[i]?.name+" CHOSEN",
									`Store: ${storeName}, User: ${username}, LightSpeed Sale Id: ${saleId}`
								);

								//Set to the default customfield id for the store
								customFieldIdFromLine = Object.keys(
									customFieldObj[i]?.data[retailStoreId]
								).find(
									(key) =>
										customFieldObj[i]?.data[retailStoreId][key] === customFieldObj[i]?.default
								);

								//Throw error if couldn't find default retail order id for this store
								if (!customFieldIdFromLine) {
									throw customErrorObject.updateError({
										errorType: customErrorObject.ErrorTypes.NO_VALUE_RETURNED,
										summary: "799cceff-952a-454a-8b5a-94acc2a03d9a: " + customFieldObj[i]?.default + " NOT FOUND",
										details: `Default retail order value for store not found. Store: ${storeName}, User: ${username}, LightSpeed Sale Id: ${saleId}`,
									});
								}
							}

							//Add set custom field to actions array
							actionObj.actions.push({
								type: "set_custom_field",
								entity: "sale",
								custom_field_name:  customFieldObj[i]?.id,
								custom_field_value: customFieldIdFromLine,
							});
							

							if(customFieldObj[i]?.id == "order_source"){
								//Set sales rep if is WhatsApp order
								let orderSourceName =
									customFieldObj[i]?.data[retailStoreId][customFieldIdFromLine];
								
								log.debug({
									title: 'orderSourceName',
									details: orderSourceName
								});
								if(orderSourceName){
									orderSourceName.includes("WhatsApp Order - ") &&
									actionObj.actions.push({
										type: "set_custom_field",
										entity: "sale",
										custom_field_name: "sales_rep",
										custom_field_value: orderSourceName.replace(
											"WhatsApp Order - ",
											""
										),
									});
								}
							}
						}

						/*Handle removing order type from line items -
						need to use the "id" field, not the "product_id" field for this*/
						if (
							customFieldIdFromLine &&
							customFieldInPayloadArr[0] &&
							customFieldInPayloadArr[0]["id"]
						) {
							actionObj.actions.push({
								type: "remove_line_item",
								line_item_id: customFieldInPayloadArr[0]["id"],
							});
						}
					}

					
					log.debug("jsonActionObj",JSON.stringify(actionObj));
					//If any actions were set - write the actions JSON object to context
					if (actionObj.actions.length > 0) {
						log.debug(`Sale ID: ${saleId}`, actionObj.actions);

						const jsonActionObj = JSON.stringify(actionObj);
						log.debug("jsonActionObj",JSON.stringify(actionObj));

						context.response.write(jsonActionObj);
					}
				} catch (err) {
					customErrorObject.throwError({
						summaryText: `745b625d-7056-4260-a1b7-cd8b0334e04b : ERROR_PAYING: ${retailStoreId}`,
						error: err,
					});
				}
			}
		}
	}

	return {
		onRequest,
	};
});
