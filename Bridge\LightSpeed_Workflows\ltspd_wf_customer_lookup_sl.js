/**
 * @description SL to show pop-up form to lookup and create customer

 * @NApiVersion 2.1
 * @NScriptType Suitelet
 * @NAmdConfig /SuiteScripts/config.json
 * 
 * <AUTHOR>
 * @module ltspd_wf_customer_lookup_sl
 */

define(["require", "N/log", "N/ui/serverWidget","N/url","N/query"], (require) => {

	const log = require("N/log");
	const serverWidget = require("N/ui/serverWidget");
	const url = require("N/url");
	const query = require("N/query");

    const checkNSCustomerRecord = (queryString) => {
        const sqlQuery = `SELECT customer.id, customer.entityid, customer.phone, customer.companyName, customer.custentity_in8_vend_id
                        FROM customer 
                        WHERE 
                            (
                                phone LIKE '%${queryString}%'
                                OR
                                companyName LIKE '%${queryString}%'
                            )
                            AND
                            isinactive = 'F'
                        `;

        return query.runSuiteQL({
            query: sqlQuery,
        }).asMappedResults();
    }

    const getNSCustomerRecord = (customerId) => {
        const sqlQuery = `SELECT customer.id, customer.entityid, customer.phone, customer.companyName, customer.custentity_in8_vend_id
                        FROM customer 
                        WHERE 
                            customer.id = ${customerId}
                        `;

    //   return query
    //     .runSuiteQL({
    //       query: sqlQuery,
    //     })
    //     .asMappedResults()[0];

        return query.runSuiteQL({
            query: sqlQuery,
        }).asMappedResults();
    }

	let onRequest = (scriptContext) => {
		if (scriptContext.request.method === "GET") {

            let parameter = scriptContext.request.parameters;
            let customerId = parameter.customerid || '';
            let isUpdate = parameter.update || '';
            log.debug({title: 'parameter',details: parameter});
            let queryString = parameter.query || '';

            let customerLookup;

            let hasCustomerRecordObj = {
                ns: false,
                ls: false
            };
            if(isUpdate=='false' && customerId){
                customerLookup = getNSCustomerRecord(customerId);
                log.debug({title: 'getNSCustomerRecord customerLookup',details: customerLookup});

                if(customerLookup.length){
                    hasCustomerRecordObj.ns = true;
                    hasCustomerRecordObj.ls = (customerLookup[0].custentity_in8_vend_id) ? true : false;
                }

                log.debug({title: 'hasCustomerRecordObj',details: hasCustomerRecordObj});
            }
            else if(queryString){
                customerLookup = checkNSCustomerRecord(queryString);
                log.debug({title: 'customerLookup',details: customerLookup});

                
                if(customerLookup.length){
                    hasCustomerRecordObj.ns = true;
                    hasCustomerRecordObj.ls = (customerLookup[0].custentity_in8_vend_id) ? true : false;
                }

                log.debug({title: 'hasCustomerRecordObj',details: hasCustomerRecordObj});
            }

			var form = serverWidget.createForm({
				title: " ",
			});

			var jsField = form.addField({
				id: "customer_lookup_form",
				type: serverWidget.FieldType.INLINEHTML,
				label: "HTML",
			});

            let suiteletURL = url.resolveScript({
				scriptId: "customscript_ltspd_customer_lookup_sl",
				deploymentId: "customdeploy_ltspd_customer_lookup_sl"
			});
            
            if(!queryString && !hasCustomerRecordObj.ns && !customerId){
                jsField.defaultValue = `
                    <html>
                        <body>
                            <!--The below links are stylesheets from LightSpeed. If something is displaying funny, check these first.-->
                            <link href='//vendappcdn.freetls.fastly.net/webregister/css/vendor-9e83fa82a9.css' rel='stylesheet'>
                            <link href='//vendfrontendassets.freetls.fastly.net/fonts/fonts-v9.css' rel='stylesheet'>
                            <style>
                            .active {
                                background-color: green;
                            }
                            </style>
                            <script>
                                window.addEventListener('keydown', function(event) {
                                    // Check if the 'Escape' key was pressed
                                    if (event.key === 'Escape') {
                                        // Optional: Add a confirmation prompt
                                        const confirmClose = confirm("Are you sure you want to close this window?");
                                        if (confirmClose) {
                                            window.close(); // Attempt to close the window
                                        }
                                    }
                                });
                                const searchPhoneNumber = () => {
                                    const queryField = document.getElementById("query");
                                    window.location.href='${suiteletURL}&update=false&query='+queryField.value;
                                }
                                const noCustomer = () => {
                                    const confirmClose = confirm("Are you sure you want to close this window?");
                                    if (confirmClose) {
                                        window.close(); // Attempt to close the window
                                    }
                                }

                                const toggleSearchMode = (type) => {
                                    if(type == 'phone'){
                                        document.getElementById("phone-query").classList.add("active");
                                        document.getElementById("name-query").classList.remove("active");
                                        document.getElementById("query").placeholder = "SEARCH CUSTOMER PHONE";
                                    }
                                    else if(type == 'name'){
                                        document.getElementById("phone-query").classList.remove("active");
                                        document.getElementById("name-query").classList.add("active");
                                        document.getElementById("query").placeholder = "SEARCH CUSTOMER NAME";
                                    }
                                }
                            </script>
                        
                            <div class="vd-modals-container">
                                <div class="vd-overlay vd-overlay--invisible"></div>
                        
                                <div class="vd-dialog" role="dialog">
                                    <div class="vd-modal-container vd-modal--size-medium vd-modal--with-close-button" tabindex="-1">
                                        <div class="vd-modal-inner-container">
                                            <div class="vd-dialog-actions vd-modal--size-medium" style="display: flex;gap: 20px;margin-bottom: 20px;">
                                                <button type="button" class="vd-btn vd-btn--no active" id="phone-query" onclick="toggleSearchMode('phone')" style="width: 50%; ">
                                                   Phone Number
                                                </button>
                                                <button type="button" class="vd-btn vd-btn--no" id="name-query" onclick="toggleSearchMode('name')" style="width: 50%;">
                                                    Name
                                                </button>
                                            </div>
                                            <div class="vd-dialog-header vd-modal--size-medium">
                                                <input style="height:100px; width: 100%; font-size:40px; text-align: center;" type="text" id="query" placeholder="SEARCH CUSTOMER PHONE" value="${queryString}">
                                            </div>
                                            <div class="vd-dialog-actions vd-modal--size-medium" style="display:flex; gap: 20px;">
                                                <div class="vd-btn-group">
                                                    <button type="button" class="vd-btn vd-btn--yes" onClick="noCustomer()">No Customer</button>
                                                </div>
                                                <div class="vd-btn-group">
                                                    <button type="button" class="vd-btn vd-btn--no" onClick="searchPhoneNumber()">Search</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                        
                            </div>
                        
                        </body>
                        
                        </html>
                `;
            }
            else if(!hasCustomerRecordObj.ls || !hasCustomerRecordObj.ns){

                let customerNotFoundMessage = '';

                if(!hasCustomerRecordObj.ls && !hasCustomerRecordObj.ns){
                    customerNotFoundMessage = 'No Customer Found in NetSuite and LightSpeed'
                }
                else if(!hasCustomerRecordObj.ls){
                    customerNotFoundMessage = 'No Customer Found in LightSpeed';
                }
                else if(!hasCustomerRecordObj.ns){
                    customerNotFoundMessage = 'No Customer Found in NetSuite';
                }

                

                jsField.defaultValue = `
                    <html>
                        <body>
                            <!--The below links are stylesheets from LightSpeed. If something is displaying funny, check these first.-->
                            <link href='//vendappcdn.freetls.fastly.net/webregister/css/vendor-9e83fa82a9.css' rel='stylesheet'>
                            <link href='//vendfrontendassets.freetls.fastly.net/fonts/fonts-v9.css' rel='stylesheet'>
                            <script>
                                window.addEventListener('keydown', function(event) {
                                    // Check if the 'Escape' key was pressed
                                    if (event.key === 'Escape') {
                                        // Optional: Add a confirmation prompt
                                        const confirmClose = confirm("Are you sure you want to close this window?");
                                        if (confirmClose) {
                                            window.close(); // Attempt to close the window
                                        }
                                    }
                                });
                                const createNewCustomer = () => {
                                    const lsCustomerPageURL = "https://thevineyardmadison.retail.lightspeed.app/customers?direction=ASC&field=DEFAULT";
                                    const lsNewCustomerButton = document.getElementsByClassName("vd-btn vd-btn--go")[0];
                                    window.location.href= lsCustomerPageURL;
                                    setTimeout(function() {
                                        lsNewCustomerButton.click();
                                    }, 5000);
                                }
                                const goBack = () => {
                                    window.location.href='${suiteletURL}';
                                }

                                const noCustomer = () => {
                                    const confirmClose = confirm("Are you sure you want to close this window?");
                                    if (confirmClose) {
                                        window.close(); // Attempt to close the window
                                    }
                                }

                            </script>
                        
                            <div class="vd-modals-container">
                                <div class="vd-overlay vd-overlay--invisible"></div>
                        
                                <div class="vd-dialog" role="dialog">
                                    <div class="vd-modal-container vd-modal--size-medium vd-modal--with-close-button" tabindex="-1">
                                        <div class="vd-modal-inner-container">
                                            <div class="vd-dialog-header vd-modal--size-medium">
                                                <h1>${customerNotFoundMessage}</h1>
                                            </div>
                                            <div class="vd-dialog-actions vd-modal--size-medium" style="display:flex; gap: 20px;">
                                                <div class="vd-btn-group">
                                                    <button type="button" class="vd-btn vd-btn--no" onClick="noCustomer()">No Customer</button>
                                                </div>
                                                <div class="vd-btn-group">
                                                    <button type="button" class="vd-btn vd-btn--yes" onClick="goBack()">Go Back</button>
                                                </div>
                                                <div class="vd-btn-group">
                                                    <button type="button" class="vd-btn vd-btn--no" onClick="createNewCustomer()">Create New Customer</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                        
                            </div>
                        
                        </body>
                        
                        </html>
                `;
            }
            else {
                let customerName = 'Select Customer';
                let customerPhone = '';

                let selectOptions = '<option value=""></option>';
               
                for(let i = 0; i < customerLookup.length; i++){
                    log.debug('${(customerLookup[i].id == customerId)', customerLookup[i].id +'=='+customerId);
                    selectOptions += `<option value="${customerLookup[i].id}" ${(customerLookup[i].id == customerId) ? 'selected' : ''}>${customerLookup[i].companyname}</option>`;

                    if(customerLookup[i].id == customerId){
                        customerPhone = customerLookup[i].phone;
                        customerName = customerLookup[i].companyname;
                    }
                }

                jsField.defaultValue = `
                    <html>
                        <body>
                            <!--The below links are stylesheets from LightSpeed. If something is displaying funny, check these first.-->
                            <link href='//vendappcdn.freetls.fastly.net/webregister/css/vendor-9e83fa82a9.css' rel='stylesheet'>
                            <link href='//vendfrontendassets.freetls.fastly.net/fonts/fonts-v9.css' rel='stylesheet'>
                            <script>
                                window.addEventListener('keydown', function(event) {
                                    // Check if the 'Escape' key was pressed
                                    if (event.key === 'Escape') {
                                        // Optional: Add a confirmation prompt
                                        const confirmClose = confirm("Are you sure you want to close this window?");
                                        if (confirmClose) {
                                            window.close(); // Attempt to close the window
                                        }
                                    }
                                });
                                const updateCustomerPhoneNumber = () => {
                                    const phoneNumberField = document.getElementById("phoneInput");
                                    window.location.href='${suiteletURL}&id=${customerId}&update=true&customerid=${customerId}&phonenumber='+phoneNumberField.value;
                                    xhttp.open("POST", '${suiteletURL}&id=${customerId}&update=true&customerid=${customerId}&phonenumber='+phoneNumberField.value, true);
                                    xhttp.send();
                                }

                                const goBack = () => {
                                    window.location.href='${suiteletURL}';
                                }

                                const noCustomer = () => {
                                    const confirmClose = confirm("Are you sure you want to close this window?");
                                    if (confirmClose) {
                                        window.close(); // Attempt to close the window
                                    }
                                }

                                const selectCustomer = () => {
                                    const customerSelect = document.getElementById("customer");
                                    window.location.href='${suiteletURL}&update=false&customerid='+customerSelect.value;
                                }
                            </script>
                        
                            <div class="vd-modals-container">
                                <div class="vd-overlay vd-overlay--invisible"></div>
                        
                                <div class="vd-dialog" role="dialog">
                                    <div class="vd-modal-container vd-modal--size-medium vd-modal--with-close-button" tabindex="-1">
                                        <div class="vd-modal-inner-container">
                        
                                            
                                            <div class="vd-dialog-header vd-modal--size-medium">
                                                <h1>${customerName}</h1>
                                                <label for="customer">Customer:</label>
                                                <select name="customer" id="customer" onChange="selectCustomer()">
                                                ${selectOptions}
                                                </select>
                                            </div>
                                            <div class="vd-dialog-header vd-modal--size-medium">
                                                 <input type="hidden" name="customerid" id="customerid" value="${customerId}">
                                                <input style="height:100px; width: 100%; font-size:50px; text-align: center;" type="text" id="phoneInput" placeholder="Enter phone number" value="${customerPhone}">
                                            </div>
                                            <div class="vd-dialog-actions vd-modal--size-medium" style="display:flex; gap: 20px;">
                                                <div class="vd-btn-group">
                                                    <button type="button" class="vd-btn vd-btn--yes" onClick="goBack()">Go Back</button>
                                                </div>
                                                <div class="vd-btn-group">
                                                    <button type="button" class="vd-btn vd-btn--no" onClick="updateCustomerPhoneNumber()">Update Customer</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                        
                            </div>
                        
                        </body>
                    </html>
                `;
            }

			scriptContext.response.writePage(form);
		}
        else if (scriptContext.request.method === "POST") {
			// Handle POST request
            let parameter = scriptContext.request.parameters;
            log.debug({title: 'parameter',details: parameter});
            let updateCustomer = parameter.update;
            let customerid = parameter.customerid || '';
            let phoneNumber = parameter.phonenumber || '';

            if(updateCustomer && customerid && phoneNumber){
                //Update customer in NetSuite
                let updatedCustomerId = record.submitFields({
                    type: record.Type.CUSTOMER,
                    id: customerid,
                    values: {
                        phone: phoneNumber
                    }
                });

                log.debug({
                    title: 'updatedCustomerId',
                    details: updatedCustomerId
                });

                 scriptContext.response.write(JSON.stringify({response: updatedCustomerId}));
            }

		}
	}
	return { onRequest };
});
