/**
 * @description Map reduce script to update the status of payments to "Waiting for Response"
 *              This script is called by the vlmd_corepay_submit_file_mr script.
 *
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 *
 * <AUTHOR>
 */

define(["require", "N/log", "N/runtime", "N/record"], (
  /** @type {any} */ require
) => {
  const log = require("N/log");
  const runtime = require("N/runtime");
  const record = require("N/record");

  /**
   * Return all payments to be updated
   *
   * @function getInputData
   * @returns {Array|undefined} Array of payment objects to process
   */
  function getInputData() {
    try {
      const currentScript = runtime.getCurrentScript();
      const processedPayments = JSON.parse(
        currentScript.getParameter({
          name: "custscript_processed_payments",
        })
      );

      if (!processedPayments || processedPayments?.length === 0) {
        log.error("GET_INPUT_DATA_ERROR", "No payments parameter provided");
        return [];
      }

      log.audit(
        "Processing Payments",
        `Found ${processedPayments.length} payments to update`
      );

      return processedPayments;
    } catch (err) {
      log.error("GET_INPUT_DATA_ERROR", err);
      throw err;
    }
  }

  /**
   * Update the payment record to mark it as processed
   *
   * @function reduce
   * @param {Object} context - The reduce context
   * @param {Array} context.values - Values passed in
   * @param {Function} context.write - Function to write output key-value pairs
   * @returns {void}
   */
  function reduce(context) {
    try {
      const paymentData = JSON.parse(context.values[0]);

      if (
        !paymentData ||
        !paymentData.internalId ||
        !paymentData.paymentOrCredit
      ) {
        context.write("Error", "Invalid payment data structure");
        return;
      }

      const { internalId, paymentOrCredit } = paymentData;
      record.submitFields({
        type:
          paymentOrCredit == "VendPymt"
            ? record.Type.VENDOR_PAYMENT
            : record.Type.VENDOR_CREDIT,
        id: internalId,
        values: {
          custbody_vlmd_corepay_status: "2", //Waiting for Response
        },
      });

      context.write(internalId, "Successfully Processed");
    } catch (err) {
      context.write("Error", JSON.stringify(err));
    }
  }

  /**
   * Log summary of processing details
   *
   * @function summarize
   * @param {Object} context - The summarize context
   * @param {Object} context.output - Output from reduce stage
   * @param {Object} context.reduceSummary - Summary of reduce stage
   * @returns {void}
   */
  function summarize(context) {
    let successCount = 0;
    let errorCount = 0;

    context.output.iterator().each(function (key, value) {
      if (key !== "Error") {
        successCount++;
      } else {
        errorCount++;
      }

      return true;
    });

    log.audit(
      "Processing Summary",
      `Successfully processed ${successCount} payments. Errors: ${errorCount}`
    );

    if (context.mapSummary && context.mapSummary.errors.iterator) {
      context.mapSummary.errors.iterator().each(function (key, error) {
        log.error(`Error processing payment ${key}`, error);
        return true;
      });
    }
  }

  return {
    getInputData,
    reduce,
    summarize,
  };
});
