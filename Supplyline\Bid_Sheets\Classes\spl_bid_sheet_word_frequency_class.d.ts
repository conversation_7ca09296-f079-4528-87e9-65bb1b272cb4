/**
 * Interface and type definitions for the WordFrequencyAnalyzer class
 *
 * <AUTHOR>
 */

import { TextMatching } from "./spl_bid_sheet_text_matching_class";

/**
 * Keyword object with score and frequency information
 */
export interface Keyword {
    /** The keyword text */
    keyword: string;
    /** The importance score (TF-IDF) */
    score?: number;
    /** Total occurrences of the word */
    count?: number;
    /** Number of items containing this word */
    docFrequency?: number;
    /** Inverse document frequency */
    idf?: number;
    /** How many times this word appears in the corpus (alias for count) */
    frequency?: number;
}

/**
 * Word frequency map entry
 */
export interface WordFrequencyEntry {
    /** Total occurrences of the word */
    count: number;
    /** Number of items containing this word */
    docFrequency: number;
    /** Inverse document frequency (calculated) */
    idf: number;
}

/**
 * Item match result with score and matched keywords
 */
export interface ItemMatchResult {
    /** The matched item */
    item: any;
    /** Normalized match score (0-1) */
    score: number;
    /** List of keywords that matched */
    matchedKeywords: string[];
}

/**
 * Constructor options for WordFrequencyAnalyzer class
 */
export interface WordFrequencyAnalyzerOptions {
    /** TextMatching instance for text processing */
    textProcessor?: TextMatching | null;
    /** NetSuite file module for file operations */
    fileModule?: any | null;
    /** Pre-loaded word frequency map */
    wordFrequencyMap?: Record<string, WordFrequencyEntry> | null;
}

/**
 * Options for keyword extraction
 */
export interface KeywordExtractionOptions {
    /** Maximum number of keywords to extract */
    maxKeywords?: number;
    /** Minimum document frequency for keywords */
    minDocFrequency?: number;
    /** Sort by count (true) or doc frequency (false) */
    sortByCount?: boolean;
}

/**
 * Options for finding items by keywords
 */
export interface FindItemsByKeywordsOptions {
    /** Maximum number of results */
    maxResults?: number;
    /** Minimum score for matches */
    minScore?: number;
    /** Minimum importance score for keywords */
    keywordImportanceThreshold?: number;
    /** Maximum number of keywords to use for matching */
    maxKeywords?: number;
    /** Word frequency map for filtering keywords */
    wordFrequencyMap?: Record<string, WordFrequencyEntry> | null;
    /** Item index for faster searching. Keys are keywords, values are arrays of item IDs */
    itemIndex?: Record<string, string[]> | null;
}

/**
 * File save result
 */
export interface FileSaveResult {
    /** ID of the saved file */
    fileId: string | number;
    /** Name of the saved file */
    fileName: string;
}

/**
 * File load result
 */
export interface FileLoadResult {
    /** The loaded word frequency map */
    wordFrequencyMap: Record<string, WordFrequencyEntry>;
    /** When the map was last updated */
    lastUpdated: Date;
}

/**
 * WordFrequencyAnalyzer class for analyzing keyword frequency and importance
 */
export interface WordFrequencyAnalyzer {
    /** Constructor */
    new(options?: WordFrequencyAnalyzerOptions): WordFrequencyAnalyzer;



    /** TextMatching instance for text processing */
    textProcessor: TextMatching;

    /** NetSuite file module for file operations */
    file: any;

    /** Word frequency map */
    wordFrequencyMap: Record<string, WordFrequencyEntry>;

    /** When the word frequency map was last updated */
    lastUpdated: Date | null;

    /**
     * Builds a word frequency map from all items
     * @param items - Array of items to analyze
     * @returns Word frequency map
     */
    buildWordFrequencyMap(items: any[]): Record<string, WordFrequencyEntry>;

    /**
     * Extracts potential keywords from a description based on frequency map
     * @param description - Customer description
     * @param options - Options for keyword extraction
     * @returns Extracted keywords with frequency information
     */
    extractKeywords(description: string, options?: KeywordExtractionOptions): Keyword[];

    /**
     * Finds items matching the customer description using keyword frequency analysis
     * @param customerDescription - Customer description to match
     * @param items - Items to search
     * @param options - Search options
     * @returns Matching items with scores
     */
    findItemsByKeywords(customerDescription: string, items: any[], options?: FindItemsByKeywordsOptions): ItemMatchResult[];

    /**
     * Saves word frequency map to file
     * @param fileName - File name
     * @param folderId - Folder ID
     * @returns Result with file ID
     */
    saveWordFrequencyMapToFile(fileName?: string, folderId?: number): FileSaveResult | null;

    /**
     * Loads word frequency map from file
     * @param fileIdOrPath - File ID or path
     * @returns Loaded word frequency map
     */
    loadWordFrequencyMapFromFile(fileIdOrPath: string | number): FileLoadResult | null;
}

/**
 * Module interface for WordFrequencyAnalyzer
 */
export interface WordFrequencyAnalyzerModule {
    /**
     * The WordFrequencyAnalyzer class constructor for creating new instances
     */
    WordFrequencyAnalyzer: WordFrequencyAnalyzer;
}

/**
 * Default export is the WordFrequencyAnalyzer constructor
 */
declare const WordFrequencyAnalyzer: WordFrequencyAnalyzer;

export default WordFrequencyAnalyzer;
