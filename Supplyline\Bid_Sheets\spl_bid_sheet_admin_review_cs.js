/**
 * @NApiVersion 2.1
 * @NScriptType ClientScript
 *
 * @description Client script for bid sheet admin review suitelet
 *
 * <AUTHOR>
 * @module spl_bid_sheet_admin_review_cs
 */
define([
'N/runtime',
'./Classes/Text_Matching/spl_bid_sheet_text_matching_class',
'./Classes/Text_Matching/spl_bid_sheet_word_frequency_class',
'./Classes/Text_Matching/spl_bid_sheet_item_repository_class',
], (
runtime,
TextMatching,
WordFrequencyAnalyzer,
ItemRepository
) => {

    /**
     * Function to be executed after page is initialized and ready
     *
     * @param {Object} context
     */
    function pageInit(context) {
        try {

            window.textMatcherClass = TextMatching;
            window.wordFrequencyAnalyzerClass = WordFrequencyAnalyzer;
            window.itemRepositoryClass = ItemRepository;

        } catch (error) {
            console.error('Error initializing TextMatching class:', error);
        }
    }

    return {
        pageInit: pageInit
    };
});



