/**
* @description Creates <PERSON><PERSON> and <PERSON>end<PERSON> Bill for all flagged vendor bills created via Bill Capture
*              Deletes Bill Capture Vendor Bills Afterwards 

* </br><b>Schedule:</b> Runs every night at 12:30 AM
*
* @NApiVersion 2.1
* @NScriptType MapReduceScript
*
* <AUTHOR>
* @module brdg_bill_capture_completion_mr
 */
define([
	"require",
	"N/record",
	"N/query",
	"../../Classes/vlmd_custom_error_object",
	"../../Classes/vlmd_mr_summary_handling",
	"./brdg_bill_capture_completion_lib",
],(require) => {
        const record = require("N/record");
		const query = require("N/query");

        const CustomErrorObject = require("../../Classes/vlmd_custom_error_object");
        const customErrorObject = new CustomErrorObject();

		const billCaptureCompletionLib = require("./brdg_bill_capture_completion_lib");
		
	function getInputData() {
        try {
			// Query for vendor bills created via Bill Capture that are pending approval
			const billCaptureQuery = `
				SELECT 
					id,
					tranid
				FROM 
					transaction 
				WHERE 
					BUILTIN.DF(status) LIKE '%Pending Approval' AND
					custbody_brdg_bill_capture = 'T' AND
					BUILTIN.DF(type) = 'Bill'
			`;
			const billCaptureVendorBill = query.runSuiteQL({
				query: billCaptureQuery,
			}).asMappedResults();

			// If no bills found, return empty array
			if (!billCaptureVendorBill.length) return [];

			// Get transaction IDs for duplicate detection
			const tranIds = billCaptureVendorBill.map(bill => bill.tranid).join(`','`);

			// Find bills with exactly one instance (no duplicates)
			const billCountQuery = /*sql*/`
				SELECT 
					tranid
				FROM 
					transaction 
				WHERE 
					tranid IN ('${tranIds}') AND
					BUILTIN.DF(type) = 'Bill'
				GROUP BY
					tranid
				HAVING 
					COUNT(id) = 1
			`;
			const uniqueBillResults = query.runSuiteQL({
				query: billCountQuery,
			}).asMappedResults();

			const uniqueTranIds = uniqueBillResults.map(result => result.tranid);

			// Only process bills that have exactly one instance
			return billCaptureVendorBill
				.filter(bill => uniqueTranIds.includes(bill.tranid))
				.map(bill => bill.id);
		} catch (err) {
			customErrorObject.throwError({
				summaryText: `b3c9e473-74ae-460e-bbb3-1852c7cd30d5 : GET_INPUT_DATA_ERROR`,
				error: err,
			});
		}
	}
	
	function reduce(context) {
		const originalVendorBillId = context.values[0];
		let originalVendorBill;
		let poId;
		let newVendorBillId;

		try {
			// Load original vendor bill
			originalVendorBill = record.load({
				type: record.Type.VENDOR_BILL,
				id: originalVendorBillId,
			});

			// Step 1: Create Purchase Order from Vendor Bill
			poId = billCaptureCompletionLib.createPurchaseOrderFromVendorBill(originalVendorBill, originalVendorBillId);

			log.audit('PO Creation Success', {
				originalVendorBillId: originalVendorBillId,
				purchaseOrderId: poId
			});

			// Step 2: Create new Vendor Bill from the Purchase Order
			try {
				newVendorBillId = billCaptureCompletionLib.createVendorBillFromPurchaseOrder(poId, originalVendorBill, originalVendorBillId);

				// Validate that a valid ID was returned
				if (!newVendorBillId || newVendorBillId === null || newVendorBillId === undefined) {
					throw new Error('Vendor bill creation returned null or invalid ID');
				}

				log.audit('Vendor Bill Creation Success', {
					originalVendorBillId: originalVendorBillId,
					purchaseOrderId: poId,
					newVendorBillId: newVendorBillId
				});
			} catch (billError) {
				// If bill creation fails, revert the PO that was created to avoid orphaned records
				billCaptureCompletionLib.revertProcess(poId);

				// Re-throw the original bill creation error
				customErrorObject.throwError({
					summaryText: `aabdead3-2dcc-425b-88fe-52b58d91db76 : VENDOR_BILL_CREATION_FAILED`,
					error: billError,
				});
			}

			// Step 3: Mark original vendor bill for cancellation (only if both PO and bill were created successfully)
			try {
				billCaptureCompletionLib.markOriginalBillForCancellation(originalVendorBillId);
			} catch (cancellationError) {
				// If cancellation fails, revert both PO and new vendor bill
				billCaptureCompletionLib.revertProcess(poId, newVendorBillId);

				customErrorObject.throwError({
					summaryText: `24f9ce83-7189-4ac9-8887-9c83020f4e31 : FAILED_TO_MARK_ORIGINAL_BILL_FOR_CANCELLATION`,
					error: cancellationError,
				});
			}

			log.audit('Bill Capture Completion Success', {
				originalVendorBillId: originalVendorBillId,
				purchaseOrderId: poId,
				newVendorBillId: newVendorBillId
			});

		} catch (err) {
			customErrorObject.throwError({
				summaryText: `aabdead3-2dcc-425b-88fe-52b58d91db76 : BILL_CAPTURE_COMPLETION_ERROR`,
				error: err,
			});
		}
	}

	function summarize(context) {
		const StageHandling = require("../../Classes/vlmd_mr_summary_handling");
		const stageHandling = new StageHandling(context);
  
		stageHandling.printScriptProcessingSummary();
  
		const errorObject = stageHandling.printErrors({
			groupErrors: true,
		});

		if(errorObject.errorArr.length > 0) {
			// Throw error to trigger email notification
			throw new Error(errorObject.errorsMessage);
		}
	}

	return {
		getInputData,
		reduce,
		summarize,
	};
});
