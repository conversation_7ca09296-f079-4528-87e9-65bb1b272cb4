/**
 * @description MapReduce script to generate JSON files for items, item indexes, and word frequency
 *
 * Schedule: on-demand
 *
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 *
 * <AUTHOR>
 * @module spl_bid_sheet_data_generator_mr
 */
define(["require",
    "N/file",
    "N/query",
    "N/runtime",
    "./Classes/Text_Matching/spl_bid_sheet_text_matching_class",
    "./Classes/Text_Matching/spl_bid_sheet_item_repository_class",
    "./Classes/Text_Matching/spl_bid_sheet_word_frequency_class",
    "../../Classes/vlmd_custom_error_object"
], (require) => {

    const file = require('N/file');
    const query = require('N/query');
    const runtime = require('N/runtime');

    const TextMatching = require('./Classes/Text_Matching/spl_bid_sheet_text_matching_class');
    const ItemRepository = require('./Classes/Text_Matching/spl_bid_sheet_item_repository_class');
    const WordFrequencyAnalyzer = require('./Classes/Text_Matching/spl_bid_sheet_word_frequency_class');
    const CustomErrorObject = require("../../Classes/vlmd_custom_error_object");

    const customErrorObject = new CustomErrorObject();

    /**
     * Defines the function that is executed at the beginning of the map/reduce process
     * @param {Object} context - Context object
     */
    function getInputData() {
        try {
            log.audit({
                title: 'Data Generator',
                details: 'Starting data generation process - Multiple map executions mode'
            });

            // Initialize the item repository to load items from database
            const itemRepository = new ItemRepository({
                queryModule: query,
                fileModule: file
            });

            // Load all items from the database
            const items = itemRepository.loadItemsFromDatabase();

            log.audit({
                title: 'Items Loaded',
                details: `Loaded ${items.length} items for processing`
            });

            // Return each item as a separate work unit for individual map processing
            return items.map((item, index) => ({
                type: 'process_item',
                item: item,
                itemIndex: index,
                totalItems: items.length
            }));
        } catch (err) {
            customErrorObject.throwError({
                summaryText: 'Error in getInputData',
                error: err
            });
        }
    }

    /**
     * Defines the function that is executed when the map entry point is triggered
     * @param {Object} context - Context object
     */
    function map(context) {
        try {
            // Parse the input data from getInputData
            const inputData = JSON.parse(context.value);
            const { type, item, itemIndex, totalItems } = inputData;

            // Log progress periodically (every 1000 items)
            if (itemIndex % 1000 === 0) {
                log.audit({
                    title: 'Map Progress',
                    details: `Processing item ${itemIndex + 1} of ${totalItems}: ${item.itemId}`
                });
            }

            // Initialize the text processor for token generation
            const textProcessor = new TextMatching();

            // Combine all relevant text fields for this item
            const itemText = [
                item.itemId,
                item.displayName,
                item.vendorCode,
                item.category
            ].filter(Boolean).join(' ').toLowerCase();

            // Generate tokens for this item
            const tokens = textProcessor.generateTokens({
                stringToTokenize: itemText,
                removeFillerWords: true
            });

            // Create unique tokens set to avoid counting repeated words in same item
            const uniqueTokens = [...new Set(tokens)];

            // Write individual item data and its tokens for aggregation in reduce
            context.write({
                key: 'item_data',
                value: {
                    item: item,
                    tokens: tokens,
                    uniqueTokens: uniqueTokens,
                    itemIndex: itemIndex
                }
            });

        } catch (err) {
            customErrorObject.throwError({
                summaryText: 'Error in map function',
                error: err
            });
        }
    }

    /**
     * Defines the function that is executed when the reduce entry point is triggered
     * @param {Object} context - Context object
     */
    function reduce(context) {
        try {
            const FOLDER_ID = runtime.getCurrentScript().getParameter({ name: 'custscript_spl_bid_sheet_data_fldr_id' });

            log.audit({
                title: 'Reduce Stage',
                details: `Aggregating data from ${context.values.length} map executions`
            });

            // Aggregate all items and tokens from map executions
            const allItems = [];
            const allTokensData = [];

            // Process each result from map executions
            context.values.forEach(value => {
                const mapResult = JSON.parse(value);
                allItems.push(mapResult.item);
                allTokensData.push({
                    tokens: mapResult.tokens,
                    uniqueTokens: mapResult.uniqueTokens,
                    itemIndex: mapResult.itemIndex
                });
            });

            log.audit({
                title: 'Data Aggregated',
                details: `Processed ${allItems.length} items, building indexes and word frequency`
            });

            // Initialize the item repository with aggregated items
            const itemRepository = new ItemRepository({
                fileModule: file,
                items: allItems
            });

            // Create item index from aggregated items
            itemRepository.createItemsIndex();

            // Build word frequency map from all tokens
            const wordFrequencyMap = {};
            const totalItems = allItems.length;

            // Process all tokens to build frequency map
            allTokensData.forEach(tokenData => {
                const { uniqueTokens, tokens } = tokenData;

                // Update document frequency (number of items containing each word)
                uniqueTokens.forEach(token => {
                    if (!wordFrequencyMap[token]) {
                        wordFrequencyMap[token] = {
                            count: 0,          // Total occurrences
                            docFrequency: 0,   // Number of items containing this word
                            idf: 0             // Inverse document frequency (calculated later)
                        };
                    }

                    wordFrequencyMap[token].docFrequency++;
                    wordFrequencyMap[token].count += tokens.filter(t => t === token).length;
                });
            });

            // Calculate IDF (Inverse Document Frequency) for each word
            Object.keys(wordFrequencyMap).forEach(word => {
                const docFrequency = wordFrequencyMap[word].docFrequency;
                // IDF formula: log(totalDocuments / documentsContainingTerm)
                wordFrequencyMap[word].idf = Math.log(totalItems / docFrequency);
            });

            log.audit({
                title: 'Word Frequency Built',
                details: `Generated frequency data for ${Object.keys(wordFrequencyMap).length} unique tokens`
            });

            // Save items to file
            const itemsFileId = itemRepository.saveItemsToFile('bid_sheet_items.json', FOLDER_ID).fileId;

            // Save item index to file
            const itemIndexFileId = itemRepository.saveItemIndexToFile('bid_sheet_item_index.json', FOLDER_ID).fileId;

            // Initialize the word frequency analyzer with built frequency map
            const wordFrequencyAnalyzer = new WordFrequencyAnalyzer({
                textProcessor: new TextMatching(),
                fileModule: file,
                wordFrequencyMap: wordFrequencyMap
            });

            // Save word frequency map to file
            const wordFrequencyFileId = wordFrequencyAnalyzer.saveWordFrequencyMapToFile('bid_sheet_word_frequency.json', FOLDER_ID).fileId;

            // Write the file IDs to context.write for the summarize stage
            context.write({
                key: 'fileIds',
                value: {
                    itemsFileId: itemsFileId,
                    itemIndexFileId: itemIndexFileId,
                    wordFrequencyFileId: wordFrequencyFileId
                }
            });
        } catch (err) {
            customErrorObject.throwError({
                summaryText: 'Error in reduce function',
                error: err
            });
        }
    }

    /**
     * Defines the function that is executed when the summarize entry point is triggered
     * @param {Object} context - Context object
     */
    function summarize(context) {
        try {
            log.audit({
                title: 'Summarize Stage',
                details: 'Data generation process completed'
            });

            // Log any errors that occurred during the map/reduce process
            if (context.inputSummary.error) {
                log.error({
                    title: 'Input Error',
                    details: context.inputSummary.error
                });
            }

            // Log map errors
            context.mapSummary.errors.iterator().each(function(key, error) {
                log.error({
                    title: 'Map Error for key: ' + key,
                    details: error
                });
                return true;
            });

            // Log reduce errors
            context.reduceSummary.errors.iterator().each(function(key, error) {
                log.error({
                    title: 'Reduce Error for key: ' + key,
                    details: error
                });
                return true;
            });

            // Log the file IDs
            let fileIds = {};
            context.output.iterator().each(function(_, value) {
                fileIds = JSON.parse(value);
                log.audit({
                    title: 'Generated Files',
                    details: JSON.stringify(fileIds)
                });
                return true;
            });
        } catch (err) {
            log.error({
                title: 'Error in summarize function',
                details: err
            });
        }
    }





    return {
        getInputData: getInputData,
        map: map,
        reduce: reduce,
        summarize: summarize
    };
});
