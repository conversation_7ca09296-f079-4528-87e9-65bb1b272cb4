/**
 * @description Creates RIP Accrual and Tier-Quantity records
 *
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * @NAmdConfig /SuiteScripts/config.json
 *
 * <AUTHOR> <<EMAIL>>
 * @module brdg_rip_create_credit_records_mr
 */

define([
  "require",
  "N/email",
  "N/format",
  "N/log",
  "N/query",
  "N/record",
  "N/runtime",
  "../../../Classes/vlmd_custom_error_object",
  "../../../Classes/vlmd_mr_summary_handling",
  "../../../Helper_Libraries/vlmd_record_module_helper_lib",
  "../../Libraries/brdg_helper_functions_lib",
  "../Libraries/brdg_rip_records_helper_lib",
], (/** @type {any} */ require) => {
  const email = require("N/email");
  const format = require("N/format");
  const log = require("N/log");
  const query = require("N/query");
  const record = require("N/record");
  const runtime = require("N/runtime");

  /** @type {import ("../../../Classes/vlmd_custom_error_object").CustomErrorObject} */
  const CustomErrorObject = require("../../../Classes/vlmd_custom_error_object");
  const customError = new CustomErrorObject();

  /** @type {import ("../../../Classes/vlmd_mr_summary_handling").StageHandling} */
  const StageHandling = require("../../../Classes/vlmd_mr_summary_handling");

  /** @type {import ("../../../Helper_Libraries/vlmd_record_module_helper_lib")} */
  const recordHelperLib = require("../../../Helper_Libraries/vlmd_record_module_helper_lib");

  /** @type {import ("../Libraries/brdg_rip_records_helper_lib")} */
  const createRipRecordLib = require("../Libraries/brdg_rip_records_helper_lib");

  /** @type {import ("../../Libraries/brdg_helper_functions_lib")} */
  const bridgeHelperFunctionsLib = require("../../Libraries/brdg_helper_functions_lib");

  /**
   * @typedef {import("../../../Classes/brdg_rip").BestTierLevel} BestTierLevel
   * @typedef {import("../../../Classes/brdg_rip").MaximizedRipObject} MaximizedRipObject
   * @typedef {import("../../../Classes/brdg_rip").RipItem} RipItem
   * @typedef {import("../../../Classes/brdg_rip").RipObject} RipObject
   * @typedef {import("../../../Classes/brdg_rip").RipObjectSummary} RipObjectSummary
   * @typedef {import("../../../Classes/brdg_rip").RipQuantity} RipQuantity
   * @typedef {import("../../../Classes/brdg_rip").TierLevel} TierLevel
   * @typedef {import("../../../Classes/brdg_rip").TierQuantityObj} TierQuantityObj
   */

  const Unit = {
    BOTTLE: 1,
    CASE: 2,
    GROUP: 3,
  };

  const RipVendor = {
    ALLIED: 13694,
    FEDWAY: 13423,
    ROYAL1: 13410,
    ROYAL2: 13366,
  };

  /**
   * @description RIP item ID associated per Vendor
   * @type {{[key:string]: string}}
   */
  const RipVendorItem = {
    [RipVendor.ALLIED]: "44228",
    [RipVendor.FEDWAY]: "74239",
    [RipVendor.ROYAL1]: "74244",
    [RipVendor.ROYAL2]: "74244",
  };

  /**
   * Gets the name and message from the error object
   *
   * @param {any} obj Error object
   * @returns {any} Error details
   */
  function getErrorDetails(obj) {
    return obj && obj.name && obj.message ? `${obj.name}: ${obj.message}` : obj;
  }

  /**
   * runSuiteQL wrapper
   *
   * @param {string} sqlQuery SuiteQL string
   * @returns {query.QueryResultMap | null} Query results
   */
  function runSqlQuery(sqlQuery) {
    try {
      const sqlResults = query
        .runSuiteQL({
          query: sqlQuery,
        })
        .asMappedResults()[0];

      if (!sqlResults) {
        return null;
      }

      return sqlResults;
    } catch (/** @type {any} */ err) {
      throw customError.updateError({
        errorType: customError.ErrorTypes.EVAL_ERROR,
        summary: "13396441-1d5a-4a25-b558-38e560011178: ERROR_RUNNING_SQL",
        details: getErrorDetails(err),
      });
    }
  }

  /**
   * Return an item object with quantities and units of measure.
   * The object will be updated with Rip details.
   *
   * @param {import("N/record").Record} vendorBill NetSuite Vendor Bill record
   * @param {number} lineCount Sublist line count
   * @returns {RipObject[]} Item object with Rip details
   */
  function getItemsInfo(vendorBill, lineCount) {
    const /** @type {RipObject[]} */ itemObjArr = [];
    try {
      for (var i = 0; i < lineCount; i++) {
        const isComponentItem = vendorBill.getSublistValue({
          sublistId: "item",
          fieldId: "ingroup",
          line: i,
        });

        if (isComponentItem == "T") {
          continue;
        }

        const itemId = Number(
          vendorBill.getSublistValue({
            sublistId: "item",
            fieldId: "item",
            line: i,
          })
        );

        const itemQuantity = Number(
          vendorBill.getSublistValue({
            sublistId: "item",
            fieldId: "quantity",
            line: i,
          }) || 0
        );

        const itemType =
          vendorBill
            .getSublistValue({
              sublistId: "item",
              fieldId: "itemtype",
              line: i,
            })
            ?.toString() || "";

        // When item is a group item - pull the unit from the component which is on the next line
        const itemUnits =
          vendorBill
            .getSublistValue({
              sublistId: "item",
              fieldId: "units_display",
              line: i,
            })
            ?.toString() || "";

        // If the purchase unit includes CS, it first gets a CASE rebate check, otherwise a bottle rebate check
        const unitsType =
          itemType == "Group"
            ? Unit.GROUP
            : itemUnits.includes("CS")
            ? Unit.CASE
            : Unit.BOTTLE;

        const convertedItemQuantity =
          unitsType === Unit.CASE
            ? Number(
                vendorBill.getSublistValue({
                  sublistId: "item",
                  fieldId: "unitconversionrate",
                  line: i,
                })
              ) * itemQuantity
            : 0;

        itemObjArr.push({
          itemId,
          itemQuantity,
          convertedItemQuantity,
          unitsType,
          rebateDetailArr: [],
          countAs: 1,
        });
      }
      return itemObjArr;
    } catch (/** @type {any} */ err) {
      throw customError.updateError({
        errorType: customError.ErrorTypes.RECORD_NOT_LOADED,
        summary:
          "8888705a-4ff7-4a52-98c2-f46ec3a23e00: ERROR_GETTING_ITEMS_OBJECTS",
        details: getErrorDetails(err),
      });
    }
  }

  /**
   * Sets the rebateDetailArr property of the item object to the QueryResultMap[].
   * Returns a boolean based on the availability of a rip.
   *
   * @param {RipObject} itemObj Item object containing Rip details
   * @param {Date} billDate Vendor Bill Date
   * @param {number} billVendor Vendor ID assigned to the Bill
   * @returns {boolean} Returns true if there is an available rip
   */
  function checkIfAvailRip(itemObj, billDate, billVendor) {
    const sqlQuery = `
      SELECT
        tg.id tiergroup,
        cr.id rebatedetail,
        cr.custrecord_rip_code ripcode,
        NVL(ci.custrecord_agreement_detail_item_count,1) countas
      FROM
        CUSTOMRECORD_REBATE_AGREEMENT_DETAIL cr
        JOIN customrecord_rebate_agreement ca ON cr.custrecord_rebate_parent = ca.id
        JOIN customrecord_rebate_tier_group tg ON cr.custrecord_tier_group = tg.id
        LEFT JOIN customrecord_rebate_agreement_detail_itm ci ON ci.custrecord_agreement_detail_item = ${itemObj.itemId}
          AND ci.custrecord_agreement_detail = cr.id
      WHERE
        BUILTIN.MNFILTER(
          cr.custrecord_rebate_items_included,
          'MN_INCLUDE',
          '',
          'FALSE',
          NULL,
          ${itemObj.itemId}
        ) = 'T'
        AND TO_DATE(?, 'MM/DD/YYYY') BETWEEN ca.custrecord_rebate_start_date
        AND ca.custrecord_rebate_end_date
        AND ca.custrecord_rebate_vendor = ${billVendor}
        AND cr.isinactive = 'F'
        AND ca.isinactive = 'F'
        AND tg.isinactive = 'F'
        AND ci.isinactive = 'F'
    `;

    const resultIterator = query
      .runSuiteQL({
        query: sqlQuery,
        // @ts-ignore Type 'Date' is not assignable to type 'string | number | boolean'
        params: [billDate],
      })
      .asMappedResults();

    if (resultIterator.length > 0) {
      // @ts-ignore Type 'QueryResultMap[]' is not assignable to type 'RebateDetail[]'
      itemObj.rebateDetailArr = resultIterator;
      return true;
    }
    return false;
  }

  /**
   * Returns the tier level with the best value
   *
   * @param {number} tierGroupId Tier Group ID
   * @param {number} countAs Count multiplier
   * @param {RipObject} itemObj Item object with Rip details
   * @returns {BestTierLevel} Tier Level with best value
   */
  function runTierLevelQuery(tierGroupId, countAs, itemObj) {
    const convertedUnitsType =
      itemObj.unitsType == Unit.BOTTLE ? Unit.CASE : Unit.BOTTLE;

    const { itemQuantity, convertedItemQuantity, unitsType } = itemObj;
    const itemCount = Math.round(itemQuantity * countAs);
    const convertedItemCount = Math.round(convertedItemQuantity * countAs);

    const whenConvertedUnitThenConvertQuantity = convertedItemQuantity
      ? `WHEN rtl.custrecord_tier_quantity <= ${convertedItemCount} AND rtl.custrecord_unit_of_measure = ${convertedUnitsType} THEN ${convertedItemCount}`
      : ` `;

    const orConvertedQuantityAndUnitsType = convertedItemQuantity
      ? `OR (rtl.custrecord_tier_quantity <= ${convertedItemCount} AND rtl.custrecord_unit_of_measure = ${convertedUnitsType})`
      : ``;

    const getBestTierLevelInfoSql = `
      SELECT
      TOP 1
        rtl.custrecord_dollar_off AS "custrecord_dollar_off",
        rtl.id AS "id",
        rtl.custrecord_tier_quantity AS "tier_minimum",
        rtl.custrecord_unit_of_measure AS "tier_units",
        CASE
          WHEN rtl.custrecord_tier_quantity <= ${itemCount} AND rtl.custrecord_unit_of_measure = ${unitsType} THEN ${itemCount}
          ${whenConvertedUnitThenConvertQuantity}
          ELSE 0
        END AS "quantity_used"
      FROM
        customrecord_rebate_tier_level rtl
      INNER JOIN customrecord_rebate_tier_group rtg ON (
        rtl.id = rtg.custrecord_tier_level_1
        OR rtl.id = rtg.custrecord_tier_level_2
        OR rtl.id = rtg.custrecord_tier_level_3
        OR rtl.id = rtg.custrecord_tier_level_4
        OR rtl.id = rtg.custrecord_tier_level_5
      )
      WHERE
        rtg.id = ${tierGroupId}
        AND rtl.isinactive = 'F'
        AND rtg.isinactive = 'F'
        AND (
          (rtl.custrecord_tier_quantity <= ${itemCount} AND rtl.custrecord_unit_of_measure = ${unitsType})
          ${orConvertedQuantityAndUnitsType}
        )
      ORDER BY custrecord_dollar_off desc
    `;

    // @ts-ignore Type 'QueryResultMap | null | undefined' is not assignable to type 'BestTierLevel'
    return runSqlQuery(getBestTierLevelInfoSql);
  }

  /**
   * Get the RIP code that gives the item the best value.
   * Update the RipObject with amounts, agreement and tier information.
   *
   * @param {RipObject} itemObj Item object with Rip details
   * @returns {void}
   */
  function getBestRipLevel(itemObj) {
    // An item can have multiple rip codes, so now checking to see which code is best value
    let rebateDetailsArr = itemObj.rebateDetailArr;
    let /** @type {MaximizedRipObject[]} */ amountOffWithRipCodeArr = [];
    try {
      rebateDetailsArr.forEach(function (rebateDetailObj) {
        // Gets the best rip level - whichever is better, by case or by bottle
        const rebateDetailsObj = runTierLevelQuery(
          rebateDetailObj.tiergroup,
          rebateDetailObj.countas,
          itemObj
        );

        if (rebateDetailsObj && rebateDetailsObj.id != null) {
          // If a level will give money off based on the quantity
          amountOffWithRipCodeArr.push({
            amountOff: rebateDetailsObj.custrecord_dollar_off,
            totalAmountOff:
              rebateDetailsObj.custrecord_dollar_off *
              (rebateDetailsObj.quantity_used / rebateDetailsObj.tier_minimum),
            ripCode: rebateDetailObj.ripcode,
            agreementDetailId: rebateDetailObj.rebatedetail,
            tierLevelId: rebateDetailsObj.id,
            tierGroupInternalId: rebateDetailObj.tiergroup,
            usedConversion:
              rebateDetailsObj.tier_units == itemObj.unitsType ? false : true,
            countAs: rebateDetailObj.countas,
          });
        }
      });
    } catch (/** @type {any} */ err) {
      throw customError.updateError({
        errorType: customError.ErrorTypes.RECORD_NOT_LOADED,
        summary:
          "8b211306-9f87-4542-a66c-4a8954de13cc: ERROR_GETTING_RIP_LEVEL",
        details: getErrorDetails(err),
      });
    }

    if (amountOffWithRipCodeArr && amountOffWithRipCodeArr.length > 0) {
      // If a rip code fits into different levels for different rip codes
      // Checking to see which rip code will give better value and returning that object
      let ripCodeGreatestValueObj = amountOffWithRipCodeArr.reduce((max, rip) =>
        max.totalAmountOff > rip.totalAmountOff ? max : rip
      );
      // Adding the rip code details to the item object
      itemObj.bestRipCode = ripCodeGreatestValueObj.ripCode;
      itemObj.agreementDetailId = ripCodeGreatestValueObj.agreementDetailId;
      itemObj.tierLevelId = ripCodeGreatestValueObj.tierLevelId;
      itemObj.tierGroupInternalId = ripCodeGreatestValueObj.tierGroupInternalId;
      itemObj.usedConversion = ripCodeGreatestValueObj.usedConversion;
      itemObj.itemQuantity =
        ripCodeGreatestValueObj.usedConversion == true
          ? Math.round(
              itemObj.convertedItemQuantity * ripCodeGreatestValueObj.countAs
            )
          : Math.round(itemObj.itemQuantity * ripCodeGreatestValueObj.countAs);
      itemObj.countAs = ripCodeGreatestValueObj.countAs;
    } else {
      //If this item by itself doesn't fit into any tier level - add the rip code to the obj because maybe once grouped with a diff item, it will fit
      itemObj.bestRipCode = itemObj.rebateDetailArr[0].ripcode;
      itemObj.agreementDetailId = itemObj.rebateDetailArr[0].rebatedetail;
      itemObj.tierGroupInternalId = itemObj.rebateDetailArr[0].tiergroup;
      itemObj.countAs = itemObj.rebateDetailArr[0].countas;
    }

    // Deleting the rebateDetailArr - which had all rebate options, because we already got the best match
    // @ts-ignore The operand of a 'delete' operator must be optional.ts(2790)
    delete itemObj.rebateDetailArr;
  }

  /**
   * Create a rip code and items mapping object
   *
   * @param {RipObject[]} itemsWithRipInfoObjArr Item objects with Rip Details
   * @returns {RipObjectSummary[]} Array of grouped together items under a Rip object
   */
  function groupTogetherItemsBasedonRipCodes(itemsWithRipInfoObjArr) {
    let /** @type {RipObjectSummary[]} */ ripCodesWithTotalsArrNew = [];
    try {
      ripCodesWithTotalsArrNew = itemsWithRipInfoObjArr
        .reduce(
          (
            /** @type {RipObjectSummary[]} */ acc,
            /** @type {RipObject} */ curr
          ) => {
            const agreementDetailId = curr.agreementDetailId;
            const existingGroup = acc.find(
              (group) => group.agreementId === agreementDetailId
            );

            const /** @type {RipQuantity} */ quantityObj = {
                quantity: curr.itemQuantity,
                unitUsedForRip: curr.usedConversion
                  ? curr.unitsType === Unit.BOTTLE
                    ? Unit.CASE
                    : Unit.BOTTLE
                  : curr.unitsType,
              };

            if (existingGroup) {
              const existingQuantityObj = existingGroup.quantities.find(
                (obj) => obj.unitUsedForRip === quantityObj.unitUsedForRip
              );

              if (existingQuantityObj) {
                existingQuantityObj.quantity += curr.itemQuantity;
              } else {
                existingGroup.quantities.push(quantityObj);
              }

              existingGroup.itemIds.push({
                itemId: curr.itemId,
                itemQuantity: curr.itemQuantity,
                unitUsedForRip: curr.usedConversion
                  ? curr.unitsType === Unit.BOTTLE
                    ? Unit.CASE
                    : Unit.BOTTLE
                  : curr.unitsType,
                countAs: curr.countAs,
              });
            } else {
              acc.push({
                agreementId: agreementDetailId,
                tierGroupInternalId: curr.tierGroupInternalId,
                ripCode: curr.bestRipCode,
                unitsType: curr.unitsType,
                quantities: [quantityObj],
                itemIds: [
                  {
                    itemId: curr.itemId,
                    itemQuantity: curr.itemQuantity,
                    unitUsedForRip: quantityObj.unitUsedForRip,
                    countAs: curr.countAs,
                  },
                ],
              });
            }

            return acc;
          },
          []
        )
        .map((group) => ({
          ...group,
          itemIds: group.itemIds.sort((a, b) => a.itemId - b.itemId),
        }));
    } catch (/** @type {any} */ err) {
      throw customError.updateError({
        errorType: customError.ErrorTypes.EVAL_ERROR,
        summary:
          "caa2267f-c8e7-46fd-80e8-c2845079b650: ERROR_GROUPING_RIP_CODES",
        details: getErrorDetails(err),
      });
    }
    return ripCodesWithTotalsArrNew;
  }

  /**
   * Determines if the minimum tier level has been reached based on quantity
   *
   * @param {RipObjectSummary} ripCodeObj Rip Object Summary
   * @param {boolean} onlyReturnBooleanNoDetails Determines if the function will only return a boolean
   * @returns {TierLevel | boolean} Tier level from the query or the flag if minimum tier has been reached
   */
  function checkIfReachedMinTierLevel(ripCodeObj, onlyReturnBooleanNoDetails) {
    let /** @type {any} */ tierLevelObjsArr = [];
    ripCodeObj.quantities.forEach((unitQuantityObj) => {
      const checkRemainder = `custrecord_tier_quantity <= ${unitQuantityObj.quantity}`;

      const andCheckTotalQuantity = ripCodeObj.totalQuantity
        ? `AND custrecord_tier_quantity <= ${ripCodeObj.totalQuantity}`
        : "";

      const andCheckMatchingUnitsString =
        unitQuantityObj.unitUsedForRip != Unit.GROUP
          ? `AND custrecord_unit_of_measure = ${unitQuantityObj.unitUsedForRip}`
          : "";

      const andCheckQuantityForBottles =
        unitQuantityObj.unitUsedForRip == Unit.CASE
          ? `OR (
            custrecord_tier_quantity <= ${unitQuantityObj.quantity} 
            AND custrecord_unit_of_measure = 1
          )`
          : "";

      const fromTierGroupWhereQuantitiesChecked = `
        FROM
          customrecord_rebate_tier_group
        WHERE
          customrecord_rebate_tier_group.isinactive = 'F'
          AND id = ${ripCodeObj.tierGroupInternalId}
          AND ((${checkRemainder} ${andCheckTotalQuantity} ${andCheckMatchingUnitsString}) ${andCheckQuantityForBottles})
      `;

      const tierLevelIdQuery = `SELECT TOP 1
          custrecord_dollar_off AS "custrecord_dollar_off",
          id, 
          custrecord_tier_quantity AS "tier_minimum"
        FROM
          customrecord_rebate_tier_level
        WHERE
          customrecord_rebate_tier_level.isinactive = 'F'
          AND (
            id IN (SELECT custrecord_tier_level_1 ${fromTierGroupWhereQuantitiesChecked})
            OR Id IN (SELECT custrecord_tier_level_2 ${fromTierGroupWhereQuantitiesChecked})
            OR Id IN (SELECT custrecord_tier_level_3 ${fromTierGroupWhereQuantitiesChecked})
            OR Id IN (SELECT custrecord_tier_level_4 ${fromTierGroupWhereQuantitiesChecked})
            OR Id IN (SELECT custrecord_tier_level_5 ${fromTierGroupWhereQuantitiesChecked})
          )
        ORDER BY custrecord_dollar_off desc
      `;

      const tierLevelObj = runSqlQuery(tierLevelIdQuery);

      (ripCodeObj.unitsType = unitQuantityObj.unitUsedForRip),
        tierLevelObj &&
          tierLevelObj.custrecord_dollar_off &&
          Number(tierLevelObj.custrecord_dollar_off) > 0 &&
          ((tierLevelObj.quantityUsed = unitQuantityObj.quantity),
          tierLevelObjsArr.push(tierLevelObj));
    });

    if (tierLevelObjsArr.length > 0) {
      const /** @type {TierLevel} */ tierLevelObj = tierLevelObjsArr.reduce(
          (/** @type {TierLevel} */ maxObj, /** @type {TierLevel} */ currObj) =>
            currObj.custrecord_dollar_off > maxObj.custrecord_dollar_off
              ? currObj
              : maxObj
        );
      // If we don't need the full object - add on to the passed in obj the needed properties, otherwise return the whole obj
      if (onlyReturnBooleanNoDetails) {
        ripCodeObj.amountOff = tierLevelObj.custrecord_dollar_off;
        ripCodeObj.tierLevelId = tierLevelObj.id;
        ripCodeObj.totalQuantity = tierLevelObj.quantityUsed;
        return true;
      } else {
        return tierLevelObj;
      }
    } else {
      return false;
    }
  }

  /**
   * Return Rip objects containing an array of items
   *
   * @returns {RipObjectSummary[] | undefined}
   */
  function getInputData() {
    try {
      const currentScript = runtime.getCurrentScript();
      const billId = currentScript.getParameter({
        name: "custscript_rip_create_credit_bill_id",
      });
      const vendorBill = record.load({
        type: record.Type.VENDOR_BILL,
        id: billId,
      });
      const vendor = Number(
        vendorBill.getValue({
          fieldId: "entity",
        }) || 0
      );

      if (!Object.values(RipVendor).includes(vendor)) {
        log.error({
          title: "Entity is not a RIP Vendor",
          details: vendor,
        });
        return [];
      }

      const subsidiariesArr = vendorBill.getValue({
        fieldId: "subsidiary",
      });
      const alreadyLinkedCredit = vendorBill.getValue({
        fieldId: "custbody_associated_bill_credit",
      });
      const approvalStatus = vendorBill.getValue({
        fieldId: "approvalstatus",
      });
      const bridgeSubsidiaries =
        bridgeHelperFunctionsLib.getBridgeSubsidiaries();
      const isBridgeSubsidiary = bridgeHelperFunctionsLib.isBridgeSubsidiary(
        subsidiariesArr,
        bridgeSubsidiaries
      );

      if (!isBridgeSubsidiary || approvalStatus != 2 || alreadyLinkedCredit) {
        return [];
      }

      let ripInvoiceDate = vendorBill.getValue({
        fieldId: "custbody_rip_bill_invoice_date",
      });
      if (!ripInvoiceDate) {
        // Adding in this part for now, as per Esther because too many bills were giving errors when missing the rip invoice date
        ripInvoiceDate = vendorBill.getValue({
          fieldId: "trandate",
        });
      }

      const formattedBillDate = format.format({
        value: ripInvoiceDate,
        type: format.Type.DATE,
      });
      const lineCount = vendorBill.getLineCount({
        sublistId: "item",
      });

      log.debug({
        title: `Vendor Bill ${billId}`,
        details: JSON.stringify({
          vendor,
          subsidiariesArr,
          alreadyLinkedCredit,
          approvalStatus,
          isBridgeSubsidiary,
          ripInvoiceDate,
          formattedBillDate,
          lineCount,
        }),
      });

      const itemObjsArr = getItemsInfo(vendorBill, lineCount);
      //** Getting/setting information for each individual item **//
      let /** @type {RipObject[]} */ itemsWithRipCodeArr = [];
      if (itemObjsArr && itemObjsArr.length > 0) {
        itemsWithRipCodeArr = itemObjsArr.filter((itemObj) =>
          checkIfAvailRip(itemObj, formattedBillDate, vendor)
        );
      }

      if (itemsWithRipCodeArr && itemsWithRipCodeArr.length > 0) {
        itemsWithRipCodeArr.forEach((itemObj) => getBestRipLevel(itemObj));

        //** Grouping together items with the same rip code and then creating new record for each rip code **//

        let /** @type {RipObjectSummary[]} */ ripCodeWithQtyArr =
            groupTogetherItemsBasedonRipCodes(itemsWithRipCodeArr);

        ripCodeWithQtyArr = ripCodeWithQtyArr.filter((ripCodeObj) =>
          checkIfReachedMinTierLevel(ripCodeObj, true)
        );

        if (ripCodeWithQtyArr && ripCodeWithQtyArr.length > 0) {
          return ripCodeWithQtyArr;
        }
      }

      return [];
    } catch (/** @type {any} */ err) {
      customError.throwError({
        summaryText:
          "98c7d0ce-07dc-4cd3-9577-c20b136830fc: GET_INPUT_DATA_ERROR",
        error: err,
        recordId: null,
        recordName: null,
        recordType: null,
        errorWillBeGrouped: false,
      });
    }
  }

  /**
   * Create Accrual Record in NetSuite
   *
   * @param {RipObjectSummary} ripCodeObj Rip Object
   * @param {number} vendorBillId Vendor Bill Internal ID
   * @param {string} vendorBillName Vendor Bill Name
   */
  function createNewAccrualRecord(ripCodeObj, vendorBillId, vendorBillName) {
    const newAccrualRecord = record.create({
      type: "customrecord_rip_po_rip_code_accrual",
    });
    const acrrualRecordObj = {
      name: `RIP Accrual #${ripCodeObj.ripCode} for ${vendorBillName}`,
      custrecord_rip_code_from_agreement: ripCodeObj.ripCode,
      custrecord_rebate_agreement_detail_recor: ripCodeObj.agreementId,
      custrecord_vendor_bill_accruing_for: vendorBillId,
      custrecord_highest_tier_level_reached: ripCodeObj.tierLevelId,
      custrecord_total_quantity_received: ripCodeObj.totalQuantity,
      custrecord_rip_code_tier_group: ripCodeObj.tierGroupInternalId,
    };
    // @ts-ignore setBodyValues definition not available
    recordHelperLib.setBodyValues(acrrualRecordObj, newAccrualRecord);

    try {
      const newAccrualRecordId = newAccrualRecord.save({
        ignoreMandatoryFields: false,
      });
      ripCodeObj.accrualRecordId = newAccrualRecordId;
      return newAccrualRecordId;
    } catch (/** @type {any} */ err) {
      log.error({
        title:
          "0c730d2a-842d-4fab-b051-5b7abbc5527c: ERROR_CREATING_ACCRUAL_RECORD",
        details: `${getErrorDetails(err)} - ${JSON.stringify(
          acrrualRecordObj
        )}`,
      });
      throw customError.updateError({
        errorType: customError.ErrorTypes.RECORD_NOT_CREATED,
        summary:
          "0c730d2a-842d-4fab-b051-5b7abbc5527c: ERROR_CREATING_ACCRUAL_RECORD",
        details: getErrorDetails(err),
      });
    }
  }

  /**
   * This is a simplified version of checkIfReachedMinTierLevel.
   * Gets the applicable tier level based on the tier group and rip code object.
   *
   * @param {TierLevel[]} tierLevels Tier group object containing tier levels
   * @param {RipObjectSummary} ripCodeObj Rip Object Summary containing tier group internal
   * @returns {TierLevel} RIP Tier Level object
   */
  function getApplicableTierLevel(tierLevels, ripCodeObj) {
    let /** @type {any} */ tierLevelObjsArr = [];
    ripCodeObj.quantities.forEach((unitQuantityObj) => {
      const tierLevel = tierLevels.find(
        (/** @type {TierLevel} */ tierLevel) => {
          const isRemainingQuantityWithinLevel =
            tierLevel.custrecord_tier_quantity <= unitQuantityObj.quantity;
          const isTotalQuantityWithinLevel =
            !ripCodeObj.totalQuantity ||
            tierLevel.custrecord_tier_quantity <= ripCodeObj.totalQuantity;
          const doesItemUomMatchLevelUom =
            unitQuantityObj.unitUsedForRip === Unit.GROUP ||
            tierLevel.custrecord_unit_of_measure ===
              unitQuantityObj.unitUsedForRip;
          const canCompareWithMismatchingUoms =
            unitQuantityObj.unitUsedForRip !== Unit.CASE ||
            tierLevel.custrecord_unit_of_measure === Unit.BOTTLE;

          return (
            isRemainingQuantityWithinLevel &&
            ((isTotalQuantityWithinLevel && doesItemUomMatchLevelUom) ||
              canCompareWithMismatchingUoms)
          );
        }
      );
      (ripCodeObj.unitsType = unitQuantityObj.unitUsedForRip),
        tierLevel &&
          tierLevel.custrecord_dollar_off &&
          tierLevel.custrecord_dollar_off > 0 &&
          ((tierLevel.quantityUsed = unitQuantityObj.quantity),
          tierLevelObjsArr.push(tierLevel));
    });

    return tierLevelObjsArr.reduce(
      (/** @type {TierLevel} */ maxObj, /** @type {TierLevel} */ currObj) => {
        return currObj.custrecord_dollar_off > maxObj.custrecord_dollar_off
          ? currObj
          : maxObj;
      },
      { custrecord_dollar_off: 0 }
    );
  }

  /**
   * Create Tier Quantity records in NetSuite
   *
   * @param {RipObjectSummary} ripCodeObj Rip Object Summary
   * @returns {TierQuantityObj[]} Objects containing tier quantity records for creation
   */
  function createTierQuantityRecords(ripCodeObj) {
    try {
      let /** @type {TierQuantityObj[]} */ tierQtyObjArr = [];
      //Creating a record with the quantity, tier and amount off for each tier level
      let totalAmountOwed = 0;

      const tierLevels = query
        .runSuiteQL({
          query: `
            SELECT
              BUILTIN_RESULT.TYPE_INTEGER(id) as id,
              BUILTIN_RESULT.TYPE_FLOAT(custrecord_dollar_off) as custrecord_dollar_off,
              BUILTIN_RESULT.TYPE_FLOAT(custrecord_tier_quantity) as custrecord_tier_quantity,
              BUILTIN_RESULT.TYPE_INTEGER(custrecord_unit_of_measure) as custrecord_unit_of_measure
            FROM
              customrecord_rebate_tier_level tier
            WHERE
              tier.isinactive = 'F'
              AND (
                id IN (
                  SELECT
                    custrecord_tier_level_1
                  FROM
                    customrecord_rebate_tier_group
                  WHERE
                    customrecord_rebate_tier_group.isinactive = 'F'
                    AND id = ${ripCodeObj.tierGroupInternalId}
                )
                OR id IN (
                  SELECT
                    custrecord_tier_level_2
                  FROM
                    customrecord_rebate_tier_group
                  WHERE
                    customrecord_rebate_tier_group.isinactive = 'F'
                    AND id = ${ripCodeObj.tierGroupInternalId}
                )
                OR id IN (
                  SELECT
                    custrecord_tier_level_3
                  FROM
                    customrecord_rebate_tier_group
                  WHERE
                    customrecord_rebate_tier_group.isinactive = 'F'
                    AND id = ${ripCodeObj.tierGroupInternalId}
                )
                OR id IN (
                  SELECT
                    custrecord_tier_level_4
                  FROM
                    customrecord_rebate_tier_group
                  WHERE
                    customrecord_rebate_tier_group.isinactive = 'F'
                    AND id = ${ripCodeObj.tierGroupInternalId}
                )
                OR id IN (
                  SELECT
                    custrecord_tier_level_5
                  FROM
                    customrecord_rebate_tier_group
                  WHERE
                    customrecord_rebate_tier_group.isinactive = 'F'
                    AND id = ${ripCodeObj.tierGroupInternalId}
                )
              )
            ORDER BY
              custrecord_dollar_off DESC
          `,
        })
        .asMappedResults();

      /**
       * Adding up the total amount owed for this rip code, so we can use it for vendor credit
       *
       * @returns {void}
       */
      function createSingleRecord() {
        // For sure try to create one record, then see if more are needed
        // @ts-ignore Type QueryResultMap[] is not assignable to getApplicableTierLevel's tierLevels
        const tierLevelObj = getApplicableTierLevel(tierLevels, ripCodeObj);

        if (
          typeof tierLevelObj !== "boolean" &&
          tierLevelObj.custrecord_dollar_off > 0
        ) {
          tierQtyObjArr.push({
            custrecord_incremented_tier_level: tierLevelObj.id,
            custrecord_quantity_used_for_tier_level:
              tierLevelObj.custrecord_tier_quantity,
            name: `${tierLevelObj.custrecord_dollar_off} $ -${tierLevelObj.custrecord_tier_quantity}`,
            custrecord_rip_code_accrual_per_bill_rec:
              ripCodeObj.accrualRecordId,
          });

          totalAmountOwed += tierLevelObj.custrecord_dollar_off;

          ripCodeObj.totalQuantity = ripCodeObj.totalQuantity
            ? ripCodeObj.totalQuantity - tierLevelObj.custrecord_tier_quantity
            : 0;

          // Update the leftover quantity and try again to see if another new tier quantity record is needed
          if (ripCodeObj.totalQuantity > 0) {
            createSingleRecord();
          }
        }
      }
      createSingleRecord();
      ripCodeObj.totalAmountForCredit = totalAmountOwed;
      return tierQtyObjArr;
    } catch (/** @type {any} */ err) {
      throw customError.updateError({
        errorType: customError.ErrorTypes.RECORD_NOT_CREATED,
        summary:
          "29787027-0dce-49f5-919a-0da1a921116d: ERROR_CREATING_RIP_TIER_RECORD",
        details: getErrorDetails(err),
      });
    }
  }

  /**
   * Attach Accrual Record link to the Vendor Bill
   *
   * @param {record.Record} vendorBill Vendor Bill NetSuite record
   * @param {RipObjectSummary} ripInfoObj Summarized Rip Object
   * @returns {void}
   */
  function setRipAccrualLinkAndCodePerItemOnBill(vendorBill, ripInfoObj) {
    ripInfoObj.itemIds.forEach((itemObj) => {
      try {
        const lineNumber = vendorBill.findSublistLineWithValue({
          sublistId: "item",
          fieldId: "item",
          value: itemObj.itemId,
        });
        ripInfoObj.accrualRecordId &&
          vendorBill.setSublistValue({
            sublistId: "item",
            fieldId: "custcol_rip_accrual_record_link",
            line: lineNumber,
            value: ripInfoObj.accrualRecordId,
          });
        ripInfoObj.ripCode &&
          vendorBill.setSublistValue({
            sublistId: "item",
            fieldId: "custcol_best_rip_code",
            line: lineNumber,
            value: ripInfoObj.ripCode,
          });
      } catch (/** @type {any} */ err) {
        log.error({
          title:
            "4fb947d7-2363-4a86-a296-376f85f183d4: ERROR_SETTING_RIP_ACCRUAL_LINK",
          details: getErrorDetails(err),
        });
        customError.updateError({
          errorType: customError.ErrorTypes.VALUE_NOT_SET,
          summary:
            "4fb947d7-2363-4a86-a296-376f85f183d4: ERROR_SETTING_RIP_ACCRUAL_LINK",
          details: getErrorDetails(err),
        });
      }
    });
  }

  /**
   * Calculate the total credit per item
   *
   * @param {RipObjectSummary} ripInfoObj Rip Object Summary
   * @returns {RipObjectSummary} Updated Rip Object Summary
   */
  function splitTotalsbyItem(ripInfoObj) {
    ripInfoObj.amountPerUnit =
      ripInfoObj.totalAmountForCredit && ripInfoObj.originalTotalQuantity
        ? ripInfoObj.totalAmountForCredit / ripInfoObj.originalTotalQuantity
        : 0;

    ripInfoObj.itemIds.forEach(
      (item) =>
        (item.totalCredit = item.itemQuantity * (ripInfoObj.amountPerUnit || 0))
    );

    return ripInfoObj;
  }

  /**
   * Create tier quantity objects and accrual records.
   * Link the accrual records to the the vendor bill.
   *
   * @param {import("N/types").EntryPoints.MapReduce.mapContext} context Map context
   * @returns {void}
   */
  function map(context) {
    try {
      /** @type {RipObjectSummary} */
      const ripInfoObj = JSON.parse(context.value);

      log.debug({
        title: "Rip Info Object",
        details: ripInfoObj,
      });

      const currentScript = runtime.getCurrentScript();
      const billId = Number(
        currentScript.getParameter({
          name: "custscript_rip_create_credit_bill_id",
        }) || 0
      );
      const vendorBill = record.load({
        type: record.Type.VENDOR_BILL,
        id: billId,
      });
      const vendorBillName =
        vendorBill
          .getValue({
            fieldId: "transactionnumber",
          })
          ?.toString() || "";
      const vendorId =
        vendorBill
          .getValue({
            fieldId: "entity",
          })
          ?.toString() || "";
      const vendorItem = RipVendorItem[vendorId];

      if (!vendorItem) {
        throw customError.updateError({
          errorType: customError.ErrorTypes.NO_VALUE_RETURNED,
          summary:
            "dca04292-4dfe-4aed-beed-d274553289d0: UNSUPPORTED_VENDOR_ERROR",
          details: `Missing correct rip item for this bill: ${billId}`,
        });
      }

      ripInfoObj.originalTotalQuantity = ripInfoObj.totalQuantity;

      createNewAccrualRecord(ripInfoObj, billId, vendorBillName);
      createTierQuantityRecords(ripInfoObj).forEach(
        (/** @type {TierQuantityObj} */ rec, /** @type {number} */ index) => {
          const randomNumber = Math.floor(Math.random() * 1000);
          const uniqueTimeStampId = `TQ-${index}-${Date.now()}-${randomNumber}`;
          context.write(uniqueTimeStampId, JSON.stringify(rec));
        }
      );
      setRipAccrualLinkAndCodePerItemOnBill(vendorBill, ripInfoObj);
      splitTotalsbyItem(ripInfoObj);

      vendorBill.save();

      const /** @type {{[key:string]:any}[]} */ itemSublistArr = [];

      ripInfoObj.itemIds.forEach((/** @type {RipItem} */ itemObj) => {
        itemSublistArr.push({
          item: vendorItem,
          rate: ripInfoObj.amountPerUnit,
          quantity: itemObj.itemQuantity,
          description: `Rip received for rip code ${ripInfoObj.ripCode} and total credit for this code was ${ripInfoObj.totalAmountForCredit}`,
          custcol_rip_item_link: itemObj.itemId,
          custcol_rip_total_quantity_of_item: itemObj.itemQuantity,
          custcol_rip_agreement_detail_record: ripInfoObj.agreementId,
          custcol_unit_used_for_rip: itemObj.unitUsedForRip,
          custcol_rip_count_as: itemObj.countAs,
        });
      });
      context.write(`BILL-${billId.toString()}`, {
        itemSublistArr,
        ripInfoObj,
      });
    } catch (/** @type {any} */ err) {
      log.error({
        title: "edad62dc-2905-428a-9050-89b0bd841eb7: MAP_ERROR",
        details: err,
      });
      customError.throwError({
        summaryText: "edad62dc-2905-428a-9050-89b0bd841eb7: MAP_ERROR",
        error: err,
        recordId: null,
        recordName: null,
        recordType: null,
        errorWillBeGrouped: false,
      });
    }
  }

  /**
   * Create vendor credit and tier-quantity records in NetSuite.
   *
   * @param {import("N/types").EntryPoints.MapReduce.reduceContext} context Reduce context
   * @returns {void}
   */
  function reduce(context) {
    try {
      const key = context.key;
      if (key.startsWith("BILL-")) {
        log.debug({
          title: "Creating Vendor Bill Credit",
          details: key,
        });
        const { itemSublist, accrualRecordIds } = context.values.reduce(
          (acc, value) => {
            const { itemSublistArr, ripInfoObj } = JSON.parse(value);
            acc.itemSublist = acc.itemSublist.concat(itemSublistArr);
            acc.accrualRecordIds.push(ripInfoObj.accrualRecordId);

            return acc;
          },
          {
            /** @type {{[key:string]:any}[]} */ itemSublist: [],
            /** @type {number[]} */ accrualRecordIds: [],
          }
        );
        const currentScript = runtime.getCurrentScript();
        const billId = currentScript.getParameter({
          name: "custscript_rip_create_credit_bill_id",
        });

        const vendorBill = record.load({
          type: record.Type.VENDOR_BILL,
          id: billId,
        });
        const vendorBillName = vendorBill.getValue({
          fieldId: "transactionnumber",
        });

        const vendorCreditObj = {
          entity: vendorBill.getValue({
            fieldId: "entity",
          }),
          subsidiary: vendorBill.getValue({
            fieldId: "subsidiary",
          }),
          account: 112,
          trandate: vendorBill.getValue({
            fieldId: "trandate",
          }),
          custbody_associated_vendor_bill: vendorBill.id,
          tranid: `RIP${vendorBillName}`,
          custbody_rip_vendor_credit: true,
        };

        try {
          // @ts-ignore createVendorCredit definition not available
          const newVendorCredit = createRipRecordLib.createVendorCredit(
            vendorCreditObj,
            itemSublist
          );
          record.submitFields({
            type: record.Type.VENDOR_BILL,
            id: billId.toString(),
            values: {
              custbody_associated_bill_credit: newVendorCredit,
            },
          });
          context.write("vendorCreditId", newVendorCredit);
        } catch (/** @type {any} */ err) {
          throw customError.updateError({
            errorType: customError.ErrorTypes.RECORD_NOT_CREATED,
            summary:
              "986b24bd-15c7-4bb8-a2f9-89acd56d9ff2: ERROR_CREATING_VENDOR_CREDIT",
            details: getErrorDetails(err),
          });
        }
      } else if (key.startsWith("TQ-")) {
        const tierQtyObj = JSON.parse(context.values[0]);
        log.debug({
          title: "Creating Tier-Quantity Record",
          details: tierQtyObj,
        });
        // @ts-ignore createRipTierRecord definition not available
        createRipRecordLib.createRipTierRecord(tierQtyObj);
      }
    } catch (/** @type {any} */ err) {
      log.error({
        title: "38004bba-1041-47d5-9853-a63ab74411e9: REDUCE_ERROR",
        details: err,
      });
      customError.throwError({
        summaryText: "38004bba-1041-47d5-9853-a63ab74411e9: REDUCE_ERROR",
        error: err,
        recordId: null,
        recordName: null,
        recordType: null,
        errorWillBeGrouped: false,
      });
    }
  }

  /**
   * Send an error email or a success email containing the ID of the processed bill.
   *
   * @param {import("N/types").EntryPoints.MapReduce.summarizeContext} context Summarize context
   */
  function summarize(context) {
    try {
      const currentScript = runtime.getCurrentScript();
      const billId = currentScript.getParameter({
        name: "custscript_rip_create_credit_bill_id",
      });

      // @ts-ignore This expression is not constructable.
      const stageHandling = new StageHandling(context);
      stageHandling.printScriptProcessingSummary();
      stageHandling.printRecordsProcessed();

      let billCreditId = "";
      context.output.iterator().each(function (key, value) {
        billCreditId = value;
        return true;
      });

      // @ts-ignore return values exist and are not void
      let { errorsMessage } = stageHandling.printErrors({
        groupErrors: true,
        formatMessageForEmail: true,
      });

      const emailMessage = errorsMessage
        ? errorsMessage
        : billCreditId
        ? `RIP records generated successfully for Vendor Bill ID: ${billId}.`
        : "";

      emailMessage &&
        email.send({
          author: 140818,
          recipients: [140818, 43398, 3288], // Lyle Prospero, Miri Landau, Huvi Boxer
          subject: `${
            errorsMessage ? "Error" : "Success"
          }: BRDG RIP Create Credit Records MR`,
          body: emailMessage,
        });
    } catch (/** @type {any} */ err) {
      customError.throwError({
        summaryText: "2e2f09a8-351a-436d-b3d9-36974a37f537: SUMMARIZE_ERROR",
        error: err,
        recordId: null,
        recordName: null,
        recordType: null,
        errorWillBeGrouped: false,
      });
    }
  }

  return {
    getInputData,
    map,
    reduce,
    summarize,
  };
});
