define([
  "require",
  "LoDash",
  "N/log",
  "N/query",
  "N/record",
  "N/format",
  "N/search",
  "../../../Helper_Libraries/vlmd_record_module_helper_lib",
  "../Libraries/brdg_rip_records_helper_lib",
  "LoDash",
], (/** @type {any} */ require,  /** @type {any} */ _) => {
  const log = require("N/log");
  const query = require("N/query");
  const record = require("N/record");
  const format = require("N/format");
  const search = require("N/search");
  const recordHelperLib = require("../../../Helper_Libraries/vlmd_record_module_helper_lib");
  const createRipRecordLib = require("../Libraries/brdg_rip_records_helper_lib");

  const Unit = {
    BOTTLE: 1,
    CASE: 2,
    GROUP: 3,
  };

  /**
   * Return item object with quantities and units of measure
   *
   * @typedef {Object} RipItem
   * @property {number} itemId ID
   * @property {number} itemQuantity Quantity multiplied by effective count
   * @property {number} convertedItemQuantity Converted Quantity
   * @property {number} unitsType Unit of Measure type
   * @property {number} billId Vendor Bill ID
   * @property {any} billDate Vendor Bill NS Date
   * @property {number} vendor Vendor Bill Entity ID
   * @property {RebateDetail[]} rebateDetailArr Associated Rebate Details
   * @property {string} bestRipCode Best Rip Code
   * @property {number} agreementDetailId Agreement Detail ID
   * @property {number} tierLevelId Tier Level ID
   * @property {number} tierGroupInternalId Tier Group ID
   * @property {boolean} usedConversion Is conversion required
   * 
   * @param {number} billId
   * @returns {RipItem[]} RIP Item
   */
  function getItemObjs(billId) {
    const itemObjArr = [];

    const vendorBill = record.load({
      type: record.Type.VENDOR_BILL,
      id: billId,
    });

    let billDate = format.format({
      value:
        vendorBill.getValue("custbody_rip_bill_invoice_date") ??
        vendorBill.getValue("trandate"),
      type: format.Type.DATE,
    });

    const vendor = Number(vendorBill.getValue("entity") || 0);
    const itemLineCount = vendorBill.getLineCount({ sublistId: "item" });

    for (var i = 0; i < itemLineCount; i++) {
      const isComponentItem = vendorBill.getSublistValue({
        sublistId: "item",
        fieldId: "ingroup",
        line: i,
      });

      if (isComponentItem == "T") {
        continue;
      }

      const itemId = Number(vendorBill.getSublistValue({
        sublistId: "item",
        fieldId: "item",
        line: i,
      }) || 0);

      const itemQuantity = Number(vendorBill.getSublistValue({
        sublistId: "item",
        fieldId: "quantity",
        line: i,
      }) || 0);

      const itemType = vendorBill.getSublistValue({
        sublistId: "item",
        fieldId: "itemtype",
        line: i,
      })?.toString() || "";

      //When item is a group item - pull the unit from the component which is on the next line
      const itemUnits = vendorBill.getSublistValue({
        sublistId: "item",
        fieldId: "units_display",
        line: itemType == "Group" ? i + 1 : i,
      })?.toString() || "";
      const unitsType = itemUnits.includes("CS") ? Unit.CASE : Unit.BOTTLE;
      //If the purchase unit includes CS, it first gets a CASE rebate check, otherwise a bottle rebate check

      const convertedItemQuantity = unitsType === Unit.CASE
        ? Number(vendorBill.getSublistValue({
          sublistId: "item",
          fieldId: "unitconversionrate",
          line: i,
        }) || 0) * itemQuantity
        : 0;

      // Set default values for properties to be updated by getBestRipLevel
      const itemObj = {
        itemId,
        itemQuantity,
        convertedItemQuantity,
        unitsType,
        billId,
        billDate,
        vendor,
        rebateDetailArr: [],
        bestRipCode: "",
        agreementDetailId: 0,
        tierLevelId: 0,
        tierGroupInternalId: 0,
        usedConversion: false,
      };
      itemObjArr.push(itemObj);
    }
    return itemObjArr;
  }

  /**
   *
   * @typedef {Object} RebateDetail
   * @property {number} tiergroup Tier Group ID
   * @property {number} rebatedetail Agreement Detail ID
   * @property {string} ripcode Agreement Detail Rip Code
   * @property {number} countas Count As
   *
   * @param {RipItem} itemObj Object containing item and vendor bill information
   * @returns {boolean} Returns true if there is an available rip
   */
  function checkIfAvailRip(itemObj) {
    const sqlQuery = `
      SELECT
        tg.id tiergroup,
        cr.id rebatedetail,
        cr.custrecord_rip_code ripcode,
        COALESCE(ci.custrecord_agreement_detail_item_count,1) countas
      FROM
        CUSTOMRECORD_REBATE_AGREEMENT_DETAIL cr
        JOIN customrecord_rebate_agreement ca ON cr.custrecord_rebate_parent = ca.id
        JOIN customrecord_rebate_tier_group tg ON cr.custrecord_tier_group = tg.id
        LEFT JOIN customrecord_rebate_agreement_detail_itm ci ON ci.custrecord_agreement_detail_item = ${itemObj.itemId}
          AND ci.custrecord_agreement_detail = cr.id
      WHERE
        BUILTIN.MNFILTER(
          cr.custrecord_rebate_items_included,
          'MN_INCLUDE',
          '',
          'FALSE',
          NULL,
          ${itemObj.itemId}
        ) = 'T'
        AND TO_DATE(?, 'MM/DD/YYYY') BETWEEN ca.custrecord_rebate_start_date
        AND ca.custrecord_rebate_end_date
        AND ca.custrecord_rebate_vendor = ${itemObj.vendor} 
    `;

    const resultIterator = query
      .runSuiteQL({
        query: sqlQuery,
        params: [itemObj.billDate],
      })
      .asMappedResults();

    //Returns an array of all rebate details - the tiergroup, rebatedetail ID, the ripcode, and count as, and adds it to the item obj

    if (resultIterator.length > 0) {
      // @ts-ignore Type 'QueryResultMap[]' is not assignable to type 'RebateDetail[]'
      itemObj.rebateDetailArr = resultIterator;
      return true;
    } else {
      return false;
    }
  }

  /**
   * Returns a single row
   *
   * @typedef {Object} BestTierLevel
   * @property {number} custrecord_dollar_off Dollar amount
   * @property {number} id Tier Level ID
   * @property {number} tier_minimum Tier Level Quantity
   * @property {number} tier_units Tier Level Unit of Measure
   * @property {number} quantity_used Calculated Quantity
   *
   * @param {number} tierGroupId Tier Group ID
   * @param {number} countAs Count multiplier
   * @param {RipItem} itemObj Item object
   * @returns {BestTierLevel} Tier Level with best value
   */
  function runTierLevelQuery(tierGroupId, countAs, itemObj) {
    const convertedUnitsType = itemObj.unitsType == Unit.BOTTLE ? Unit.CASE : Unit.BOTTLE;

    const { itemQuantity, convertedItemQuantity, unitsType } = itemObj;
    const itemCount = Math.round(itemQuantity*countAs);
    const convertedItemCount = Math.round(convertedItemQuantity*countAs);

    const orConvertedQuantityAndUnitsType = convertedItemQuantity
      ? `OR (
        custrecord_tier_quantity <= ${convertedItemCount}
        AND custrecord_unit_of_measure = ${convertedUnitsType}
      )`
      : "";

    const fromTierGroupWhereQuantitiesChecked = `
      FROM
        customrecord_rebate_tier_group
      WHERE
        id  = ${tierGroupId} 
        AND ((custrecord_tier_quantity <= ${itemCount} AND custrecord_unit_of_measure = ${unitsType}) ${orConvertedQuantityAndUnitsType})
    `;

    const getBestTierLevelInfoSql = `
      SELECT
        MAX(custrecord_dollar_off) AS "custrecord_dollar_off",
        MAX(id) AS "id",
        MAX(custrecord_tier_quantity) AS "tier_minimum",
        MAX(custrecord_unit_of_measure) AS "tier_units"
      FROM
        customrecord_rebate_tier_level
      WHERE
        id IN (SELECT custrecord_tier_level_1 ${fromTierGroupWhereQuantitiesChecked})
        OR Id IN (SELECT custrecord_tier_level_2 ${fromTierGroupWhereQuantitiesChecked})
        OR Id IN (SELECT custrecord_tier_level_3 ${fromTierGroupWhereQuantitiesChecked})
        OR Id IN (SELECT custrecord_tier_level_4 ${fromTierGroupWhereQuantitiesChecked})
        OR Id IN (SELECT custrecord_tier_level_5 ${fromTierGroupWhereQuantitiesChecked})
    `;

    // @ts-ignore Type 'QueryResultMap | null' is not assignable to type 'BestTierLevel'
    return _runSqlQuery(getBestTierLevelInfoSql);
  }

  /**
   *
   * @param {string} sqlQuery
   * @returns results of query
   */
  function _runSqlQuery(sqlQuery) {
    const sqlResults = query
      .runSuiteQL({
        query: sqlQuery,
      })
      .asMappedResults()[0];

    if (!sqlResults) {
      return null;
    }

    return sqlResults;
  }

  /**
   * Get the RIP code that gives the item the best value.
   * Update the RipItem with amounts, agreement and tier information.
   *
   * @typedef {Object} MaximizedRipItem
   * @property {number} amountOff Tier Level Dollar Amount
   * @property {string} ripCode RIP Code
   * @property {number} agreementDetailId Agreement Detail ID
   * @property {number} tierLevelId Tier Level ID
   * @property {number} tierGroupInternalId Tier Group ID
   * @property {boolean} usedConversion Is conversion required
   * @property {number} countAs Count as
   *
   * @param {RipItem} itemObj
   * @returns {void}
   */
  function getBestRipLevel(itemObj) {
    //An item can have multiple rip codes, so now checking to see which code is best value
    let rebateDetailsArr = itemObj.rebateDetailArr;
    let /** @type {MaximizedRipItem[]} */ amountOffWithRipCodeArr = [];
    rebateDetailsArr.forEach(function (rebateDetailObj) {
      //Gets the best rip level - whichever is better, by case or by bottle
      const rebateDetailsObj = runTierLevelQuery(
        rebateDetailObj.tiergroup,
        rebateDetailObj.countas,
        itemObj
      );
      if (rebateDetailsObj && rebateDetailsObj.id != null) {
        //If a level will give money off based on the quantity
        amountOffWithRipCodeArr.push({
          amountOff: rebateDetailsObj.custrecord_dollar_off,
          ripCode: rebateDetailObj.ripcode,
          agreementDetailId: rebateDetailObj.rebatedetail,
          tierLevelId: rebateDetailsObj.id,
          tierGroupInternalId: rebateDetailObj.tiergroup,
          usedConversion:
            rebateDetailsObj.tier_units == itemObj.unitsType ? false : true,
          countAs: rebateDetailObj.countas,
        });
      }
    });

    if (amountOffWithRipCodeArr && amountOffWithRipCodeArr.length > 0) {
      //If a rip code fits into different levels for different rip codes
      //Checking to see which rip code will give better value and returning that object
      let ripCodeGreatestValueObj = amountOffWithRipCodeArr.reduce((max, rip) =>
        max.amountOff > rip.amountOff ? max : rip
      );
      //Adding the rip code details to the item OBJ
      itemObj.bestRipCode = ripCodeGreatestValueObj.ripCode;
      itemObj.agreementDetailId = ripCodeGreatestValueObj.agreementDetailId;
      itemObj.tierLevelId = ripCodeGreatestValueObj.tierLevelId;
      itemObj.tierGroupInternalId = ripCodeGreatestValueObj.tierGroupInternalId;
      itemObj.usedConversion = ripCodeGreatestValueObj.usedConversion;
      itemObj.itemQuantity =
        ripCodeGreatestValueObj.usedConversion == true
          ? Math.round(itemObj.convertedItemQuantity*ripCodeGreatestValueObj.countAs)
          : Math.round(itemObj.itemQuantity*ripCodeGreatestValueObj.countAs);
    } else {
      //If this item by itself doesn't fit into any tier level - add the rip code to the obj because maybe once grouped with a diff item, it will fit
      itemObj.bestRipCode = itemObj.rebateDetailArr[0].ripcode;
      itemObj.agreementDetailId = itemObj.rebateDetailArr[0].rebatedetail;
      itemObj.tierGroupInternalId = itemObj.rebateDetailArr[0].tiergroup;
    }

    //Deleting the rebateDetailArr - which had all rebate options, because we already got the best match
    // @ts-ignore The operand of a 'delete' operator must be optional.ts(2790)
    delete itemObj.rebateDetailArr;
  }

  /**
   * Create a rip code and items mapping object
   *
   * @param {RipItem[]} itemsWithRipInfoObjArr
   * @returns array of grouped together items
   */
  function groupTogetherItemsBasedonRipCodes(itemsWithRipInfoObjArr) {
    let ripCodesWithTotalsArr = [];
    ripCodesWithTotalsArr = _.chain(itemsWithRipInfoObjArr)
      .groupBy("agreementDetailId")
      .map(function (/** @type {RipItem[]} */ ripInfoObjs, /** @type {number} */ agreementDetailId) {
        return {
          agreementId: agreementDetailId,
          totalQuantity: _.sumBy(ripInfoObjs, "itemQuantity"),
          tierGroupInternalId: ripInfoObjs[0].tierGroupInternalId,
          ripCode: ripInfoObjs[0].bestRipCode,
          unitsType: ripInfoObjs[0].unitsType,
          //Creating an array of the items that are part of this rip code
          itemIds: ripInfoObjs.map((itemObj) => ({
            itemId: itemObj.itemId,
            itemQuantity: itemObj.itemQuantity,
            unitUsedForRip:
              itemObj.usedConversion == true
                ? itemObj.unitsType == Unit.BOTTLE
                  ? Unit.CASE
                  : Unit.BOTTLE
                : itemObj.unitsType,
            billId: itemObj.billId,
          })),
        };
      })
      .value();
    return ripCodesWithTotalsArr;
  }

  /**
   *
   * @param {object} ripCodeObj
   * @returns boolean true or false
   */
  function checkIfReachedMinTierLevel(ripCodeObj, onlyReturnBooleanNoDetails) {
    const orQuantityCheckedAndIsBottle = ripCodeObj.unitsType == Unit.CASE
      ? `OR (
        custrecord_tier_quantity < = ${ripCodeObj.totalQuantity}
        AND custrecord_unit_of_measure = ${Unit.BOTTLE}
      )`
      : "";

    const fromTierGroupWhereQuantitiesChecked = `
      FROM
        customrecord_rebate_tier_group
      WHERE
        id = ${ripCodeObj.tierGroupInternalId}
        AND (
          (custrecord_tier_quantity < = ${ripCodeObj.totalQuantity} AND custrecord_unit_of_measure = ${ripCodeObj.unitsType})
          ${orQuantityCheckedAndIsBottle}
        )
    `;

    const tierLevelIdQuery = `SELECT
        MAX(custrecord_dollar_off) AS "custrecord_dollar_off",
        MAX(id) AS "id",
        MAX(custrecord_tier_quantity) AS "tier_minimum"
      FROM
        customrecord_rebate_tier_level
      WHERE
        id IN (SELECT custrecord_tier_level_1 ${fromTierGroupWhereQuantitiesChecked})
        OR Id IN (SELECT custrecord_tier_level_2 ${fromTierGroupWhereQuantitiesChecked})
        OR id IN (SELECT custrecord_tier_level_3 ${fromTierGroupWhereQuantitiesChecked})
        OR id IN (SELECT custrecord_tier_level_4 ${fromTierGroupWhereQuantitiesChecked})
        OR id IN (SELECT custrecord_tier_level_5 ${fromTierGroupWhereQuantitiesChecked})
    `;

    const tierLevelObj = _runSqlQuery(tierLevelIdQuery);

    if (tierLevelObj.custrecord_dollar_off > 0) {
      //If we don't need the full object - add on to the passed in obj the needed properties, otherwise return the whole obj
      if (onlyReturnBooleanNoDetails) {
        ripCodeObj.amountOff = tierLevelObj.custrecord_dollar_off;
        ripCodeObj.tierLevelId = tierLevelObj.id;
        return true;
      } else {
        return tierLevelObj;
      }
    } else {
      return false;
    }
  }

  /**
   *
   * @param {object} ripCodeObj
   */
  function createNewAccrualRecord(ripCodeObj) {
    const newAccrualRecord = record.create({
      type: "customrecord_rip_po_rip_code_accrual",
    });
    const vendorBillName = search.lookupFields({
      type: search.Type.VENDOR_BILL,
      id: ripCodeObj.itemIds[0].billId,
      //TODO: confirm ok to set accrual bill link as one of the possibly many bil
      columns: ["transactionnumber"],
    })["transactionnumber"];
    const acrrualRecordObj = {
      name: "RIP Accrual #" + ripCodeObj.ripCode + " for " + vendorBillName,
      custrecord_rip_code_from_agreement: ripCodeObj.ripCode,
      custrecord_rebate_agreement_detail_recor: ripCodeObj.agreementId,
      custrecord_vendor_bill_accruing_for: ripCodeObj.itemIds[0].billId,
      custrecord_highest_tier_level_reached: ripCodeObj.tierLevelId,
      custrecord_total_quantity_received: ripCodeObj.totalQuantity,
      custrecord_rip_code_tier_group: ripCodeObj.tierGroupInternalId,
    };
    recordHelperLib.setBodyValues(acrrualRecordObj, newAccrualRecord);
    ripCodeObj.vendorBillName = vendorBillName;
    const newAccrualRecordId = newAccrualRecord.save({
      ignoreFieldChange: true,
      ignoreMandatoryFields: false,
    });
    ripCodeObj.accrualRecordId = newAccrualRecordId;
    //   newAccrualRecordsCreated.push(newAccrualRecordId);
    return newAccrualRecordId;
  }

  /**
   *
   * @param {object} ripCodeObj
   */
  function createTierQuantityRecords(ripCodeObj) {
    //Creating a record with the quantity, tier and amount off for each tier level
    let totalAmountOwed = 0;
    //Adding up the total amount owed for this rip code, so we can use it for vendor credit
    function _createSingleRecord() {
      //For sure try to create one record, then see if more are needed
      const tierLevelObj = checkIfReachedMinTierLevel(ripCodeObj, false);

      if (tierLevelObj.custrecord_dollar_off > 0) {
        const createTierQtyRecordObj = {
          custrecord_incremented_tier_level: tierLevelObj.id,
          custrecord_quantity_used_for_tier_level: tierLevelObj.tier_minimum,
          name:
            tierLevelObj.custrecord_dollar_off +
            " $ -" +
            tierLevelObj.tier_minimum,
          custrecord_rip_code_accrual_per_bill_rec: ripCodeObj.accrualRecordId,
        };

        totalAmountOwed = totalAmountOwed + tierLevelObj.custrecord_dollar_off;
        const newTierQuantityRecord = createRipRecordLib.createRipTierRecord(
          createTierQtyRecordObj
        );
        ripCodeObj.totalQuantity =
          ripCodeObj.totalQuantity - tierLevelObj.tier_minimum;

        //Update the leftover quantity and try again to see if another new tier quantity record is needed
        if (ripCodeObj.totalQuantity > 0) {
          _createSingleRecord();
        }
        return newTierQuantityRecord;
      }
    }
    _createSingleRecord(ripCodeObj);
    ripCodeObj.totalAmountForCredit = totalAmountOwed;
  }

  /**
   *
   * @param {object} vendorBill
   * @param {object} ripInfoObj
   */
  function setRipAccrualLinkAndCodePerItemOnBill(ripInfoObj) {
    ripInfoObj.itemIds.forEach((itemObj) => {
      loadedBill = record.load({
        type: record.Type.VENDOR_BILL,
        id: itemObj.billId,
      });
      const lineNumber = loadedBill.findSublistLineWithValue({
        sublistId: "item",
        fieldId: "item",
        value: itemObj.itemId,
      });
      loadedBill.setSublistValue({
        sublistId: "item",
        fieldId: "custcol_rip_accrual_record_link",
        line: lineNumber,
        value: ripInfoObj.accrualRecordId,
      });
      loadedBill.setSublistValue({
        sublistId: "item",
        fieldId: "custcol_best_rip_code",
        line: lineNumber,
        value: ripInfoObj.ripCode,
      });
      loadedBill.save({
        ignoreFieldChange: true,
        ignoreMandatoryFields: true,
      });
    });
  }

  /**
   *
   * @param {object} ripInfoObj
   * @returns {object}
   */
  function splitTotalsbyItem(ripInfoObj) {
    ripInfoObj.amountPerUnit =
      ripInfoObj.totalAmountForCredit / ripInfoObj.originalTotalQuantity;
    ripInfoObj.itemIds.forEach(
      (item) =>
        (item.totalCredit = item.itemQuantity * ripInfoObj.amountPerUnit)
    );
    return ripInfoObj;
  }

  /**
   *
   * @param {array} billIdsArr
   * @returns {object}
   */
  function generateRipsOnMultipleBills(billIdsArr) {
    const totalItemObjsArr = [];
    const deletedRipRecordsObjArr = [];
    //#region Checking for Applicable Rips
    billIdsArr.forEach((billId) => {
      const vendorCreditId = search.lookupFields({
        type: search.Type.VENDOR_BILL,
        id: billId,
        columns: ["custbody_associated_bill_credit"],
      })["custbody_associated_bill_credit"][0].value;

      //Delete related rip records before re-running
      const deletedRipRecordsObj = createRipRecordLib.deleteRipRecords(
        billId,
        vendorCreditId,
        true
      );
      if (deletedRipRecordsObj) {
        deletedRipRecordsObjArr.push(deletedRipRecordsObj);
      }

      totalItemObjsArr.push(getItemObjs(billId));
    });

    var itemsWithRipCodeArr = totalItemObjsArr
      .flat(1)
      .filter((itemObj) => checkIfAvailRip(itemObj));
    if (itemsWithRipCodeArr && itemsWithRipCodeArr.length > 0) {
      itemsWithRipCodeArr.forEach((itemObj) => getBestRipLevel(itemObj));
      var ripCodeWithQtyArr =
        groupTogetherItemsBasedonRipCodes(itemsWithRipCodeArr);

      ripCodeWithQtyArr = ripCodeWithQtyArr.filter((ripCodeObj) =>
        checkIfReachedMinTierLevel(ripCodeObj, true)
      );

      ripCodeWithQtyArr.forEach(
        (ripCodeObj) =>
          (ripCodeObj.originalTotalQuantity = ripCodeObj.totalQuantity)
      );
      //#endregion

      //#region Creating New Records
      var newAccrualRecordsCreated = [];

      ripCodeWithQtyArr.forEach((itemObj) =>
        newAccrualRecordsCreated.push(createNewAccrualRecord(itemObj))
      );

      ripCodeWithQtyArr.forEach((itemObj) =>
        createTierQuantityRecords(itemObj)
      );
      //#endregion
    }

    //#region Setting Fields on Bills and Creating Bill Credit
    if (newAccrualRecordsCreated && newAccrualRecordsCreated.length > 0) {
      ripCodeWithQtyArr.forEach((ripInfoObj) => {
        setRipAccrualLinkAndCodePerItemOnBill(ripInfoObj);
      });

      ripCodeWithQtyArr.forEach((ripInfoObj) => splitTotalsbyItem(ripInfoObj));

      itemSublistArr = [];

      ripCodeWithQtyArr.forEach((ripInfoObj) =>
        ripInfoObj.itemIds.forEach((itemObj) => {
          itemSublistArr.push({
            item: "44228",
            rate: ripInfoObj.amountPerUnit,
            quantity: itemObj.itemQuantity,
            description: `Rip received for rip code ${ripInfoObj.ripCode} and total credit for this code was ${ripInfoObj.totalAmountForCredit}`, //;for items: ${ripInfoObj.itemIds.itemId.join(", ")}`,
            custcol_rip_item_link: itemObj.itemId,
            custcol_rip_total_quantity_of_item: itemObj.itemQuantity,
            custcol_rip_agreement_detail_record: ripInfoObj.agreementId,
            custcol_unit_used_for_rip: itemObj.unitUsedForRip,
          });
        })
      );

      const vendorBill = record.load({
        type: record.Type.VENDOR_BILL,
        id: ripCodeWithQtyArr[0].itemIds[0].billId,
      });

      const totalItemIdObjsArr = [];
      ripCodeWithQtyArr.forEach((ripInfoObj) =>
        ripInfoObj.itemIds.forEach((obj) => totalItemIdObjsArr.push(obj))
      );
      var uniqueBillsArr = [
        ...new Set(totalItemIdObjsArr.map((item) => item.billId)),
      ];

      //Setting values on teh vendor credit obj, based on the first vendor bill
      const vendorCreditObj = {
        entity: vendorBill.getValue("entity"),
        subsidiary: vendorBill.getValue("subsidiary"),
        account: 112,
        trandate: vendorBill.getValue("trandate"),
        custbody_associated_vendor_bills: uniqueBillsArr,
        tranid: `RIP${vendorBill.getValue("transactionnumber")}`,
        custbody_rip_vendor_exclude_from_ar: true,
      };
      var newVendorCredit = createRipRecordLib.createVendorCredit(
        vendorCreditObj,
        itemSublistArr
      );

      uniqueBillsArr.forEach((billId) =>
        record.submitFields({
          type: record.Type.VENDOR_BILL,
          id: billId,
          values: {
            custbody_associated_bill_credit: newVendorCredit,
            custbody_is_part_of_grouped_bills: true,
          },
          options: {
            enableSourcing: false,
            ignoreMandatoryFields: true,
          },
        })
      );
    }
    //#endregion
    return {
      deletedRipRecords: deletedRipRecordsObjArr,
      updatedBillsArr: uniqueBillsArr,
    };
  }

  return { generateRipsOnMultipleBills };
});
