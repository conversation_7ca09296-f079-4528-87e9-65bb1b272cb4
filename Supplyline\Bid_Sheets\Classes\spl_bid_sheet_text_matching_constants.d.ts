/**
 * TypeScript definitions for text matching constants
 * Contains type definitions for filler words, unit replacements, dimension and packaging patterns
 *
 * <AUTHOR>
 */

/**
 * Common filler words to be removed during normalization
 */
export const FILLER_WORDS: Set<string>;

/**
 * Replacements for abbreviations, shortcuts, and other variations
 */
export const UNIT_REPLACEMENTS: {
    // Common abbreviations
    'w/': string;
    'f/': string;

    // Dimension labels
    'l': string;
    'w': string;
    'h': string;
    'd': string;

    // Length/Distance units
    'in': string;
    'in.': string;
    '"': string;
    '\'\'': string;
    'ft': string;
    'ft.': string;
    '\'': string;
    'yd': string;
    'yd.': string;
    'mm': string;
    'cm': string;
    'm': string;
    'km': string;
    'mi': string;

    // Area and Volume descriptors
    'sq': string;
    'sq.': string;
    'cu': string;
    'cu.': string;

    // Geometric measurements
    'dia': string;
    'dia.': string;
    'ø': string;
    'rad': string;
    'rad.': string;
    'r': string;
    'r.': string;

    // Quantity units
    'pc': string;
    'pcs': string;
    'ea': string;
    'pk': string;
    'pk.': string;
    'pkg': string;
    'bx': string;
    'ctn': string;
    'cs': string;
    'dz': string;
    'pr': string;
    'st': string;
    'bdl': string;
    'rl': string;
    'un': string;

    // Volume units - Liquid
    'fl oz': string;
    'fl. oz.': string;
    'fl.oz.': string;
    'floz': string;
    'ml': string;
    'gal': string;
    'qt': string;
    'pt': string;

    // Volume units - Cubic
    'cu in': string;
    'cu ft': string;
    'cu yd': string;
    'cc': string;
    'cu m': string;

    // Weight units
    'oz': string;
    'oz.': string;
    'lb': string;
    'lb.': string;
    'lbs': string;
    'g': string;
    'mg': string;
    'kg': string;
    't': string;
    'mt': string;

    // Allow for additional keys
    [key: string]: string;
};

/**
 * Set of standardized unit terms
 */
export const UNITS: Set<string>;

/**
 * Interface for packaging pattern objects
 */
export interface PackagingPattern {
    /** Regular expression pattern to match */
    pattern: RegExp;
    /** Replacement string with capture groups */
    replacement: string;
}

/**
 * Packaging information patterns to standardize unit conversion ratios
 */
export const PACKAGING_PATTERNS: PackagingPattern[];
