/**
 * @description Admin tools for bid sheet review functionality
 *
 * @NApiVersion 2.1
 *
 * <AUTHOR>
 * @module spl_bid_sheet_review_admin_tools_class
 */
define([
    "exports",
    "../../../../Classes/vlmd_custom_error_object",
    "N/record",
    "N/query",
], (
    /** @type {any} */ exports,
    /** @type {any} */ CustomErrorObject,
    /** @type {any} */ record,
    /** @type {any} */ query
) => {

    class BidSheetReviewAdminTools {
        constructor() {
            this.customErrorObject = new CustomErrorObject();
        }

        /**
         * Clear all verified and overridden items
         * @param {string} bidSheetId - The ID of the bid sheet
         * @returns {Object} - Result of the operation
         */
        clearVerifiedAndOverridenItems(bidSheetId) {
            try {
                // Get all bid sheet items with verified matches
                const bidSheetItemsQuery = `
                    SELECT
                        id
                    FROM
                        customrecord_spl_bid_sheet_items
                    WHERE
                        custrecord_spl_bsi_bid_sheet = ? AND
                        (custrecord_spl_bsi_item_match IS NOT NULL OR
                        custrecord_spl_bsi_match_override = 'T' OR
                        custrecord_spl_bsi_skipped = 'T')
                `;

                const bidSheetItems = query.runSuiteQL({
                    query: bidSheetItemsQuery,
                    params: [bidSheetId]
                }).asMappedResults();

                // Clear the item match field for each item
                for (let item of bidSheetItems) {
                    record.submitFields({
                        type: 'customrecord_spl_bid_sheet_items',
                        id: item.id,
                        values: {
                            custrecord_spl_bsi_item_match: '',
                            custrecord_spl_bsi_match_override: false,
                            custrecord_spl_bsi_skipped: false
                        }
                    });
                }

                return {
                    success: true,
                    clearedItems: bidSheetItems.length
                };
            } catch (err) {
                this.customErrorObject.throwError({
                    summaryText: `ERROR_IN_CLEAR_VERIFIED_AND_OVERRIDEN_ITEMS`,
                    error: err
                });
                throw err;
            }
        }
    }

    // Export the class
    exports.BidSheetReviewAdminTools = BidSheetReviewAdminTools;

    return BidSheetReviewAdminTools;
});
