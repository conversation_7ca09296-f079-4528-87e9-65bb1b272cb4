/**
 * @description Sets the order source custom field value and removes the order source line on the order coming from LS.
 * @NApiVersion 2.1
 * @NScriptType Suitelet
 * <AUTHOR>
 * @module ltspd_wf_order_src
 */

define([
	"require",
	"N/log",
    "N/query",
    "N/record",
	"../../Classes/vlmd_custom_error_object",
], function (require, log) {
	/** @type {import("../../Classes/vlmd_custom_error_object").CustomErrorObject} */
    const CustomErrorObject = require("../../Classes/vlmd_custom_error_object");
	const customErrorObject = new CustomErrorObject();
    const query = require("N/query");
    const record = require("N/record");

    const searchNSCustomerViaPhone = (queryString) => {
        const sqlQuery = `SELECT customer.id, customer.entityid, customer.phone, customer.companyName, customer.custentity_in8_vend_id
                        FROM customer 
                        WHERE 
                            phone LIKE '%${queryString}%' 
                            AND
                            isinactive = 'F'
                        `;

        return query.runSuiteQL({
            query: sqlQuery,
        }).asMappedResults();
    }

    const searchNSCustomerViaName = (queryString) => {
        const sqlQuery = `SELECT customer.id, customer.entityid, customer.phone, customer.companyName, customer.custentity_in8_vend_id
                        FROM customer 
                        WHERE 
                            companyName LIKE '%${queryString}%'
                            AND
                            isinactive = 'F'
                        `;

        return query.runSuiteQL({
            query: sqlQuery,
        }).asMappedResults();
    }

    const getNSCustomerRecord = (customerId) => {
        const sqlQuery = `SELECT customer.id, customer.entityid,customer.email, customer.phone, customer.companyName, customer.custentity_in8_vend_id
                        FROM customer 
                        WHERE 
                            customer.id = ${customerId}
                        `;
        return query.runSuiteQL({
            query: sqlQuery,
        }).asMappedResults();
    }

    const updateCustomerPhone = (customerId,phoneNumber) => {
        let updatedCustomerId = record.submitFields({
            type: record.Type.CUSTOMER,
            id: customerId,
            values: {
                phone: phoneNumber
            }
        });

        log.debug({
            title: 'updatedCustomerId',
            details: updatedCustomerId
        });

        return updatedCustomerId;
    }



	let onRequest = (context) => {
	    if (context.request.method === "POST") {
            const parsedJson = JSON.parse(context.request.body),
                useremail = parsedJson["user"]["email"],
                username = parsedJson["user"]["username"],
                storeName = parsedJson["retailer"]["domain_prefix"],
				retailStoreId = parsedJson["retailer_id"],
				lineItemsArr = parsedJson["sale"]["line_items"],
				saleId = parsedJson["sale"]["id"],
                saleCustomFields = parsedJson["sale"]["custom_fields"]
            log.debug({title: 'parsedJson',details: parsedJson});
            //Exit script if not specific user
            if (useremail != "<EMAIL>") {
                return;
            }

            let actionObj = {
                    actions: []
            };

            let requireCustomFieldObj = {
                type: "require_custom_fields",
                title: "Search Customer in NetSuite",
                entity: "sale",
                entity_id: saleId,
                message: 'Please Enter Customer Information',
                confirm_label: "Search",
                required_custom_fields: []
            }

            let confirmObj ={
                type: "stop",
                title: "No Customer Found ",
                dismiss_label: "Ok"
            }



            let noCustomer = saleCustomFields.find((obj) => obj.name == "no_customer");
            let searchType = saleCustomFields.find((obj) => obj.name == "customer_search_type");
            let customerPhoneQuery = saleCustomFields.find((obj) => obj.name == "customer_phone_query");
            let customerNameQuery = saleCustomFields.find((obj) => obj.name == "customer_name_query");
            let customerList = saleCustomFields.find((obj) => obj.name == "customer_searchresult_list");
            let customerPhoneObj = saleCustomFields.find((obj) => obj.name == "customer_phone");
            
            log.debug({title: 'noCustomer',details: noCustomer});
            log.debug({title: 'searchType',details: searchType});
            log.debug({title: 'customerPhoneQuery',details: customerPhoneQuery});
            log.debug({title: 'customerNameQuery',details: customerNameQuery});
            log.debug({title: 'customerList',details: customerList});
            
           if(customerList){
                log.debug({title: 'Action',details: 'Update Phone'});

                let selectedCustomerId = customerList.string_array_value[0];  
                let customerLookup = getNSCustomerRecord(selectedCustomerId);
                log.debug({title: 'customerLookup',details: customerLookup});


                if(customerLookup.length){
                    let customerName = customerLookup[0]?.companyname;
                    let customerPhone = customerLookup[0]?.phone;
                    let customerEmail = customerLookup[0]?.email || '';
                    let customerVendorId = customerLookup[0]?.custentity_in8_vend_id || '';

                    if(customerPhoneObj){

                        if(customerPhoneObj.string_value != customerPhone){
                            log.debug({title: 'Action',details: 'Update Customer Phone'});
                            log.debug({title: 'customerPhoneObj.string_value',details: customerPhoneObj.string_value});
                            let updatedCustomerId = updateCustomerPhone(selectedCustomerId,customerPhoneObj.string_value);
                            log.debug({title: 'updatedCustomerId',details: updatedCustomerId});
                        }
                    }
                    else
                    {
                        actionObj.actions.push({
                            type: "set_custom_field",
                            entity: "sale",
                            custom_field_name: "customer_email",
                            custom_field_value: customerEmail
                        });

                        actionObj.actions.push({
                            type: "set_custom_field",
                            entity: "sale",
                            custom_field_name: "customer_phone",
                            custom_field_value: customerPhone
                        });

                        requireCustomFieldObj.title = customerName;
                        requireCustomFieldObj.message = 'Email: '+customerEmail+'\nVendor ID: '+customerVendorId;
                        requireCustomFieldObj.required_custom_fields.push({
                            name: "customer_phone"
                        });
                        actionObj.actions.push(requireCustomFieldObj);
                    }
                }
            }
            else if(customerPhoneQuery){
                log.debug({title: 'Action',details: 'Search Customer Via Phone'});
                customerLookup = searchNSCustomerViaPhone(customerPhoneQuery.string_value);
                if(customerLookup.length){
                    let customerSearchResultListObj = {
                        name: "customer_searchresult_list",
                        values: [
                            {"value":"","title":""}
                        ]
                    }
                    for(let i = 0; i < customerLookup.length; i++){
                        customerSearchResultListObj.values.push({
                            value: (customerLookup[i].id).toString(),
                            title: customerLookup[i].companyname
                        });
                    }
                    requireCustomFieldObj.message = 'Enter Customer Phone Number';
                    requireCustomFieldObj.required_custom_fields.push(customerSearchResultListObj);
                    actionObj.actions.push(requireCustomFieldObj);
                }
                else
                {
                    // let customerNotFoundMessage = 'No Customer Found in NetSuite';
                    // confirmObj.message = customerNotFoundMessage,
                    // actionObj.actions.push(confirmObj);
                    requireCustomFieldObj.message = 'No Customer Found With Phone: '+customerPhoneQuery.string_value+' in NS';
                    requireCustomFieldObj.required_custom_fields.push({
                        name: "customer_phone_query"
                    });
                    actionObj.actions.push(requireCustomFieldObj);
                }
            }
            else if(customerNameQuery){
                log.debug({title: 'Action',details: 'Search Customer Via Name'});
                customerLookup = searchNSCustomerViaName(customerNameQuery.string_value);

                 if(customerLookup.length){
                    let customerSearchResultListObj = {
                        name: "customer_searchresult_list",
                        values: [
                            {"value":"","title":""}
                        ]
                    }
                    for(let i = 0; i < customerLookup.length; i++){
                        customerSearchResultListObj.values.push({
                            value: (customerLookup[i].id).toString(),
                            title: customerLookup[i].companyname
                        });
                    }

                    requireCustomFieldObj.message = 'Enter Customer Name';
                    requireCustomFieldObj.required_custom_fields.push(customerSearchResultListObj);
                    actionObj.actions.push(requireCustomFieldObj);
                }
                else
                {
                    // let customerNotFoundMessage = 'No Customer Found in NetSuite';
                    // confirmObj.message = customerNotFoundMessage,

                    // actionObj.actions.push(confirmObj);
                    requireCustomFieldObj.message = 'No Customer Found With Name: '+customerNameQuery.string_value+' in NS';
                    requireCustomFieldObj.required_custom_fields.push({
                        name: "customer_name_query"
                    });
                    actionObj.actions.push(requireCustomFieldObj);
                }
            }
            else if(!searchType){
                log.debug({title: 'Action',details: 'Display Search Type'});
                requireCustomFieldObj.required_custom_fields.push({
                    name: "customer_search_type",
                    values: [
                        {
                        "value": "phone",
                        "title": "Phone Number"
                        },
                        {
                        "value": "name",
                        "title": "Customer Name"
                        },
                        {
                        "value": "nocustomer",
                        "title": "No Customer"
                        }
                    ]
                });
                // requireCustomFieldObj.required_custom_fields.push({
                //     name: "no_customer"
                // });
                requireCustomFieldObj.message = 'Select Option';
                actionObj.actions.push(requireCustomFieldObj);
            }
            else if(searchType){
                log.debug({title: 'Action',details: 'Search Type Selected'});
                
                if(searchType.string_array_value.includes("nocustomer")){
                  //Do nothing
                }
                else if(searchType.string_array_value.includes("phone")){
                    requireCustomFieldObj.message = 'Enter Customer Phone Number';
                    requireCustomFieldObj.required_custom_fields.push({
                        name: "customer_phone_query"
                    });
                }
                else if(searchType.string_array_value.includes("name")){
                    requireCustomFieldObj.message = 'Enter Customer Name';
                    requireCustomFieldObj.required_custom_fields.push({
                        name: "customer_name_query"
                    });
                }

                actionObj.actions.push(requireCustomFieldObj);
            }
            
            const jsonActionObj = JSON.stringify(actionObj);
            log.debug({title: 'jsonActionObj',details: jsonActionObj});
			context.response.write(jsonActionObj);
        }
	}

	return {
		onRequest,
	};
});

