/**
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * <AUTHOR>
 * @description MR to delete Bounced Email Daily
 * @module vlmd_delete_bounced_email_mr
 */

define(['N/search','N/https'], function (search,https) {
	
    const getInputData = (getInputContext) => {

        let undeliveredemailSearchObj = search.create({
            type: "undeliveredemail",
            filters:
            [
                ["logdate",search.Operator.WITHIN,"today"]
            ],
            columns: ["recipients"]
        });

        return undeliveredemailSearchObj;
    }

    const map = (mapContext) => {
        log.debug({
            title: 'mapContext',
            details: mapContext
        });

        //nlapiServerCall('/app/crm/marketing/bouncedemailserver.nl', 'removeBounce', ["<EMAIL>"]);
        

        var response = https.post({
            url: '/app/crm/marketing/bouncedemailserver.nl',
            body: JSON.stringify({removeBounce: '<EMAIL>'})
        });

        return {

        }
    }

    const summarize = (summaryContext) => {
        summaryContext.mapContext.iterator().each();
    }
	
	return {
		getInputData,
		map,
		summarize,
	};
});
