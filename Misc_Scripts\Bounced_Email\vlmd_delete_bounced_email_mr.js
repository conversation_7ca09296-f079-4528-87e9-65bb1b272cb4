/**
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * <AUTHOR>
 * @description MR to delete Bounced Email Daily
 * @module vlmd_delete_bounced_email_mr
 */

define(['N/search','N/https'], function (search,https) {
	
    const getInputData = (getInputContext) => {

        let undeliveredemailSearchObj = search.create({
            type: "undeliveredemail",
            filters:
            [
                ["logdate",search.Operator.WITHIN,"today"]
            ],
            columns: ["recipients"]
        });

        return undeliveredemailSearchObj;
    }

    const map = (mapContext) => {

        log.debug({
            title: 'mapContext',
            details: mapContext
        });

        let mapContextValue = JSON.parse(mapContext.value);
        let emailObj = mapContextValue.values;

        log.debug({
            title: 'mapContext',
            details: mapContext
        });

        let recipient = emailObj.recipients;

        log.debug({
            title: 'recipient',
            details: recipient
        });

        //nlapiServerCall('/app/crm/marketing/bouncedemailserver.nl', 'removeBounce', ["<EMAIL>"]);
        

        // var response = https.post({
        //     url: 'https://5802576-sb1.app.netsuite.com/app/crm/marketing/bouncedemailserver.nl',
        //     body: JSON.stringify({
        //         method: 'removeBounce',
        //         params: [recipient]
        //     }),
        //     headers: {
        //         'Content-Type': 'application/json'
        //     }
        // });

        var response = https.post({
            url: 'https://5802576-sb1.app.netsuite.com/app/crm/marketing/bouncedemailserver.nl',
            body: 'removeBounce=' + encodeURIComponent(recipient)
        });
        

        log.debug({
            title: 'response',
            details: response
        });




        mapContext.write({
            key: emailObj.id,
            value: emailObj
        });
    }

    const summarize = (summaryContext) => {
        summaryContext.mapSummary.errors.iterator().each((key, error)=>{
            log.error({
                title: 'Map Error for key '+key,
                details: error
            });

            return true;
        });
    }
	
	return {
		getInputData,
		map,
		summarize,
	};
});
