 /**
 * @NApiVersion 2.1
 * @NScriptType ScheduledScript
 * @NModuleScope Public
 *
 * @description Scheduled Script for automatic Snowflake OAuth token refresh
 * <AUTHOR>
 */
define(["N/log", "N/email", "./snowflake_oauth_lib"], function (
  log,
  email,
  snowflakeOAuth
) {

  const errorMessages = [];

  /**
   * Send error notification email
   * @param {string} subject - Email subject
   * @param {string} errorMessage - Error message to include in email body
   */
  function sendErrorNotification(subject, errorMessage) {
    try {
      const emailBody = `
        <h2>Snowflake Token Refresh Error</h2>
        <p><strong>Error:</strong> ${errorMessage}</p>
        <p><strong>Script:</strong> Snowflake Token Refresh Scheduled Script</p>
        <p><strong>Time:</strong> ${new Date().toISOString()}</p>
        <p>Please investigate and take appropriate action to ensure Snowflake connectivity is maintained.</p>
      `;

      email.send({
        author: 223244, // Requests email
        recipients: ["<EMAIL>"],
        subject: subject,
        body: emailBody
      });

    } catch (emailError) {
      log.error('817c108d-d504-451d-8897-27fb04f492e6 : Failed to Send Error Email', {
        originalError: errorMessage,
        emailError: emailError.message
      });
    }
  }

  /**
   * Scheduled script entry point
   * @param {Object} context - Scheduled script context
   */
  function execute(context) {
    try {
      const refreshCheck = snowflakeOAuth.checkTokenRefreshNeeded(10);
      
      log.debug('Token Refresh Check', {
        needsRefresh: refreshCheck.needsRefresh,
        minutesUntilExpiration: refreshCheck.minutesUntilExpiration,
        expiration: refreshCheck.expiration
      });
      
      if (refreshCheck.needsRefresh) {
        log.audit('Token Refresh Required', 
          `Token expires in ${refreshCheck.minutesUntilExpiration} minutes. Attempting refresh...`);
        
        if (refreshCheck.tokenData && refreshCheck.tokenData[snowflakeOAuth.SNOWFLAKE_CONFIG.oauth.refreshTokenFieldId]) {
          const refreshToken = refreshCheck.tokenData[snowflakeOAuth.SNOWFLAKE_CONFIG.oauth.refreshTokenFieldId];
          
          const newTokenData = snowflakeOAuth.refreshAndStoreNewToken(refreshToken);
          
          if (newTokenData && newTokenData.token) {
            const verifyToken = snowflakeOAuth.getStoredOAuthToken();
            if (verifyToken && verifyToken.token) {
              log.audit('Token Verification Successful', 
                `New token verified and stored. Expires: ${verifyToken.expiration}`);
            } else {
              errorMessages.push(`New token was refreshed but verification of storage failed with refresh token: ${refreshToken}`);
              log.error('c2fcf940-e0b2-4973-80c7-a6498f341def : Token Verification Failed', errorMsg);
            }
          } else {
            errorMessages.push('Failed to refresh OAuth token. Manual intervention may be required.');
            log.error('e249e9e9-fa5c-4e18-a43c-20e290bde866 : Token Refresh Failed', errorMsg);
          }
        } else {
          errorMessages.push('Cannot refresh token - no refresh token found in stored data');
          log.error('14bc4e80-701b-401a-8028-645b6dabe164 : No Refresh Token Available', errorMsg);
        }
      } else {
        log.debug('Token Refresh Not Needed', 
          `Token is valid for ${refreshCheck.minutesUntilExpiration} more minutes`);
      }
      
    } catch (error) {
      errorMessages.push(`Scheduled token refresh failed: ${error.message}`);
      log.error('4d84e71a-16f5-4cc4-8294-d6cccb6bb23b : Scheduled Token Refresh Error', {
        name: error.name,
        message: error.message,
      });
    }
    if (errorMessages.length > 0) {
      const allErrors = errorMessages.join('\n');
      if (errorMessages.length > 1) {
        sendErrorNotification('Snowflake Token Refresh Errors',
          `Error(s) occurred during token refresh:\n\n${allErrors}`);
      }
    }
  }

  return {
    execute: execute,
  };

});