/**
 * Interfaces and types used for Bridge RIP Management
 */

/**
 * Tier level object
 */
export interface TierLevel {
    /** ID */
    id: number;
    /** Dollar amount */
    custrecord_dollar_off: number;
    /** Quantity Used */
    quantityUsed: number;
    /** Tier Level Quantity */
    custrecord_tier_quantity: number;
    /** Tier Level Unit of Measure */
    custrecord_unit_of_measure: number;
}

/**
 * Best tier level, contains the units
 */
export interface BestTierLevel {
    /** Tier Level ID */
    id: number;
    /** Dollar amount */
    custrecord_dollar_off: number;
    /** Tier Level Quantity */
    tier_minimum: number;
    /** Tier Level Unit of Measure */
    tier_units: number;
    /** Calculated Quantity */
    quantity_used: number;
}

export interface RebateDetail {
    /** Tier Group ID */
    tiergroup: number;
    /** Agreement Detail ID */
    rebatedetail: number;
    /** Agreement Detail Rip Code */
    ripcode: string;
    /** Count As */
    countas: number;
}

export interface RipQuantity {
    /** Quantity */
    quantity: number;
    /** Unit */
    unitUsedForRip: number;
}

export interface RipItem {
    /** Count As */
    countAs: number;
    /** ID */
    itemId: number;
    /** Quantity */
    itemQuantity: number;
    /** Total Credit */
    totalCredit?: number;
    /** Unit */
    unitUsedForRip: number;
}

export interface RipObjectCore {
    /** Agreement ID */
    agreementId?: number;
    /** Tier Level ID */
    tierLevelId?: number;
    /** Tier Group ID */
    tierGroupInternalId?: number;
    /** Unit of Measure type */
    unitsType: number;
}

/**
 * Items grouped into a single Rip they belong to
 */
export interface RipObjectSummary extends RipObjectCore {
    /** Items Object */
    itemIds: RipItem[];
    /** Accrual Internal ID */
    accrualRecordId?: number;
    /** Amount Per Unit */
    amountPerUnit?: number;
    /** Amount Off */
    amountOff?: number;
    /** Original Total Quantity */
    originalTotalQuantity?: number;
    /** Quantities */
    quantities: RipQuantity[];
    /** Rip Code */
    ripCode?: string;
    /** Total Amount For Credit */
    totalAmountForCredit?: number;
    /** Total Quantity */
    totalQuantity?: number;
}

/**
 * Item object with Rip Details
 */
export interface RipObject extends RipObjectCore {
    /** Agreement Detail ID */
    agreementDetailId?: number;
    /** Best Rip Code */
    bestRipCode?: string;
    /** Converted Quantity */
    convertedItemQuantity: number;
    /** Count as */
    countAs: number;
    /** Item ID */
    itemId: number;
    /** Quantity multiplied by effective count */
    itemQuantity: number;
    /** Associated Rebate Details */
    rebateDetailArr: RebateDetail[];
    /** Is conversion required */
    usedConversion?: boolean;
}

/**
 * Intermediate object containing Rip details used to determine the Rip code with the best value
 */
export interface MaximizedRipObject {
    /** Agreement Detail ID */
    agreementDetailId: number;
    /** Tier Level Dollar Amount */
    amountOff: number;
    /** Count as */
    countAs: number;
    /** RIP Code */
    ripCode: string;
    /** Tier Group ID */
    tierGroupInternalId: number;
    /** Tier Level ID */
    tierLevelId: number;
    /** Calculated Dollar Amount */
    totalAmountOff: number;
    /** Is conversion required */
    usedConversion: boolean;
}

export interface TierQuantityObj {
    /** Tier Level ID */
    custrecord_incremented_tier_level: number;
    /** Tier Level Quantity */
    custrecord_quantity_used_for_tier_level: number;
    /** Tier-Quantity Name '{amount} $ -{quantity}' */
    name: string;
    /** Accrual Record ID */
    custrecord_rip_code_accrual_per_bill_rec: number | undefined;
}