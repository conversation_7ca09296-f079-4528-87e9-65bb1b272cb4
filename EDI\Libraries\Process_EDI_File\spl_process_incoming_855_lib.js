/**
 * @NApiVersion 2.x
 */

//@ts-ignore
define([
  "N/log",
  "N/record",
  "N/search",
  "GetPurchaseOrderAddressLib",
  "ComparePoAckValues",
  "SetPurchaseOrderCorrections",
  "MoveFileLib",
  "ProcessPoAckEmailLib",
  "PushEdiEmailInfoToDBLib",
  "Numeral",
], function (
  log,
  record,
  search,
  getPurchaseOrderAddress,
  comparePoAcknowledgmentValuesLib,
  setPurchaseOrderCorrections,
  moveFileLib,
  processEmailLib,
  pushEdiEmailInfoToDBLib,
  numeral
) {
  function processPoAck(vendorData, poAck, fileName) {
    var helperFunctions = (function () {
      function _processFail(logMessage) {
        log.error({
          title: "Error",
          details: logMessage,
        });

        errorLog.push(logMessage);
      }

      function getPurchaseOrderInternalId() {
        try {
          var poSearch = search.create({
            type: "purchaseorder",
            filters: [
              ["mainline", "is", "T"],
              "AND",
              ["type", "anyof", "PurchOrd"],
              "AND",
              [
                "transactionnumbertext",
                "haskeywords",
                poAck?.purchaseOrderNumber,
              ],
            ],
          });
          var resultSet = poSearch.run();
          var result = resultSet.getRange({
            start: 0,
            end: 1,
          });

          let internalId = result[0]?.id;
          if (
            !internalId ||
            internalId == "" ||
            internalId == null ||
            internalId == undefined
          ) {
            throw `No internal id found for ${poAck?.purchaseOrderNumber}`;
          }

          return internalId;
        } catch (e) {
          _processFail(
            `Internal ID not gotten for EDI file ${fileName} Error: ${e}`
          );
        }
      }

      function loadPurchaseOrder() {
        try {
          let purchaseOrder = record.load({
            type: record.Type.PURCHASE_ORDER,
            id: purchaseOrderInternalId,
            isDynamic: true,
          });

          return purchaseOrder;
        } catch (e) {
          _processFail(
            `Purchase order not loaded for PO internal ID ${purchaseOrderInternalId}: ${e}`
          );
        }
      }

      function getPurchaseOrderObj() {
        return {
          purchaseOrderInternalId,
          purchaseOrderName,
          createdBy: purchaseOrderRecord.getText("recordcreatedby"),
          items: _getItems(),
          shippingAddress: _getPurchaseOrderShippingAddress(),
          subsidiary: purchaseOrderRecord.getValue("subsidiary"),
        };

        function _getItems() {
          var arr = [];

          try {
            var itemLineCount = purchaseOrderRecord.getLineCount({
              sublistId: "item",
            });

            for (var x = 0; x < itemLineCount; x++) {
              let itemInternalId = purchaseOrderRecord.getSublistValue({
                sublistId: "item",
                fieldId: "item",
                line: x,
              });

              var itemName = purchaseOrderRecord.getSublistText({
                sublistId: "item",
                fieldId: "item",
                line: x,
              });

              var quantity = purchaseOrderRecord.getSublistText({
                sublistId: "item",
                fieldId: "quantity",
                line: x,
              });
              quantity = numeral(quantity).format("0.00");

              var rate = purchaseOrderRecord.getSublistText({
                sublistId: "item",
                fieldId: "rate",
                line: x,
              });
              rate = numeral(rate).format("0.00");
              arr.push({ itemInternalId, itemName, rate, quantity });
            }
          } catch (e) {
            _processFail(`Items not gotten for ${purchaseOrderName} - ${e}`);
          }

          return arr;
        }

        function _getPurchaseOrderShippingAddress() {
          try {
            var isDropShip = purchaseOrderRecord.getValue("createdfrom");
            var address = getPurchaseOrderAddress.getPurchaseOrderAddress(
              isDropShip ?? false,
              purchaseOrderInternalId,
              purchaseOrderName
            );

            return address.address1.toUpperCase();
          } catch (e) {
            _processFail(
              `Shipping address not gotten for ${purchaseOrderName} Error: ${e}`
            );
          }
        }
      }

      function compareValues() {
        try {
          var logObj = comparePoAcknowledgmentValuesLib.compareValues(
            poAck,
            purchaseOrderObj,
            purchaseOrderInternalId,
            vendorData.purchasingSoftwareId
          );

          if (logObj.errorLog.length > 0) {
            errorLog = errorLog.concat(logObj.errorLog);
          }

          return logObj;
        } catch (e) {
          _processFail(
            `The values for ${purchaseOrderName} were not compared successfully. Error: ${e}`
          );
          return false;
        }
      }

      function createNoteRecordOnPurchaseOrder() {
        var errorCreatingNoteLog =
          setPurchaseOrderCorrections.createEdiNoteOnPurchaseOrder(
            purchaseOrderInternalId,
            errorLog
          );

        if (errorCreatingNoteLog.length > 0) {
          errorLog = errorLog.concat(errorCreatingNoteLog);
        }
      }

      function updatePurchaseOrderAndSave() {
        try {
          if (logObj.rejectedItems.length <= 0 && errorLog.length <= 0) {
            purchaseOrderRecord.setValue(
              "custbody_spl_prscd_func_ack_suc",
              true
            );

            purchaseOrderRecord.save({
              enableSourcing: true,
              ignoreMandatoryFields: true,
            });
          }
        } catch (e) {
          _processFail(
            `PO ${purchaseOrderName} not updated successfully. Error: ${e}`
          );
        }
      }

      function moveFile() {
        var moveErrorLog = moveFileLib.moveFile(
          vendorData,
          fileName,
          errorLog.length <= 0
        );
        if (moveErrorLog.length > 0) {
          _processFail(`${fileName} not moved. Error: ${moveErrorLog}`);
        }
      }

      function sendEmail() {
        return processEmailLib.processEmail(
          logObj,
          errorLog,
          vendorData.vendorName,
          purchaseOrderObj,
          fileName
        );
      }

      return {
        getPurchaseOrderInternalId,
        loadPurchaseOrder,
        getPurchaseOrderObj,
        compareValues,
        createNoteRecordOnPurchaseOrder,
        updatePurchaseOrderAndSave,
        moveFile,
        sendEmail,
      };
    })();

    var errorLog = [];

    try {
      var purchaseOrderInternalId =
        helperFunctions.getPurchaseOrderInternalId();
      var purchaseOrderRecord = helperFunctions.loadPurchaseOrder();

      if (!purchaseOrderRecord)
        throw new Error(
          `No purchase order record gotten for ${purchaseOrderInternalId}`
        );

      var purchaseOrderName = purchaseOrderRecord.getValue("tranid");

      var purchaseOrderObj = helperFunctions.getPurchaseOrderObj();

      var logObj = helperFunctions.compareValues();
      helperFunctions.createNoteRecordOnPurchaseOrder();
      helperFunctions.updatePurchaseOrderAndSave();
      helperFunctions.moveFile();
      var sentEmailObj = helperFunctions.sendEmail();

      if (vendorData.pushEmailToDB) {
        pushEdiEmailInfoToDBLib.pushEdiEmailInfoToDB(
          vendorData,
          sentEmailObj,
          fileName,
          poAck.purchaseOrderNumber
        );
      }
    } catch (err) {
      errorLog.push(err);
      log.error("Error processing PO Ack", errorLog.join("\n"));
    }
  }

  return {
    processPoAck: processPoAck,
  };
});
