/**
 * @description  Loops through items on the bill and if applicable, creates all RIP records needed
 *
 * </br><b>Deployed On:</b> BRDG Vendor Bills
 * </br><b>Execution Context:</b> ALL
 * </br><b>Event Type/Mode:</b> ALL
 * </br><b>Entry Points:</b> beforeSubmit, afterSubmit
 *
 *
 * @NApiVersion 2.1
 * @NScriptType UserEventScript
 * @NAmdConfig /SuiteScripts/config.json
 *
 * <AUTHOR>
 * @module brdg_rip_create_records_ue
 *
 */

define([
  "require",
  "LoDash",
  "N/log",
  "N/query",
  "N/record",
  "N/error",
  "N/redirect",
  "N/runtime",
  "N/format",
  "N/search",
  "../Libraries/brdg_rip_records_helper_lib",
  "../../Libraries/brdg_helper_functions_lib",
  "../../../Helper_Libraries/vlmd_record_module_helper_lib",
], (/** @type {any} */ require, /** @type {any} */ _) => {
  const log = require("N/log");
  const record = require("N/record");
  const runtime = require("N/runtime");
  const query = require("N/query");
  const redirect = require("N/redirect");
  const error = require("N/error");
  const format = require("N/format");
  const search = require("N/search");
  const newAccrualRecordsCreated = [];

  const Unit = {
    BOTTLE: 1,
    CASE: 2,
    GROUP: 3,
  };

  /**@type {import ("../Libraries/brdg_rip_records_helper_lib")}} */
  const createRipRecordLib = require("../Libraries/brdg_rip_records_helper_lib");
  /**@type {import ("../../Libraries/brdg_helper_functions_lib")}} */
  const bridgeHelperFunctionsLib = require("../../Libraries/brdg_helper_functions_lib");
  /**@type {import ("../../../Helper_Libraries/vlmd_record_module_helper_lib")}} */
  const recordHelperLib = require("../../../Helper_Libraries/vlmd_record_module_helper_lib");

  const ripVendorsObj = {
    Allied: "13694",
  };

  /**
   *
   * @param {string} messageName
   * @param {string} messageDetails
   * @param {boolean} leaveScript
   */
  function _handleErrors(messageName, messageDetails, leaveScript) {
    if (leaveScript == true) {
      throw error.create({
        name: messageName,
        message: messageDetails,
      });
    }
  }

  /**
   * Return item object with quantities and units of measure
   *
   * @typedef {Object} RipItem
   * @property {number} itemId ID
   * @property {number} itemQuantity Quantity multiplied by effective count
   * @property {number} convertedItemQuantity Converted Quantity
   * @property {number} unitsType Unit of Measure type
   * @property {RebateDetail[]} rebateDetailArr Associated Rebate Details
   * @property {string} bestRipCode Best Rip Code
   * @property {number} agreementDetailId Agreement Detail ID
   * @property {number} tierLevelId Tier Level ID
   * @property {number} tierGroupInternalId Tier Group ID
   * @property {boolean} usedConversion Is conversion required
   *
   * @param {import("N/record").Record} vendorBill
   * @param {number} lineCount
   * @returns {RipItem[]|undefined} Item object
   */
  function getItemsInfo(vendorBill, lineCount) {
    const itemObjArr = [];
    try {
      for (var i = 0; i < lineCount; i++) {
        const isComponentItem = vendorBill.getSublistValue({
          sublistId: "item",
          fieldId: "ingroup",
          line: i,
        });

        if (isComponentItem == "T") {
          continue;
        }

        const itemId = Number(vendorBill.getSublistValue({
          sublistId: "item",
          fieldId: "item",
          line: i,
        }));

        const itemQuantity = Number(vendorBill.getSublistValue({
          sublistId: "item",
          fieldId: "quantity",
          line: i,
        }) || 0);

        const itemType = vendorBill.getSublistValue({
          sublistId: "item",
          fieldId: "itemtype",
          line: i,
        })?.toString() || "";

        //When item is a group item - pull the unit from the component which is on the next line
        const itemUnits = vendorBill.getSublistValue({
          sublistId: "item",
          fieldId: "units_display",
          line: i,
        })?.toString() || "";

        //If the purchase unit includes CS, it first gets a CASE rebate check, otherwise a bottle rebate check
        const unitsType = itemType == "Group"
          ? Unit.GROUP
          : itemUnits.includes("CS") 
            ? Unit.CASE
            : Unit.BOTTLE;

        const convertedItemQuantity = unitsType === Unit.CASE
          ? Number(vendorBill.getSublistValue({
            sublistId: "item",
            fieldId: "unitconversionrate",
            line: i,
          })) * itemQuantity
          : 0;

        // Set default values for properties to be updated by getBestRipLevel
        const itemObj = {
          itemId,
          itemQuantity,
          convertedItemQuantity,
          unitsType,
          rebateDetailArr: [],
          bestRipCode: "",
          agreementDetailId: 0,
          tierLevelId: 0,
          tierGroupInternalId: 0,
          usedConversion: false,
        };

        itemObjArr.push(itemObj);
      }
      return itemObjArr;
    } catch (/** @type {any} */ err) {
      _handleErrors("Error getting items objects", err.message, true);
    }
  }

  /**
   * Sets the rebateDetailArr property of the item object to the QueryResultMap[].
   * Returns a boolean based on the availability of a rip.
   *
   * @typedef {Object} RebateDetail
   * @property {number} tiergroup Tier Group ID
   * @property {number} rebatedetail Agreement Detail ID
   * @property {string} ripcode Agreement Detail Rip Code
   * @property {number} countas Count As
   *
   * @param {RipItem} itemObj Object containing item ID
   * @param {Date} billDate Vendor Bill Date
   * @param {number} billVendor Vendor ID assigned to the Bill
   * @returns {boolean} Returns true if there is an available rip
   */
  function checkIfAvailRip(itemObj, billDate, billVendor) {
    const sqlQuery = `
      SELECT
        tg.id tiergroup,
        cr.id rebatedetail,
        cr.custrecord_rip_code ripcode,
        COALESCE(ci.custrecord_agreement_detail_item_count,1) countas
      FROM
        CUSTOMRECORD_REBATE_AGREEMENT_DETAIL cr
        JOIN customrecord_rebate_agreement ca ON cr.custrecord_rebate_parent = ca.id
        JOIN customrecord_rebate_tier_group tg ON cr.custrecord_tier_group = tg.id
        LEFT JOIN customrecord_rebate_agreement_detail_itm ci ON ci.custrecord_agreement_detail_item = ${itemObj.itemId}
          AND ci.custrecord_agreement_detail = cr.id
      WHERE
        BUILTIN.MNFILTER(
          cr.custrecord_rebate_items_included,
          'MN_INCLUDE',
          '',
          'FALSE',
          NULL,
          ${itemObj.itemId}
        ) = 'T'
        AND TO_DATE(?, 'MM/DD/YYYY') BETWEEN ca.custrecord_rebate_start_date
        AND ca.custrecord_rebate_end_date
        AND ca.custrecord_rebate_vendor = ${billVendor} 
    `;

    const resultIterator = query
      .runSuiteQL({
        query: sqlQuery,
        params: [billDate],
      })
      .asMappedResults();

    //Returns an array of all rebate details - the tiergroup, rebatedetail ID, the ripcode, and count as, and adds it to the item obj

    if (resultIterator.length > 0) {
      // @ts-ignore Type 'QueryResultMap[]' is not assignable to type 'RebateDetail[]'
      itemObj.rebateDetailArr = resultIterator;
      return true;
    } else {
      return false;
    }
  }

  /**
   * Get the RIP code that gives the item the best value.
   * Update the RipItem with amounts, agreement and tier information.
   *
   * @typedef {Object} MaximizedRipItem
   * @property {number} amountOff Tier Level Dollar Amount
   * @property {number} totalAmountOff Calculated Dollar Amount
   * @property {string} ripCode RIP Code
   * @property {number} agreementDetailId Agreement Detail ID
   * @property {number} tierLevelId Tier Level ID
   * @property {number} tierGroupInternalId Tier Group ID
   * @property {boolean} usedConversion Is conversion required
   * @property {number} countAs Count as
   *
   * @param {RipItem} itemObj
   * @returns {void}
   */
  function getBestRipLevel(itemObj) {
    //An item can have multiple rip codes, so now checking to see which code is best value
    let rebateDetailsArr = itemObj.rebateDetailArr;
    let /** @type {MaximizedRipItem[]} */ amountOffWithRipCodeArr = [];
    try {
      rebateDetailsArr.forEach(function (rebateDetailObj) {
        //Gets the best rip level - whichever is better, by case or by bottle
        const rebateDetailsObj = runTierLevelQuery(
          rebateDetailObj.tiergroup,
          rebateDetailObj.countas,
          itemObj
        );

        if (rebateDetailsObj && rebateDetailsObj.id != null) {
          //If a level will give money off based on the quantity
          amountOffWithRipCodeArr.push({
            amountOff: rebateDetailsObj.custrecord_dollar_off,
            totalAmountOff:
              rebateDetailsObj.custrecord_dollar_off *
              (rebateDetailsObj.quantity_used / rebateDetailsObj.tier_minimum),
            ripCode: rebateDetailObj.ripcode,
            agreementDetailId: rebateDetailObj.rebatedetail,
            tierLevelId: rebateDetailsObj.id,
            tierGroupInternalId: rebateDetailObj.tiergroup,
            usedConversion:
              rebateDetailsObj.tier_units == itemObj.unitsType ? false : true,
            countAs: rebateDetailObj.countas,
          });
        }
      });
    } catch (/** @type {any} */ err) {
      _handleErrors("Error getting rip level", err.message, true);
    }

    if (amountOffWithRipCodeArr && amountOffWithRipCodeArr.length > 0) {
      //If a rip code fits into different levels for different rip codes
      //Checking to see which rip code will give better value and returning that object
      let ripCodeGreatestValueObj = amountOffWithRipCodeArr.reduce((max, rip) =>
        max.totalAmountOff > rip.totalAmountOff ? max : rip
      );
      //Adding the rip code details to the item OBJ
      itemObj.bestRipCode = ripCodeGreatestValueObj.ripCode;
      itemObj.agreementDetailId = ripCodeGreatestValueObj.agreementDetailId;
      itemObj.tierLevelId = ripCodeGreatestValueObj.tierLevelId;
      itemObj.tierGroupInternalId = ripCodeGreatestValueObj.tierGroupInternalId;
      itemObj.usedConversion = ripCodeGreatestValueObj.usedConversion;
      itemObj.itemQuantity =
        ripCodeGreatestValueObj.usedConversion == true
          ? Math.round(itemObj.convertedItemQuantity*ripCodeGreatestValueObj.countAs)
          : Math.round(itemObj.itemQuantity*ripCodeGreatestValueObj.countAs);
    } else {
      //If this item by itself doesn't fit into any tier level - add the rip code to the obj because maybe once grouped with a diff item, it will fit
      itemObj.bestRipCode = itemObj.rebateDetailArr[0].ripcode;
      itemObj.agreementDetailId = itemObj.rebateDetailArr[0].rebatedetail;
      itemObj.tierGroupInternalId = itemObj.rebateDetailArr[0].tiergroup;
    }

    // Deleting the rebateDetailArr - which had all rebate options, because we already got the best match
    // @ts-ignore The operand of a 'delete' operator must be optional.ts(2790)
    delete itemObj.rebateDetailArr;
  }

  /**
   * Returns a single row
   *
   * @typedef {Object} BestTierLevel
   * @property {number} custrecord_dollar_off Dollar amount
   * @property {number} id Tier Level ID
   * @property {number} tier_minimum Tier Level Quantity
   * @property {number} tier_units Tier Level Unit of Measure
   * @property {number} quantity_used Calculated Quantity
   *
   * @param {number} tierGroupId Tier Group ID
   * @param {number} countAs Count multiplier
   * @param {RipItem} itemObj Item object
   * @returns {BestTierLevel} Tier Level with best value
   */
  function runTierLevelQuery(tierGroupId, countAs, itemObj) {
    const convertedUnitsType = itemObj.unitsType == Unit.BOTTLE ? Unit.CASE : Unit.BOTTLE;

    const { itemQuantity, convertedItemQuantity, unitsType } = itemObj;
    const itemCount = Math.round(itemQuantity*countAs);
    const convertedItemCount = Math.round(convertedItemQuantity*countAs);

    const whenConvertedUnitThenConvertQuantity = convertedItemQuantity
      ? `WHEN rtl.custrecord_tier_quantity <= ${convertedItemCount} AND rtl.custrecord_unit_of_measure = ${convertedUnitsType} THEN ${convertedItemCount}`
      : ` `;

    const orConvertedQuantityAndUnitsType = convertedItemQuantity
      ? `OR (rtl.custrecord_tier_quantity <= ${convertedItemCount} AND rtl.custrecord_unit_of_measure = ${convertedUnitsType})`
      : ``;

    const getBestTierLevelInfoSql = `
      SELECT
      TOP 1
        rtl.custrecord_dollar_off AS "custrecord_dollar_off",
        rtl.id AS "id",
        rtl.custrecord_tier_quantity AS "tier_minimum",
        rtl.custrecord_unit_of_measure AS "tier_units",
        CASE
          WHEN rtl.custrecord_tier_quantity <= ${itemCount} AND rtl.custrecord_unit_of_measure = ${unitsType} THEN ${itemCount}
          ${whenConvertedUnitThenConvertQuantity}
          ELSE 0
        END AS "quantity_used"
      FROM
        customrecord_rebate_tier_level rtl
      INNER JOIN customrecord_rebate_tier_group rtg ON (
        rtl.id = rtg.custrecord_tier_level_1
        OR rtl.id = rtg.custrecord_tier_level_2
        OR rtl.id = rtg.custrecord_tier_level_3
        OR rtl.id = rtg.custrecord_tier_level_4
        OR rtl.id = rtg.custrecord_tier_level_5
      )
      WHERE
        rtg.id = ${tierGroupId}
        AND (
          (rtl.custrecord_tier_quantity <= ${itemCount} AND rtl.custrecord_unit_of_measure = ${unitsType})
          ${orConvertedQuantityAndUnitsType}
        )
      ORDER BY custrecord_dollar_off desc


    `;

    // @ts-ignore Type 'QueryResultMap | null | undefined' is not assignable to type 'BestTierLevel'
    return runSqlQuery(getBestTierLevelInfoSql);
  }

  /**
   *
   * @param {string} sqlQuery
   * @returns results of query
   */
  function runSqlQuery(sqlQuery) {
    try {
      const sqlResults = query
        .runSuiteQL({
          query: sqlQuery,
        })
        .asMappedResults()[0];

      if (!sqlResults) {
        return null;
      }

      return sqlResults;
    } catch (/** @type {any} */ err) {
      _handleErrors("Error running sql", err.message, true);
    }
  }

  /**
   * Create a rip code and items mapping object
   *
   * @param {RipItem[]} itemsWithRipInfoObjArr
   * @returns array of grouped together items
   */
  function groupTogetherItemsBasedonRipCodes(itemsWithRipInfoObjArr) {
    let ripCodesWithTotalsArrNew = [];
    try {
      ripCodesWithTotalsArrNew = itemsWithRipInfoObjArr
        .reduce((acc, curr) => {
          const agreementDetailId = curr.agreementDetailId;
          const existingGroup = acc.find(
            (group) => group.agreementId === agreementDetailId
          );

          const quantityObj = {
            quantity: curr.itemQuantity,
            unitUsedForRip: curr.usedConversion
              ? curr.unitsType === Unit.BOTTLE
                ? Unit.CASE
                : Unit.BOTTLE
              : curr.unitsType,
          };

          if (existingGroup) {
            const existingQuantityObj = existingGroup.quantities.find(
              (obj) => obj.unitUsedForRip === quantityObj.unitUsedForRip
            );

            if (existingQuantityObj) {
              existingQuantityObj.quantity += curr.itemQuantity;
            } else {
              existingGroup.quantities.push(quantityObj);
            }

            existingGroup.itemIds.push({
              itemId: curr.itemId,
              itemQuantity: curr.itemQuantity,
              unitUsedForRip: curr.usedConversion
                ? curr.unitsType === Unit.BOTTLE
                  ? Unit.CASE
                  : Unit.BOTTLE
                : curr.unitsType,
            });
          } else {
            acc.push({
              agreementId: agreementDetailId,
              tierGroupInternalId: curr.tierGroupInternalId,
              ripCode: curr.bestRipCode,
              unitsType: curr.unitsType,
              quantities: [quantityObj],
              itemIds: [
                {
                  itemId: curr.itemId,
                  itemQuantity: curr.itemQuantity,
                  unitUsedForRip: quantityObj.unitUsedForRip,
                },
              ],
            });
          }

          return acc;
        }, [])
        .map((group) => ({
          ...group,
          itemIds: group.itemIds.sort((a, b) => a.itemId - b.itemId),
        }));
    } catch (e) {
      _handleErrors("Error grouping together rip codes", e.message, true);
    }
    return ripCodesWithTotalsArrNew;
  }

  /**
   *
   * @param {object} ripCodeObj
   * @returns boolean true or false
   */
  function checkIfReachedMinTierLevel(ripCodeObj, onlyReturnBooleanNoDetails) {
    let tierLevelObjsArr = [];
    ripCodeObj.quantities.forEach((unitQuantityObj) => {
      const checkQuantity = `custrecord_tier_quantity <= ${unitQuantityObj.quantity}`;

      const andCheckingRemainder = ripCodeObj.totalQuantity
        ? `AND custrecord_tier_quantity <= ${ripCodeObj.totalQuantity}`
        : "";

      const andCheckMatchingUnitsString = unitQuantityObj.unitUsedForRip != Unit.GROUP
        ? `AND custrecord_unit_of_measure = ${unitQuantityObj.unitUsedForRip}`
        : "";

      const andCheckingBothUnitsTypeString = unitQuantityObj.unitUsedForRip == Unit.CASE
        ? `OR (
            custrecord_tier_quantity <= ${unitQuantityObj.quantity} 
            AND custrecord_unit_of_measure = 1
          )`
        : "";

      const fromTierGroupWhereQuantitiesChecked = `
        FROM
          customrecord_rebate_tier_group
        WHERE
          id = ${ripCodeObj.tierGroupInternalId}
          AND ((${checkQuantity} ${andCheckingRemainder} ${andCheckMatchingUnitsString}) ${andCheckingBothUnitsTypeString})
      `;

      const tierLevelIdQuery = `SELECT TOP 1
          custrecord_dollar_off AS "custrecord_dollar_off",
          id, 
          custrecord_tier_quantity AS "tier_minimum"
        FROM
          customrecord_rebate_tier_level
        WHERE
          id IN (SELECT custrecord_tier_level_1 ${fromTierGroupWhereQuantitiesChecked})
          OR Id IN (SELECT custrecord_tier_level_2 ${fromTierGroupWhereQuantitiesChecked})
          OR Id IN (SELECT custrecord_tier_level_3 ${fromTierGroupWhereQuantitiesChecked})
          OR Id IN (SELECT custrecord_tier_level_4 ${fromTierGroupWhereQuantitiesChecked})
          OR Id IN (SELECT custrecord_tier_level_5 ${fromTierGroupWhereQuantitiesChecked})
        ORDER BY custrecord_dollar_off desc
      `;

      const tierLevelObj = runSqlQuery(tierLevelIdQuery);

      (ripCodeObj.unitsType = unitQuantityObj.unitUsedForRip),
        tierLevelObj &&
          tierLevelObj.custrecord_dollar_off > 0 &&
          ((tierLevelObj.quantityUsed = unitQuantityObj.quantity),
          tierLevelObjsArr.push(tierLevelObj));
    });

    if (tierLevelObjsArr.length > 0) {
      const tierLevelObj = tierLevelObjsArr.reduce((maxObj, currObj) =>
        currObj.custrecord_dollar_off > maxObj.custrecord_dollar_off
          ? currObj
          : maxObj
      );
      //If we don't need the full object - add on to the passed in obj the needed properties, otherwise return the whole obj
      if (onlyReturnBooleanNoDetails) {
        ripCodeObj.amountOff = tierLevelObj.custrecord_dollar_off;
        ripCodeObj.tierLevelId = tierLevelObj.id;
        ripCodeObj.totalQuantity = tierLevelObj.quantityUsed;
        return true;
      } else {
        return tierLevelObj;
      }
    } else {
      return false;
    }
  }

  /**
   *
   * @param {object} ripCodeObj
   */

  function createNewAccrualRecord(ripCodeObj, vendorBillId, vendorBillName) {
    const newAccrualRecord = record.create({
      type: "customrecord_rip_po_rip_code_accrual",
    });
    const acrrualRecordObj = {
      name: "RIP Accrual #" + ripCodeObj.ripCode + " for " + vendorBillName,
      custrecord_rip_code_from_agreement: ripCodeObj.ripCode,
      custrecord_rebate_agreement_detail_recor: ripCodeObj.agreementId,
      custrecord_vendor_bill_accruing_for: vendorBillId,
      custrecord_highest_tier_level_reached: ripCodeObj.tierLevelId,
      custrecord_total_quantity_received: ripCodeObj.totalQuantity,
      custrecord_rip_code_tier_group: ripCodeObj.tierGroupInternalId,
    };
    recordHelperLib.setBodyValues(acrrualRecordObj, newAccrualRecord);

    try {
      const newAccrualRecordId = newAccrualRecord.save({
        ignoreFieldChange: true,
        ignoreMandatoryFields: false,
      });
      ripCodeObj.accrualRecordId = newAccrualRecordId;
      newAccrualRecordsCreated.push(newAccrualRecordId);
      return newAccrualRecordId;
    } catch (e) {
      _handleErrors("Error creating new accrual record", e.message, true);
    }
  }

  /**
   *
   * @param {object} ripCodeObj
   */
  function createTierQuantityRecords(ripCodeObj) {
    try {
      //Creating a record with the quantity, tier and amount off for each tier level
      let totalAmountOwed = 0;
      //Adding up the total amount owed for this rip code, so we can use it for vendor credit
      function createSingleRecord() {
        //For sure try to create one record, then see if more are needed
        const tierLevelObj = checkIfReachedMinTierLevel(ripCodeObj, false);

        if (tierLevelObj.custrecord_dollar_off > 0) {
          const createTierQtyRecordObj = {
            custrecord_incremented_tier_level: tierLevelObj.id,
            custrecord_quantity_used_for_tier_level: tierLevelObj.tier_minimum,
            name:
              tierLevelObj.custrecord_dollar_off +
              " $ -" +
              tierLevelObj.tier_minimum,
            custrecord_rip_code_accrual_per_bill_rec:
              ripCodeObj.accrualRecordId,
          };

          totalAmountOwed =
            totalAmountOwed + tierLevelObj.custrecord_dollar_off;
          const newTierQuantityRecord = createRipRecordLib.createRipTierRecord(
            createTierQtyRecordObj
          );

          ripCodeObj.totalQuantity =
            ripCodeObj.totalQuantity - tierLevelObj.tier_minimum;

          //Update the leftover quantity and try again to see if another new tier quantity record is needed
          if (ripCodeObj.totalQuantity > 0) {
            createSingleRecord();
          }
          return newTierQuantityRecord;
        }
      }
      createSingleRecord(ripCodeObj);
      ripCodeObj.totalAmountForCredit = totalAmountOwed;
    } catch (e) {
      _handleErrors("Error creating new RIP tier record", e.message, true);
    }
  }

  function splitTotalsbyItem(ripInfoObj) {
    ripInfoObj.amountPerUnit =
      ripInfoObj.totalAmountForCredit / ripInfoObj.originalTotalQuantity;
    ripInfoObj.itemIds.forEach(
      (item) =>
        (item.totalCredit = item.itemQuantity * ripInfoObj.amountPerUnit)
    );
    return ripInfoObj;
  }

  /**
   *
   * @param {object} vendorBill
   * @param {object} ripInfoObj
   */
  function setRipAccrualLinkAndCodePerItemOnBill(billInEditMode, ripInfoObj) {
    ripInfoObj.itemIds.forEach((itemObj) => {
      try {
        const lineNumber = billInEditMode.findSublistLineWithValue({
          sublistId: "item",
          fieldId: "item",
          value: itemObj.itemId,
        });
        billInEditMode.setSublistValue({
          sublistId: "item",
          fieldId: "custcol_rip_accrual_record_link",
          line: lineNumber,
          value: ripInfoObj.accrualRecordId,
        });
        billInEditMode.setSublistValue({
          sublistId: "item",
          fieldId: "custcol_best_rip_code",
          line: lineNumber,
          value: ripInfoObj.ripCode,
        });
      } catch (e) {
        _handleErrors(
          "Error setting the rip accrual link on the bill",
          e.message,
          false
        );
      }
    });
  }

  function beforeSubmit(context) {
    const vendorBill = context.newRecord;
    const vendor = vendorBill.getValue("entity");
    if (!Object.values(ripVendorsObj).includes(vendor)) {
      return;
    }
    const subsidiariesArr = vendorBill.getValue({
      fieldId: "subsidiary",
    });
    const alreadyLinkedCredit = vendorBill.getValue({
      fieldId: "custbody_associated_bill_credit",
    });
    const approvalStatus = vendorBill.getValue({
      fieldId: "approvalstatus",
    });
    const bridgeSubsidiaries = bridgeHelperFunctionsLib.getBridgeSubsidiaries();
    const isBridgeSubsidiary = bridgeHelperFunctionsLib.isBridgeSubsidiary(
      subsidiariesArr,
      bridgeSubsidiaries
    );

    if (!isBridgeSubsidiary || approvalStatus != 2 || alreadyLinkedCredit) {
      return;
    }

    let ripInvoiceDate = vendorBill.getValue("custbody_rip_bill_invoice_date");
    if (!ripInvoiceDate) {
      //Adding in this part for now, as per Esther because too many bills were giving errors when missing the rip invoice date
      ripInvoiceDate = vendorBill.getValue("trandate");
      // _handleErrors("Error getting dates on invoice", `Missing rip invoice date on this bill:  ${vendorBill.id}`, true);
    }

    const formattedBillDate = format.format({
      value: ripInvoiceDate,
      type: format.Type.DATE,
    });
    const billVendor = vendorBill.getValue("entity");
    const lineCount = vendorBill.getLineCount({ sublistId: "item" });
    const itemObjsArr = getItemsInfo(vendorBill, lineCount);
    //** Getting/setting information for each individual item **//
    let /** @type {RipItem[]} */ itemsWithRipCodeArr = [];
    if (itemObjsArr && itemObjsArr.length > 0) {
      itemsWithRipCodeArr = itemObjsArr.filter((itemObj) =>
        checkIfAvailRip(itemObj, formattedBillDate, billVendor)
      );
    }

    if (itemsWithRipCodeArr && itemsWithRipCodeArr.length > 0) {
      itemsWithRipCodeArr.forEach((itemObj) => getBestRipLevel(itemObj));

      //** Grouping together items with the same rip code and then creating new record for each rip code **//

      var ripCodeWithQtyArr =
        groupTogetherItemsBasedonRipCodes(itemsWithRipCodeArr);

      ripCodeWithQtyArr = ripCodeWithQtyArr.filter((ripCodeObj) =>
        checkIfReachedMinTierLevel(ripCodeObj, true)
      );

      if (ripCodeWithQtyArr && ripCodeWithQtyArr.length > 0) {
        let sessionObj = runtime.getCurrentSession();
        sessionObj.set({
          name: "RipCodeWithQtyArr",
          value: JSON.stringify(ripCodeWithQtyArr),
        });
      }
    }
  }

  function afterSubmit(context) {
    let sessionObj = runtime.getCurrentSession();
    const ripCodeObjsArr = JSON.parse(
      sessionObj.get({ name: "RipCodeWithQtyArr" })
    );

    if (ripCodeObjsArr && ripCodeObjsArr.length > 0) {
      const vendorBill = context.newRecord;
      const vendorBillId = vendorBill.id;
      const vendorBillName = search.lookupFields({
        type: search.Type.VENDOR_BILL,
        id: vendorBillId,
        columns: ["transactionnumber"],
      })["transactionnumber"];
      ripCodeObjsArr.forEach(
        (ripCodeObj) =>
          (ripCodeObj.originalTotalQuantity = ripCodeObj.totalQuantity)
      );

      ripCodeObjsArr.forEach((itemObj) =>
        createNewAccrualRecord(itemObj, vendorBillId, vendorBillName)
      );

      ripCodeObjsArr.forEach((itemObj) => createTierQuantityRecords(itemObj));
      //Creating the tier + quantity record for each tier that this rip code can include based on the quantity

      if (newAccrualRecordsCreated && newAccrualRecordsCreated.length > 0) {
        var billInEditMode = record.load({
          type: record.Type.VENDOR_BILL,
          id: vendorBill.id,
        });
        //Load the bill, because we're doing this in the after submit function

        ripCodeObjsArr.forEach((ripInfoObj) => {
          setRipAccrualLinkAndCodePerItemOnBill(billInEditMode, ripInfoObj);
        });

        ripCodeObjsArr.forEach((ripInfoObj) => splitTotalsbyItem(ripInfoObj));

        itemSublistArr = [];

        ripCodeObjsArr.forEach((ripInfoObj) =>
          ripInfoObj.itemIds.forEach((itemObj) => {
            itemSublistArr.push({
              item: "44228",
              rate: ripInfoObj.amountPerUnit,
              quantity: itemObj.itemQuantity,
              description: `Rip received for rip code ${ripInfoObj.ripCode} and total credit for this code was ${ripInfoObj.totalAmountForCredit}`, //;for items: ${ripInfoObj.itemIds.itemId.join(", ")}`,
              custcol_rip_item_link: itemObj.itemId,
              custcol_rip_total_quantity_of_item: itemObj.itemQuantity,
              custcol_rip_agreement_detail_record: ripInfoObj.agreementId,
              custcol_unit_used_for_rip: itemObj.unitUsedForRip,
            });
          })
        );

        const vendorCreditObj = {
          entity: vendorBill.getValue("entity"),
          subsidiary: vendorBill.getValue("subsidiary"),
          account: 112,
          trandate: vendorBill.getValue("trandate"),
          custbody_associated_vendor_bill: vendorBill.id,
          tranid: `RIP${vendorBillName}`,
          custbody_rip_vendor_credit: true,
        };
        try {
          const newVendorCredit = createRipRecordLib.createVendorCredit(
            vendorCreditObj,
            itemSublistArr
          );
          billInEditMode.setValue(
            "custbody_associated_bill_credit",
            newVendorCredit
          );
          sessionObj.set({
            name: "RipCodeWithQtyArr",
            value: null,
          });
          billInEditMode.save();
          redirect.toSuitelet({
            scriptId: "customscript_brdg_redirect_rip_informati",
            deploymentId: "customdeploy_brdg_redirect_rip_sl",
            parameters: {
              newAccrualRecordsCreated: newAccrualRecordsCreated.toString(),
              currentVendorBill: vendorBill.id,
              newVendorCredit: newVendorCredit,
            },
          });
        } catch (e) {
          _handleErrors("Error saving vendor credit", e.message, true);
        }
      }
    }
  }

  return {
    beforeSubmit,
    afterSubmit,
  };
});
