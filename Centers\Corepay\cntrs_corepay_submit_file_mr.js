/*TODO: When ready to deploy to CNTRS prod account:
  remove all script file and records from our account
  confirm corepay status field is in prod
  update payments to be submitted to have a status of pending (1)
  updated value of this.referenceFolder in cntrs_corepay_factory.js to prod value
*/

//TODO: Asked <PERSON> to create subfolders, then update the file paths in the script
//TODO: confirm file generated is correct

/**
 * @description Submit CSV file of payments to Corepay SFTP server
 *
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript

 * <AUTHOR>
 */

define([
  "require",
  "N/log",
  "N/runtime",
  "./Processors/cntrs_corepay_factory",
], (/** @type {any} */ require) => {
  const log = require("N/log");
  const runtime = require("N/runtime");

  /** @type {import("./Processors/cntrs_corepay_factory")} */
  const CorePayPaymentProcessor =
    require("./Processors/cntrs_corepay_factory").CorePayPaymentProcessor;

  /**
   * Get information for all payment records to be submitted
   *
   * @function getInputData
   * @returns {SuiteQLObjectReference|undefined} Object containing SuiteQL string
   */
  function getInputData() {
    try {
      const currentScript = runtime.getCurrentScript();

      let paymentsQueryString =
        new CorePayPaymentProcessor().getPaymentsQueryObj();

      return {
        type: "suiteql",
        query: paymentsQueryString,
      };
    } catch (err) {
      log.error("GET_INPUT_DATA_ERROR", err);
      throw err;
    }
  }

  /**
   * Process each payment and format for CSV output
   *
   * @function reduce
   * @param {Object} context - The reduce context
   * @param {Array} context.values - Values passed in
   * @param {Function} context.write - Function to write output key-value pairs
   * @returns {void}
   */
  function reduce(context) {
    try {
      const parsedRow = JSON.parse(context.values[0]).values;

      if (!parsedRow || parsedRow.length < 20) {
        log.error(
          "REDUCE_ERROR",
          "Parsed row does not contain expected data structure"
        );

        return;
      }

      // Extract payment metadata for later processing
      const paymentData = {
        internalId: parsedRow[18],
        paymentOrCredit: parsedRow[19],
      };

      // Remove internal tracking fields from CSV output
      const csvRow = parsedRow
        .filter((_, index) => index !== 18 && index !== 19)
        .join("|");

      context.write(JSON.stringify(paymentData), csvRow);
    } catch (err) {
      throw err;
    }
  }

  /**
   * Create and process the payment file, update payment statuses
   *
   * @function summarize
   * @param {Object} context - The summarize context
   * @param {Object} context.output - Output from reduce stage
   * @param {Object} context.reduceSummary - Summary of reduce stage
   * @param {number} context.usage - Script usage units consumed
   * @param {number} context.concurrency - Concurrency used
   * @param {number} context.yields - Number of yields
   * @returns {void}
   */
  function summarize(context) {
    try {
      log.audit(
        "Processing Summary",
        `Usage Consumed: ${context.usage}, Concurrency Number: ${context.concurrency}, Number of Yields: ${context.yields}`
      );

      const errorsLog = [];

      context.reduceSummary.errors.iterator().each(function (key, value) {
        errorsLog.push(key);
        return true;
      });

      if (errorsLog.length > 0)
        log.error(
          `${errorsLog.length} Error${
            errorsLog.length > 1 ? "s" : ""
          } in Reduce Stage`,
          errorsLog.join("\n")
        );

      const recordsProcessedSuccessfully = [];

      context.output &&
        context.output.iterator().each((key, value) => {
          recordsProcessedSuccessfully.push(key);
          return true;
        });

      if (recordsProcessedSuccessfully.length === 0) {
        log.audit("No new payments to process");

        return;
      }

      log.audit(
        `${recordsProcessedSuccessfully.length} Payment${
          recordsProcessedSuccessfully.length != 1 ? "s" : ""
        } Processed Successfully`,
        recordsProcessedSuccessfully.join(", ")
      );

      let corePayFactory = new CorePayPaymentProcessor();
      corePayFactory.fileLines = corePayFactory.headerRow;
      corePayFactory.addBodyRows(context);
      corePayFactory.createFile();
      //TODO: uncomment when go live  - corePayFactory.uploadFile();
      corePayFactory.saveToReferenceFolder();
      corePayFactory.callScriptToUpdatePaymentStatus(context);
    } catch (err) {
      throw err;
    }
  }

  return {
    getInputData,
    reduce,
    summarize,
  };
});
