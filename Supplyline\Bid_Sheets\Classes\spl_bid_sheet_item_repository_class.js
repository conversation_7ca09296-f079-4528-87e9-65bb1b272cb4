/**
 * @description ItemRepository class for managing item data
 *
 * @NApiVersion 2.1
 * 
 * <AUTHOR>
 * @module spl_bid_sheet_item_repository_class
 */

define([
    "exports",
    "../../../Classes/vlmd_custom_error_object",
], (
    /** @type {any} */ exports,
    /** @type {any} */ CustomErrorObject
) => {

    /**
     * ItemRepository class for managing item data
     *
     * @class
     */
    class ItemRepository {
        /**
         * Create a new ItemRepository instance
         * @param {Object} options - Configuration options
         * @param {Object} [options.queryModule=null] - NetSuite query module for database operations
         * @param {Object} [options.fileModule=null] - NetSuite file module for file operations
         * @param {Array} [options.items=[]] - Pre-loaded items
         * @param {Object} [options.itemsIndexed={}] - Pre-loaded item index
         */
        constructor(options = {}) {
            const { 
                queryModule = null,
                fileModule = null,
                items = [],
                itemsIndexed = {}
            } = options;
            
            this.customErrorObject = new CustomErrorObject();
            this.query = queryModule;
            this.file = fileModule;
            
            // Initialize item data
            this.items = items;
            this.itemsIndexed = itemsIndexed;
            this.lastUpdated = null;
        }
        
        /**
         * Loads all items from the database
         * @returns {Array} Array of items
         */
        loadItemsFromDatabase() {
            try {
                if (!this.query) {
                    throw new Error("Query module not provided");
                }
                
                const itemsQuery = `
                    SELECT
                        item.id,
                        item.itemid,
                        item.displayname,
                        item.vendorname AS vendor_code,
                        BUILTIN.HIERARCHY(item.class, 'DISPLAY_JOINED') AS item_category
                    FROM
                        item
                    JOIN
                        itemSubsidiaryMap AS item_subsidiary ON item.id = item_subsidiary.item
                    WHERE
                        item.isinactive = 'F'
                        AND item_subsidiary.subsidiary = 1
                    ORDER BY
                        item.itemid
                `;
                
                let itemPagedResults = this.query.runSuiteQLPaged({
                    query: itemsQuery,
                    pageSize: 1000
                });
                
                this.items = [];
                
                // Process all pages
                for (let i = 0; i < itemPagedResults.pageRanges.length; i++) {
                    let currentPage = itemPagedResults.fetch(i);
                    let currentPagedData = currentPage.data.asMappedResults();
                    
                    for (const item of currentPagedData) {
                        this.items.push({
                            id: item.id,
                            itemId: item.itemid,
                            displayName: item.displayname || '',
                            vendorCode: item.vendor_code || '',
                            category: item.item_category || ''
                        });
                    }
                }
                
                this.lastUpdated = new Date();
                
                return this.items;
            } catch (err) {
                this.customErrorObject.throwError({
                    summaryText: "Error loading items from database",
                    error: err
                });
                return [];
            }
        }
        
        /**
         * Creates an indexed version of the items for faster lookups
         * @returns {Object} Indexed items
         */
        createItemsIndex() {
            try {
                this.itemsIndexed = {};
                
                this.items.forEach(item => {
                    this.itemsIndexed[item.id] = item;
                    
                    // Add searchText property for faster text searching
                    item.searchText = [
                        item.id,
                        item.itemId,
                        item.displayName,
                        item.vendorCode,
                        item.category
                    ].filter(Boolean).join(' ').toLowerCase();
                });
                
                return this.itemsIndexed;
            } catch (err) {
                this.customErrorObject.throwError({
                    summaryText: "Error creating item index",
                    error: err
                });
                return {};
            }
        }
        
        /**
         * Gets an item by ID
         * @param {string|number} id - Item ID
         * @returns {Object|null} Item object or null if not found
         */
        getItemById(id) {
            try {
                return this.itemsIndexed[id] || null;
            } catch (err) {
                this.customErrorObject.throwError({
                    summaryText: "Error getting item by ID",
                    error: err
                });
                return null;
            }
        }
        

        /**
         * Gets candidate items by searching for keywords in the item text
         * @param {string[]} keywords - Keywords to search for
         * @param {number} threshold - Minimum match threshold (0-1)
         * @returns {Array} Array of matching items with scores
         */
        searchItems(keywords, threshold = 0.5) {
            try {
                if (!keywords || !Array.isArray(keywords) || keywords.length === 0) {
                    return [];
                }

                // First pass: quickly filter items that contain at least one keyword
                const candidateItems = Object.values(this.itemsIndexed).filter(item => {
                    if (!item.searchText) return false;

                    return keywords.some(keyword => 
                        item.searchText.includes(keyword.toLowerCase())
                    );
                });

                // Second pass: score the candidate items
                const scoredItems = candidateItems.map(item => {
                    // Split searchText into words once
                    const itemWords = new Set(item.searchText.split(/\s+/));
                    
                    // Find matching keywords using Set intersection
                    const matchedKeywords = keywords.filter(keyword => 
                        itemWords.has(keyword.toLowerCase())
                    );
                    
                    // Calculate score based on matches
                    const matchScore = matchedKeywords.length;
                    const normalizedScore = keywords.length > 0 ? 
                        matchScore / keywords.length : 0;

                    return {
                        itemInternalId: item.id,
                        itemId: item.itemId,
                        displayName: item.displayName,
                        vendorCode: item.vendorCode,
                        category: item.category,
                        score: normalizedScore,
                        matchedKeywords: matchedKeywords
                    };
                });

                // Filter by threshold and sort by score
                return scoredItems
                    .filter(item => item.score >= threshold)
                    .sort((a, b) => b.score - a.score);
            } catch (err) {
                this.customErrorObject.throwError({
                    summaryText: "Error searching items",
                    error: err
                });
                return [];
            }
        }

        searchIndexzed
        
        /**
         * Saves items to file
         * @param {string} fileName - File name
         * @param {number} folderId - Folder ID
         * @returns {Object} Result with file ID
         */
        saveItemsToFile(fileName = 'bid_sheet_items.json', folderId) {
            try {
                if (!this.file) {
                    throw new Error("File module not provided");
                }
                
                const data = {
                    items: this.items,
                    lastUpdated: new Date().toISOString()
                };
                
                const fileContent = JSON.stringify(data);
                
                // Create or overwrite the file
                const fileObj = this.file.create({
                    name: fileName,
                    fileType: this.file.Type.JSON,
                    contents: fileContent,
                    folder: folderId
                });
                
                const fileId = fileObj.save();
                
                return {
                    fileId: fileId,
                    fileName: fileName
                };
            } catch (err) {
                this.customErrorObject.throwError({
                    summaryText: "Error saving items to file",
                    error: err
                });
                return null;
            }
        }
        
        /**
         * Saves item index to file
         * @param {string} fileName - File name
         * @param {number} folderId - Folder ID
         * @returns {Object} Result with file ID
         */
        saveItemIndexToFile(fileName = 'bid_sheet_item_index.json', folderId) {
            try {
                if (!this.file) {
                    throw new Error("File module not provided");
                }
                
                const data = {
                    itemsIndexed: this.itemsIndexed,
                    lastUpdated: new Date().toISOString()
                };
                
                const fileContent = JSON.stringify(data);
                
                // Create or overwrite the file
                const fileObj = this.file.create({
                    name: fileName,
                    fileType: this.file.Type.JSON,
                    contents: fileContent,
                    folder: folderId
                });
                
                const fileId = fileObj.save();
                
                return {
                    fileId: fileId,
                    fileName: fileName
                };
            } catch (err) {
                this.customErrorObject.throwError({
                    summaryText: "Error saving item index to file",
                    error: err
                });
                return null;
            }
        }
        
        /**
         * Loads items from file
         * @param {string|number} fileIdOrPath - File ID or path
         * @returns {Array} Loaded items
         */
        loadItemsFromFile(fileIdOrPath) {
            try {
                if (!this.file) {
                    throw new Error("File module not provided");
                }
                
                // Load the file
                const fileObj = this.file.load({
                    id: fileIdOrPath
                });
                
                const fileContent = fileObj.getContents();
                const data = JSON.parse(fileContent);
                
                this.items = data.items;
                this.lastUpdated = new Date(data.lastUpdated);
                
                return this.items;
            } catch (err) {
                this.customErrorObject.throwError({
                    summaryText: "Error loading items from file",
                    error: err
                });
                return [];
            }
        }
        
        /**
         * Loads item index from file
         * @param {string|number} fileIdOrPath - File ID or path
         * @returns {Object} Loaded item index
         */
        loadItemIndexFromFile(fileIdOrPath) {
            try {
                if (!this.file) {
                    throw new Error("File module not provided");
                }
                
                // Load the file
                const fileObj = this.file.load({
                    id: fileIdOrPath
                });
                
                const fileContent = fileObj.getContents();
                const data = JSON.parse(fileContent);
                
                this.itemsIndexed = data.itemsIndexed;
                this.lastUpdated = new Date(data.lastUpdated);
                
                return this.itemsIndexed;
            } catch (err) {
                this.customErrorObject.throwError({
                    summaryText: "Error loading item index from file",
                    error: err
                });
                return {};
            }
        }

        /**
         * Sets the items for the ItemRepository instance
         * @param {Array} items - Array of items
         */
        setItems(items) {
            this.items = items;
        }

        /**
         * Sets the indexed items for the ItemRepository instance
         * @param {Object} itemsIndexed - Indexed items
         */
        setItemsIndexed(itemsIndexed) {
            this.itemsIndexed = itemsIndexed;
        }
    }

    // Export the class
    exports.ItemRepository = ItemRepository;

    return ItemRepository;
});






