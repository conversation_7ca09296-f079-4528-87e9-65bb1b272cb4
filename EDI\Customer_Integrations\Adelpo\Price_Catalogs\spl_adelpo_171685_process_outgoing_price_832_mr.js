/**
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * @NAmdConfig /SuiteScripts/config.json
 */

//@ts-ignore
define([
	"require",
	"N/log",
	"../../../Libraries/Process_EDI_File/spl_process_outgoing_832_price_lib",
], function (require, log) {
	/**@type {import ('../../../Libraries/Process_EDI_File/spl_process_outgoing_832_price_lib')} */
	const process832PriceLib = require("../../../Libraries/Process_EDI_File/spl_process_outgoing_832_price_lib");

	const customerObj = {
		accountNumber: "171685",
		customerName: "Heritage Retirement Communities",
		internalId: 348045,
	};

	const dataObj = {
		prodGuidBool: true,
		prodDirectoryBool: true,
		decodeContent: true,
		prodGUID: "87edf63d6c0c4cd084c0614c23599ee7",
		sandboxGUID: "",
		prodDirectory: "/users/Adelpo/OUT/832",
		testDirectory: "/users/Adelpo/Test",
		transactionType: "Price Catalog",
		purchasingSoftware: "Adelpo",
		customerName: customerObj.customerName,
	};

	function getInputData(context) {
		return process832PriceLib.getPriceList(customerObj.internalId);
	}

	function map(context) {
		const parsedItemRow = JSON.parse(context.value);

		try {
			const itemObj = process832PriceLib.getItemObj(
				parsedItemRow,
				customerObj.accountNumber
			);

			const itemString = process832PriceLib.getItemAsString(itemObj);

			context.write(customerObj.accountNumber, itemString);
		} catch (err) {
			log.error(
				"MAP_ERROR",
				`Item Row: ${JSON.parse(parsedItemRow)} Error: ${err}`
			);
		}
	}

	function summarize(summary) {
		try {
			process832PriceLib.processEnd(
				summary,
				customerObj.accountNumber,
				dataObj,
				customerObj.customerName
			);
		} catch (e) {
			log.error("Error Processing Summarize", e);
		}
	}

	return {
		getInputData,
		map,
		summarize,
	};
});
