/**
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * @NAmdConfig /SuiteScripts/config.json
 */

define([
	"require",
	"N/error",
	"N/query",
	"Get810InternalIdsLib",
	"GetEdiPartnerValuesLib",
	"ProcessOutgoingEdiFileEmailLib",
	"PushEdiEmailInfoToDBLib",
	"N/runtime",
	"../../Libraries/Process_EDI_File/spl_process_outgoing_810_lib",
	"../../../Classes/vlmd_custom_error_object",
	"../../../Classes/vlmd_mr_summary_handling",
], function (
	require,
	error,
	query,
	getInvoiceInternalIdsLib,
	getEdiPartnerValuesLib,
	processOutgoingEdiFileEmailLib,
	pushEdiEmailInfoToDBLib,
	runtime
) {
	/** @type {import("../../../Classes/vlmd_custom_error_object").CustomErrorObject} */
	const CustomErrorObject = require("../../../Classes/vlmd_custom_error_object");

	const process810Lib = require("../../Libraries/Process_EDI_File/spl_process_outgoing_810_lib");

	var noTransactionsToProcess = [];

	var dataObj = {
		prodGuidBool: true,
		prodDirectoryBool: true,
		decodeContent: true,
		prodGUID: "26d995289d5f44f6a846937b6ddaca9b",
		sandboxGUID: "",
		prodDirectory: `/users/Agora/OUT/810`,
		testDirectory: "/users/Agora/Test",
		transactionType: "Invoice/Credit Memo",
		purchasingSoftware: "Agora",
		documentTypeId: 6,
		purchasingSoftwareId: 6,
		pushEmailToDB: true,
	};

	var partnerValues;

	function getInputData(context) {
		const customErrorObject = new CustomErrorObject();

		try {
			const currentScript = runtime.getCurrentScript();
			var customerId = currentScript.getParameter({
				name: "custscript_spl_rprcs_otgng_810_customer",
			});

			var parameterValues = _getParameterValues(customerId);

			var accountNumber = parameterValues.purchaser;
			var customerName = parameterValues.customer_name;
			var integrationFolder = parameterValues.purchaser;
			var integrationId = parameterValues.integration_id;

			// Value should be Comma delimited Invoice Number enclosed in single quotes ('INV835965409','INV835965466','INV835965803')
			var documentNumbersVals = currentScript.getParameter({
				name: "custscript_spl_rprcs_otgng_810_docnum",
			});

			function _getParameterValues(customerId) {
				try {
					const parameterQuery = `SELECT
			cx.altname as "customer_name",
			BUILTIN.DF( eir.custrecord_spl_prchsng_fclty ) as "purchaser",
			eir.id as "integration_id",
		 FROM
			CUSTOMER cx 
			JOIN
			   customrecord_vlmd_edi_integration eir 
			   ON (cx.custentity_spl_edi_integration_record = eir.id) 
		 WHERE
			cx.ID = 
			(
				(${customerId})
			)`;

					const resultIterator = query
						.runSuiteQL({
							query: parameterQuery,
						})
						.asMappedResults();

					return resultIterator[0];
				} catch (e) {
					throw `${e}: ${customerId}`;
				}
			}

			var searchArr = [];
			partnerValues = getEdiPartnerValuesLib.getAgoraValues(accountNumber);

			if (!partnerValues) {
				throw `No partner values gotten.`;
			}

			var customerFoldersArr = [
				{
					customerId,
					integrationFolder,
					customerName,
					integrationId,
				},
			];

			customerFoldersArr.forEach((customer) => {
				var transactionsArr = getInvoiceInternalIdsLib.getInternalIds(
					customer.customerId,
					"",
					"",
					"",
					documentNumbersVals,
					customErrorObject
				);

				if (transactionsArr.length <= 0) {
					noTransactionsToProcess.push(customer.customerName);
				} else {
					transactionsArr.forEach((transactionObj) => {
						searchArr.push({
							transactionObj: transactionObj,
							customer: customer,
							partnerValues: partnerValues,
							dataObj: dataObj,
						});
					});
				}
			});
		} catch (err) {
			customErrorObject.throwError({
				summaryText: "GET_INPUT_DATA_ERROR",
				error: err,
			});
		}
		return searchArr;
	}

	function map(context) {
		const customErrorObject = new CustomErrorObject();
		var parsedResult = JSON.parse(context.value);
		var { transactionObj, customer, partnerValues, dataObj } = parsedResult;

		try {
			const docCtrlNumRecIdVal = process810Lib.process810(
				transactionObj,
				customer,
				partnerValues,
				dataObj,
				customErrorObject
			);

			context.write(customer.customerName, docCtrlNumRecIdVal);
		} catch (err) {
			customErrorObject.throwError({
				summaryText: `MAP_ERROR`,
				error: err,
				recordId: transactionObj?.transactionId,
				recordType: transactionObj?.transactionType,
				errorWillBeGrouped: true,
			});
		}
	}

	function reduce(context) {
		context.write({
			key: context.key,
			value: context.values,
		});
	}

	function summarize(context) {
		try {
			const StageHandling = require("../../../Classes/vlmd_mr_summary_handling");
			const stageHandling = new StageHandling(context);

			stageHandling.printScriptProcessingSummary();

			const { recordsProcessedMessage } = stageHandling.printRecordsProcessed();

			const { errorsMessage } = stageHandling.printErrors({
				groupErrors: true,
			});

			var sentEmailObj = processOutgoingEdiFileEmailLib.processEmail(
				context,
				noTransactionsToProcess,
				dataObj.purchasingSoftware,
				dataObj.transactionType
			);

			if (dataObj.pushEmailToDB) {
				try {
					pushEdiEmailInfoToDBLib.pushEdiEmailInfoToDB(dataObj, sentEmailObj);
				} catch (e) {
					throw `Error pushing EDI email to database: ${e}`;
				}
			}
		} catch (err) {
			customErrorObject.throwError({
				summaryText: `SUMMARIZE_ERROR`,
				error: err,
			});
		}
	}

	return {
		getInputData,
		map,
		reduce,
		summarize,
	};
});
