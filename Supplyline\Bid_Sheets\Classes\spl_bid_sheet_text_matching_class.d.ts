/**
 * Interface and type definitions for the TextMatching class
 *
 * <AUTHOR>
 */

import { CustomErrorObject } from "../../Classes/vlmd_custom_error_object";
import { FILLER_WORDS, UNIT_REPLACEMENTS, DIMENSION_PATTERNS, DimensionPattern } from "./spl_bid_sheet_text_matching_constants";

/**
 * Item structure used in the TextMatching class
 */
export interface Item {
    id: string;
    itemId: string;
    displayName: string;
    vendorCode: string;
    category: string;
    searchText?: string;
}

/**
 * Constructor options for TextMatching class
 */
export interface TextMatchingOptions {
    loadItems?: boolean;
    cachedItems?: Item[];
    stringOne?: string;
    stringTwo?: string;
    buildTokens?: boolean;
}

/**
 * TextMatching class for string similarity and token normalization
 */
export interface TextMatching {
    /** Constructor */
    new(options?: TextMatchingOptions): TextMatching;

    /** Custom error object for error handling */
    customErrorObject: CustomErrorObject;

    /** Items array */
    items: Item[];

    /** Indexed version of items for faster lookups */
    itemsIndexed: {[key: string]: Item};

    /** First string to compare */
    stringOne: string;

    /** Second string to compare */
    stringTwo: string;

    /** Tokens from first string */
    tokensOne: {
        all: string[];
        words: string[];
        dimensions: string[];
        measurements: string[];
        packaging: string[];
    };

    /** Tokens from second string */
    tokensTwo: {
        all: string[];
        words: string[];
        dimensions: string[];
        measurements: string[];
        packaging: string[];
    };

    /** Common filler words to be removed during normalization */
    fillerWords: typeof FILLER_WORDS;

    /** Unit replacements for standardizing measurements */
    unitReplacements: typeof UNIT_REPLACEMENTS;

    /** Common dimension patterns to standardize */
    dimensionPatterns: typeof DIMENSION_PATTERNS;

    /** Set of standardized unit terms */
    units: Set<string>;

    /** Packaging information patterns */
    packagingPatterns: any[];

    /**
     * Loads all items from the database
     * @param options - Configuration options
     */
    loadAllItems(options?: {buildItemIndex?: boolean}): void;

    /**
     * Creates an indexed version of the items for faster lookups
     */
    createItemsIndex(): void;

    /**
     * Sets the items for the TextMatching instance
     * @param options - Configuration options
     */
    setItems(options: {items?: Item[], buildItemIndex?: boolean}): void;

    /**
     * Sets the strings to compare for the TextMatching instance
     * @param options - Configuration options
     */
    setStrings(options: {stringOne?: string, stringTwo?: string, buildTokens?: boolean}): void;

    /**
     * Normalizes a token string by converting to lowercase, handling units and removing filler words
     * @param stringToNormalize - The string to normalize
     * @returns Array of normalized tokens
     */
    normalizeToken(stringToNormalize: string): string[];

    /**
     * Generates tokens from a string with options for categorization and filtering
     * @param options - Configuration options
     * @returns Tokens categorized by type
     */
    generateTokens(options: {
        stringToTokenize: string;
        removeFillerWords?: boolean;
        categorizeTokens?: boolean;
    }): any;

    /**
     * Calculates similarity between two strings using Jaccard similarity
     * @param stringOne - First string to compare
     * @param stringTwo - Second string to compare
     * @returns Similarity score (0-1)
     */
    calculateSimilarity(stringOne: string, stringTwo: string): number;

    /**
     * Calculates distance score between two strings using multiple matching strategies
     * @param options - Configuration options
     * @returns Object containing score and detailed matching information, or null if score is below threshold
     */
    calculateDistance(options?: {
        stringOne?: string;
        stringTwo?: string;
        threshold?: number;
    }): {
        score: number;
        details: any;
    } | null;

    /**
     * Calculates character-level similarity between two strings using bigram matching
     * @param str1 - First string to compare
     * @param str2 - Second string to compare
     * @returns Similarity score between 0 and 1
     */
    getCharacterLevelSimilarity(str1: string, str2: string): number;

    /**
     * Checks if two dimension strings are similar
     * @param dim1 - First dimension string
     * @param dim2 - Second dimension string
     * @returns True if dimensions are similar
     */
    areSimilarDimensions(dim1: string, dim2: string): boolean;

    /**
     * Checks if two words are similar
     * @param word1 - First word
     * @param word2 - Second word
     * @returns True if words are similar
     */
    areSimilarWords(word1: string, word2: string): boolean;

    /**
     * Saves the items to localStorage for faster future lookups
     */
    saveToLocalStorage(): void;
}

/**
 * Module interface for TextMatching
 */
export interface TextMatchingModule {
    /**
     * The TextMatching class constructor for creating new instances
     */
    TextMatching: TextMatching;

    /**
     * Get the singleton instance of TextMatching
     * @returns The singleton TextMatching instance
     */
    getInstance(): TextMatching;
}

/**
 * Default export is the TextMatching constructor
 */
declare const TextMatching: TextMatching;

export default TextMatching;


