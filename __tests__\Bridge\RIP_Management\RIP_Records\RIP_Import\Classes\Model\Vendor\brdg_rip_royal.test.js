import { BridgeRIPRoyal } from "../../../../../../../../Bridge/RIP_Management/RIP_Records/RIP_Import/Classes/Models/Vendor/brdg_rip_royal";

describe("BridgeRIPRoyal", () => {
  beforeEach(() => {
    jest.resetModules();
    jest.resetAllMocks();
    jest.clearAllMocks();
  });

  describe("parseLine", () => {
    it.each([
    {
      title: "adds missing CASE uom to tier level 2",
      fields: [
        "IBK",
        "7/1/2025",
        "7/31/2025",
        "BARKAN CLASSIC",
        "31510",
        "CASE",
        "3",
        "9",
        "5",
        "25",
        "100",
        "1000",
        "",
        "",
        "",
        "",
        "",
        "ASSORTED",
        0,
      ],
      expected: {
        sku: "31510",
        ripCode: "IBK",
        brandRegistration: null,
        fromDate: "7/1/2025",
        toDate: "7/31/2025",
        description: "BARKAN CLASSIC",
        uom1: "CASE",
        qty1: "3",
        amt1: "9",
        uom2: "CASE",
        qty2: "5",
        amt2: "25",
        uom3: "CASE",
        qty3: "100",
        amt3: "1000",
        uom4: null,
        qty4: null,
        amt4: null,
        uom5: null,
        qty5: null,
        amt5: null,
        countAs: 1,
        comments: "ASSORTED",
        lineCount: 0,
      },
    },
    {
      title: "adds missing CASE uom to tier levels 2 and 3",
      fields: [
        "CHILL",
        "7/1/2025",
        "7/31/2025",
        "BARON HERZOG JEUNESSE",
        "14000",
        "CASE",
        "5",
        "15",
        "12",
        "108",
        "25",
        "500",
        "",
        "",
        "",
        "",
        "",
        "ASSORTED",
        1,
      ],
      expected: {
        sku: "14000",
        ripCode: "CHILL",
        brandRegistration: null,
        fromDate: "7/1/2025",
        toDate: "7/31/2025",
        description: "BARON HERZOG JEUNESSE",
        uom1: "CASE",
        qty1: "5",
        amt1: "15",
        uom2: "CASE",
        qty2: "12",
        amt2: "108",
        uom3: "CASE",
        qty3: "25",
        amt3: "500",
        uom4: null,
        qty4: null,
        amt4: null,
        uom5: null,
        qty5: null,
        amt5: null,
        countAs: 1,
        comments: "ASSORTED",
        lineCount: 1,
      },
    },
    {
      title: "retains CASE uom for all 3 RIP levels",
      fields: [
        "ATQ",
        "7/1/2025",
        "7/31/2025",
        "ANTIOQUENO 1.75 LIT",
        "92302",
        "CASE",
        "1",
        "5.00",
        "CASE",
        "2",
        "24.00",
        "CASE",
        "5",
        "125.00",
        "",
        "",
        "",
        "ASSORTED",
        2,
      ],
      expected: {
        sku: "92302",
        ripCode: "ATQ",
        brandRegistration: null,
        fromDate: "7/1/2025",
        toDate: "7/31/2025",
        description: "ANTIOQUENO 1.75 LIT",
        uom1: "CASE",
        qty1: "1",
        amt1: "5.00",
        uom2: "CASE",
        qty2: "2",
        amt2: "24.00",
        uom3: "CASE",
        qty3: "5",
        amt3: "125.00",
        uom4: null,
        qty4: null,
        amt4: null,
        uom5: null,
        qty5: null,
        amt5: null,
        countAs: 1,
        comments: "ASSORTED",
        lineCount: 2,
      },
    },
    {
      title: "retains CASE uom for all 4 RIP levels",
      fields: [
        "COO",
        "7/1/2025",
        "7/31/2025",
        "COOLOO 100ML",
        "18704",
        "CASE",
        "1",
        "1.00",
        "CASE",
        "5",
        "10",
        "CASE",
        "25",
        "125",
        "CASE",
        "100",
        "1000",
        "ASSORTED",
        3,
      ],
      expected: {
        sku: "18704",
        ripCode: "COO",
        brandRegistration: null,
        fromDate: "7/1/2025",
        toDate: "7/31/2025",
        description: "COOLOO 100ML",
        uom1: "CASE",
        qty1: "1",
        amt1: "1.00",
        uom2: "CASE",
        qty2: "5",
        amt2: "10",
        uom3: "CASE",
        qty3: "25",
        amt3: "125",
        uom4: "CASE",
        qty4: "100",
        amt4: "1000",
        uom5: null,
        qty5: null,
        amt5: null,
        countAs: 1,
        comments: "ASSORTED",
        lineCount: 3,
      },
    },
  ])("$title", ({ fields, expected }) => {
      expect(new BridgeRIPRoyal({customErrorObject: {}}).parseLine(fields)).toEqual(expected);
    });

    it("throws an error when column values are missing", () => {
      const updateError = jest.fn();
      expect(() => new BridgeRIPRoyal({customErrorObject: {
        updateError,
        ErrorTypes: {},
      }}).parseLine([""])).toThrow();
    })
  });
});
