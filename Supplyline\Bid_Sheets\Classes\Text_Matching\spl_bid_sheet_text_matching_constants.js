/**
 * @description Constants for text matching operations. Contains filler words, unit replacements, and other reference data
 *
 * @NApiVersion 2.1
 * <AUTHOR>
 * 
 * @module spl_bid_sheet_text_matching_constants
 */

define(['exports'], (exports) => {
    /**
     * Common filler words to be removed during normalization
     */
    exports.FILLER_WORDS = new Set([
        'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of',
        'with', 'by', 'as', 'from', 'that', 'this', 'these', 'those', 'w/', 'f/', 'w', 'f'
    ]);

    /**
     * Replacements for abbreviations, shortcuts, and other variations
     */
    exports.UNIT_REPLACEMENTS = {
        // Common abbreviations
        "w/": "with",
        "f/": "for",
        "w": "with",
        "f": "for",

        // Dimension labels
        "l": "length",
        "w": "width",
        "h": "height",
        "d": "depth",

        // Length/Distance units
        "in": "inch",
        "in.": "inch",
        "\"": "inch",
        "''": "inch",
        "ft": "foot",
        "ft.": "foot",
        "'": "foot",
        "yd": "yard",
        "yd.": "yard",
        "mm": "millimeter",
        "cm": "centimeter",
        "m": "meter",
        "km": "kilometer",
        "mi": "mile",

        // Area and Volume descriptors
        "sq": "square",
        "sq.": "square",
        "cu": "cubic",
        "cu.": "cubic",

        // Geometric measurements
        "dia": "diameter",
        "dia.": "diameter",
        "ø": "diameter",
        "rad": "radius",
        "rad.": "radius",
        "r": "radius",
        "r.": "radius",

        // Quantity units
        "pc": "piece",
        "pcs": "piece",
        "ea": "each",
        "pk": "package",
        "pk.": "package",
        "pkg": "package",
        "bx": "box",
        "ctn": "carton",
        "cs": "case",
        "dz": "dozen",
        "pr": "pair",
        "st": "set",
        "bdl": "bundle",
        "rl": "roll",
        "un": "unit",

        // Volume units - Liquid
        "fl oz": "fluid ounce",
        "fl. oz.": "fluid ounce",
        "fl.oz.": "fluid ounce",
        "floz": "fluid ounce",
        "ml": "milliliter",
        "gal": "gallon",
        "qt": "quart",
        "pt": "pint",

        // Volume units - Cubic
        "cu in": "cubic inch",
        "cu ft": "cubic foot",
        "cu yd": "cubic yard",
        "cc": "cubic centimeter",
        "cu m": "cubic meter",

        // Weight units
        "oz": "ounce",
        "oz.": "ounce",
        "lb": "pound",
        "lb.": "pound",
        "lbs": "pound",
        "g": "gram",
        "mg": "milligram",
        "kg": "kilogram",
        "t": "ton",
        "mt": "metric ton"
    };

    exports.UNITS = new Set([
        "with", "for", "with", "for",
        "length", "width", "height", "depth",
        "inch", "inch", "inch", "inch", "foot", "foot", "foot", "yard", "yard", "millimeter", "centimeter", "meter", "kilometer", "mile",
        "square", "square", "cubic", "cubic",
        "diameter", "diameter", "diameter", "radius", "radius", "radius", "radius",
        "piece", "piece", "each", "package", "package", "package", "box", "carton", "case", "dozen", "pair", "set", "bundle", "roll", "unit",
        "fluid ounce", "fluid ounce", "fluid ounce", "fluid ounce", "milliliter", "liter", "gallon", "quart", "pint",
        "cubic inch", "cubic foot", "cubic yard", "cubic centimeter", "cubic meter",
        "ounce", "ounce", "pound", "pound", "pound", "gram", "milligram", "kilogram", "ton", "metric ton"
    ]);

    /**
     * Units for dimensions
     */
    exports.UNITS_FOR_DIMENSIONS = new Set([
        'in', 'ft', 'cm', 'mm'
    ]);

    /**
     * Packaging information patterns to standardize unit conversion ratios
     */
    exports.PACKAGING_PATTERNS = [
        // 100ea/cs - 100 each per case
        { pattern: /(\d+)\s*ea\s*\/\s*cs/gi, replacement: '$1 each per case' },

        // 100ea/bx - 100 each per box
        { pattern: /(\d+)\s*ea\s*\/\s*bx/gi, replacement: '$1 each per box' },

        // 12pc/pk - 12 piece per package
        { pattern: /(\d+)\s*pc\s*\/\s*pk/gi, replacement: '$1 piece per package' },

        // 24/cs - 24 per case
        { pattern: /(\d+)\s*\/\s*cs/gi, replacement: '$1 per case' },

        // 6/bx - 6 per box
        { pattern: /(\d+)\s*\/\s*bx/gi, replacement: '$1 per box' },

        // cs/6ea - case of 6 each
        { pattern: /cs\s*\/\s*(\d+)\s*ea/gi, replacement: 'case of $1 each' },

        // bx/12pc - box of 12 piece
        { pattern: /bx\s*\/\s*(\d+)\s*pc/gi, replacement: 'box of $1 piece' },

        // 6 EA / CS - 6 each per case
        { pattern: /(\d+)\s*ea\s*\/\s*cs/gi, replacement: '$1 each per case' },

        // 12 PC / BX - 12 piece per box
        { pattern: /(\d+)\s*pc\s*\/\s*bx/gi, replacement: '$1 piece per box' }
    ];
});
