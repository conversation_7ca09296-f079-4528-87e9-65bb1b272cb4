/**
 * Interface and type definitions for the ItemRepository class
 *
 * <AUTHOR>
 */

import { CustomErrorObject } from "../../../../Classes/vlmd_custom_error_object";

/**
 * Item structure used in the ItemRepository class
 */
export interface Item {
    /** Internal ID of the item */
    id: string;
    /** Item ID/SKU */
    itemId: string;
    /** Display name of the item */
    displayName: string;
    /** Vendor code */
    vendorCode: string;
    /** Item category */
    category: string;
    /** Searchable text combining all fields */
    searchText?: string;
}

/**
 * Constructor options for ItemRepository class
 */
export interface ItemRepositoryOptions {
    /** NetSuite query module for database operations */
    queryModule?: any | null;
    /** NetSuite file module for file operations */
    fileModule?: any | null;
    /** Pre-loaded items */
    items?: Item[];
    /** Pre-loaded item index */
    itemsIndexed?: Record<string, Item>;
}

/**
 * Search options for finding items
 */
export interface SearchItemsOptions {
    /** Maximum number of results */
    limit?: number;
    /** Minimum similarity score */
    threshold?: number;
}

/**
 * Item search result with score
 */
export interface ItemSearchResult {
    /** The matched item */
    item: Item;
    /** Similarity score */
    score: number;
}

/**
 * File save result
 */
export interface FileSaveResult {
    /** ID of the saved file */
    fileId: string | number;
    /** Name of the saved file */
    fileName: string;
}

/**
 * ItemRepository class for managing item data
 */
export interface ItemRepository {
    /** Constructor */
    new(options?: ItemRepositoryOptions): ItemRepository;

    /** Custom error object for error handling */
    customErrorObject: CustomErrorObject;

    /** NetSuite query module for database operations */
    query: any;

    /** NetSuite file module for file operations */
    file: any;

    /** Array of items */
    items: Item[];

    /** Indexed version of items for faster lookups */
    itemsIndexed: Record<string, Item>;

    /** When the items were last updated */
    lastUpdated: Date | null;

    /**
     * Loads all items from the database
     * @returns Array of items
     */
    loadItemsFromDatabase(): Item[];

    /**
     * Creates an indexed version of the items for faster lookups
     * @returns Indexed items
     */
    createItemsIndex(): Record<string, Item>;

    /**
     * Gets an item by ID
     * @param id - Item ID
     * @returns Item object or null if not found
     */
    getItemById(id: string | number): Item | null;

    /**
     * Gets candidate items by searching for keywords in the item text
     * @param keywords - Keywords to search for
     * @param threshold - Minimum match threshold (0-1)
     * @returns Matching items with scores
     */
    searchItems(keywords: string[], threshold?: number): any[];

    /**
     * Saves items to file
     * @param fileName - File name
     * @param folderId - Folder ID
     * @returns Result with file ID
     */
    saveItemsToFile(fileName?: string, folderId?: number): FileSaveResult | null;

    /**
     * Saves item index to file
     * @param fileName - File name
     * @param folderId - Folder ID
     * @returns Result with file ID
     */
    saveItemIndexToFile(fileName?: string, folderId?: number): FileSaveResult | null;

    /**
     * Loads items from file
     * @param fileIdOrPath - File ID or path
     * @returns Loaded items
     */
    loadItemsFromFile(fileIdOrPath: string | number): Item[];

    /**
     * Loads item index from file
     * @param fileIdOrPath - File ID or path
     * @returns Loaded item index
     */
    loadItemIndexFromFile(fileIdOrPath: string | number): Record<string, Item>;

    /**
     * Sets the items for the ItemRepository instance
     * @param items - Array of items
     */
    setItems(items: Item[]): void;

    /**
     * Sets the indexed items for the ItemRepository instance
     * @param itemsIndexed - Indexed items
     */
    setItemsIndexed(itemsIndexed: Record<string, Item>): void;
}

/**
 * Module interface for ItemRepository
 */
export interface ItemRepositoryModule {
    /**
     * The ItemRepository class constructor for creating new instances
     */
    ItemRepository: ItemRepository;
}

/**
 * Default export is the ItemRepository constructor
 */
declare const ItemRepository: ItemRepository;

export default ItemRepository;
