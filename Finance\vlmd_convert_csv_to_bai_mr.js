/**
 * @description Map/Reduce script to convert a CSV file to a BAI format file
 * 
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 */
define(['N/file', 'N/runtime', 'N/email', 'N/log', '../Classes/vlmd_convert_to_bai'],
function(file, runtime, email, log, BaiConverter) {

  // Global error collection
  const errors = [];

  /**
   * Add error to collection with context
   * @param {string} stage - Which stage the error occurred in
   * @param {string} context - Additional context (filename, etc.)
   * @param {Error} error - The error object
   */
  function addError(stage, context, error) {
    errors.push({
      stage: stage,
      context: context,
      message: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString()
    });
  }

  const SFTP_CONFIG = {
    username: "nsar",
    passwordGuid: "3b9e672d8a1d46139c9438bd61318684",
    url: "************",
    hostKey: "AAAAB3NzaC1yc2EAAAABIwAAAIEA2YIvZPcsWA3jP6yq5J" +
            "5XYunNySNspUZUhtzxXoePDZEhxJFIF/am9o+15CqO4hlyEDni0m+VW4" +
            "LVSoiUnj31xJis91EcYrUeCewIcSwof4Xs05ghth/2SKn0cITsR5hOeST" +
            "l4jFsMf9oK0+b2gr4jdV4fria4E/eF2u0qQArWuc=",
    port: 22
  };
  
  const DEFAULT_OWNER_ID = 15131;
  const TIME_THRESHOLD_MINUTES = 60;
  
  const baiConverter = new BaiConverter(SFTP_CONFIG, TIME_THRESHOLD_MINUTES);

  /**
   * Get input data - list of CSV files in the ConvertToBAI folder
   * 
   * @param {Object} inputContext
   * @returns {Array|Object} Array of file objects to process or file content
   */
  function getInputData(inputContext) {
    try {
           
      const connection = baiConverter.createSftpConnection(baiConverter.PATHS.ROOT);
      let fileList = [];
      
      try {
        fileList = connection.list({
          path: baiConverter.PATHS.CONVERT_TO_BAI
        });
        
      } catch (listError) {
        addError('getInputData', 'fbce11c6-13ed-47bd-a6b8-e0dd3f1e9768 : listing files in ConvertToBAI', listError);
        return [];
      }
      
      const csvFiles = fileList.filter(fileInfo => 
        !fileInfo.directory && 
        fileInfo.name.toLowerCase().endsWith('.csv')
      );
      
      log.audit("CSV Files to Process", `Found ${csvFiles.length} CSV files to convert`);
      
      return csvFiles.map(file => ({
        name: file.name,
        path: baiConverter.PATHS.CONVERT_TO_BAI + file.name
      }));
    } catch (error) {
      addError('getInputData', '80122c08-c987-4362-be65-f1baf11d5212 : main function', error);
      return [];
    }
  }

  /**
   * Process each CSV file
   * 
   * @param {Object} mapContext
   */
  function map(mapContext) {
    try {
      const fileInfo = JSON.parse(mapContext.value);
      
      const connection = baiConverter.createSftpConnection(baiConverter.PATHS.ROOT);
      
      const downloadedFile = connection.download({
        directory: baiConverter.PATHS.CONVERT_TO_BAI,
        filename: fileInfo.name
      });
      
      const csvContent = downloadedFile.getContents();
      
      // Try to parse CSV with detailed error logging
      let rows = [];
      try {
        rows = baiConverter.parseCSV(csvContent);
      } catch (parseError) {
        log.error('CSV Parse Error', {
          file: fileInfo.name,
          error: parseError.message,
          stack: parseError.stack
        });
        addError('map', `d16cba28-9c48-47e5-840b-5706f9dad03f : CSV parsing failed for ${fileInfo.name}`, parseError);
        return;
      }
      
      log.audit('Parsed Rows', `File ${fileInfo.name} parsed to ${rows.length} rows`);
      
      
      if (rows.length === 0) {
        addError('map', `66b1fb5f-ed17-4860-8160-b837b4b354bf : ${fileInfo.name}`, new Error('File contains no valid data rows'));
        return;
      }
      
      mapContext.write('fileMetadata', JSON.stringify({ 
        sourceFileName: fileInfo.name 
      }));
      
      rows.forEach(row => {
        mapContext.write(fileInfo.name, JSON.stringify(row));
      });
    } catch (error) {
      addError('map', `01b16254-6064-4ce7-a385-1a3ddabb4efa : file: ${JSON.parse(mapContext.value).name}`, error);
      throw error;
    }
  }

  /**
   * Collect all rows from each file and create the BAI content
   * 
   * @param {Object} reduceContext
   */
  function reduce(reduceContext) {
    try {
      if (reduceContext.key === 'fileMetadata') {
        reduceContext.values.forEach(value => {
          reduceContext.write('fileMetadata', value);
        });
        return;
      }
      
      const fileName = reduceContext.key;
      const allRows = [];
      
      reduceContext.values.forEach(value => {
        allRows.push(JSON.parse(value));
      });
      
      if (allRows.length === 0) {
        addError('reduce', `fb104fc9-17ff-481d-a419-6e0e150ba1b2 : ${fileName}`, new Error('No valid rows found for file'));
        return;
      }
      
      // Pass the fileName to formatBAIContent
      const baiContent = baiConverter.formatBAIContent(allRows, fileName);
      
      reduceContext.write('baiFile', JSON.stringify({
        fileName: fileName,
        content: baiContent
      }));
    } catch (error) {
      addError('reduce', `9a7966e8-3edd-4b22-b252-032e8801a0da : file: ${reduceContext.key}`, error);
      throw error;
    }
  }

  /**
   * Summarize stage - create and upload the BAI file
   * 
   * @param {Object} summaryContext
   */
  function summarize(summaryContext) {
    let sourceFileNames = [];
    let baiFiles = [];
    
    try {
      summaryContext.output.iterator().each(function(key, value) {
        if (key === 'fileMetadata') {
          const metadata = JSON.parse(value);
          sourceFileNames.push(metadata.sourceFileName);
        } else if (key === 'baiFile') {
          baiFiles.push(JSON.parse(value));
        }
        return true;
      });
      
      if (baiFiles.length === 0) {
        log.audit('No BAI Content', 'The script ran successfully but no BAI content was generated. This may indicate an issue with the CSV data format.');
        return;
      }
      
      const scriptObj = runtime.getCurrentScript();
      const ownerId = scriptObj.getParameter({
        name: 'custscript_csv_to_bai_owner_id'
      }) || DEFAULT_OWNER_ID;
      
      // Create SFTP connection for moving files
      const sftpConnection = baiConverter.createSftpConnection(baiConverter.PATHS.ROOT);
      
      // Process each BAI file
      baiFiles.forEach((baiFileData, index) => {
        try {
          const originalName = baiFileData.fileName.replace(/\.[^/.]+$/, "");
          
          const baiFile = file.create({
            name: `${originalName}.bai`, // Use original filename with .bai extension
            fileType: file.Type.PLAINTEXT,
            contents: baiFileData.content
          });
          
          let connection;
          try {
            connection = baiConverter.createSftpConnection(baiConverter.PATHS.POPULAR_BANK);
            
            connection.upload({
              file: baiFile,
              replaceExisting: true
            });
            
            log.audit('BAI File Uploaded to SFTP', `File uploaded to ${baiConverter.PATHS.POPULAR_BANK} folder: ${originalName}.bai`);
            
          } catch (uploadError) {
            log.error('Error uploading BAI file to SFTP', {
              file: `${originalName}.bai`,
              error: uploadError.message
            });
            addError('summarize', `Failed to upload ${originalName}.bai`, uploadError);
          }
          
          email.send({
            author: ownerId,
            recipients: [ownerId],
            subject: 'CSV to BAI Conversion Complete',
            body: `
              <h2>CSV to BAI Conversion Successful</h2>
              <p>Your CSV file has been successfully converted to BAI format and has been uploaded to the SFTP server in the ${baiConverter.PATHS.POPULAR_BANK} folder.</p>
              <p><strong>Source File:</strong> ${baiFileData.fileName}</p>
              <p><strong>BAI File:</strong> ${originalName}.bai</p>
              <p><strong>Completion Time:</strong> ${new Date().toISOString()}</p>
              <p>The original CSV file has been moved to the ${baiConverter.PATHS.CONVERTED_CSV} directory.</p>
            `,
            attachments: [baiFile]
          });
        } catch (fileError) {
          addError('summarize', `967871cf-ed10-44b4-b8f5-683cdb90f6c7 : processing BAI file for ${baiFileData.fileName}`, fileError);
        }
      });
    } catch (error) {
      addError('summarize', 'main function', error);
    }

    if (errors.length > 0) {
      sendConsolidatedErrorNotification();
    }
  }

  /**
   * Send consolidated error notification email with all collected errors
   */
  function sendConsolidatedErrorNotification() {
    try {
      const scriptObj = runtime.getCurrentScript();
      const ownerId = scriptObj.getParameter({
        name: 'custscript_csv_to_bai_owner_id'
      }) || DEFAULT_OWNER_ID;

      const errorSummary = errors.map((err, index) => `
        <div style="border: 1px solid #ccc; margin: 10px 0; padding: 10px;">
          <h4>Error ${index + 1}: ${err.stage}</h4>
          <p><strong>Context:</strong> ${err.context}</p>
          <p><strong>Message:</strong> ${err.message}</p>
          <p><strong>Time:</strong> ${err.timestamp}</p>
          <details>
            <summary>Stack Trace</summary>
            <pre>${err.stack || 'No stack trace available'}</pre>
          </details>
        </div>
      `).join('');

      email.send({
        author: ownerId,
        recipients: [ownerId],
        subject: `CSV to BAI Conversion Failed - ${errors.length} Error(s)`,
        body: `
          <h2>CSV to BAI Conversion Errors</h2>
          <p><strong>Total Errors:</strong> ${errors.length}</p>
          <p><strong>Script:</strong> ${scriptObj.id}</p>
          <p><strong>Deployment:</strong> ${scriptObj.deploymentId}</p>
          <p><strong>Run Time:</strong> ${new Date().toISOString()}</p>

          <h3>Error Details:</h3>
          ${errorSummary}
        `
      });

    } catch (emailError) {
      log.error('4eae5f1a-f8c4-405d-a26d-aa9bff9eb652 : Failed to send consolidated error notification', emailError);
    }
  }

  return {
    getInputData,
    map,
    reduce,
    summarize
  };
});
