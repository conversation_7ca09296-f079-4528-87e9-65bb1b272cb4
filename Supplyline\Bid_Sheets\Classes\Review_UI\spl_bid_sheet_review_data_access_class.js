/**
 * @description Data access layer for bid sheet review functionality
 *
 * @NApiVersion 2.1
 *
 * <AUTHOR>
 * @module spl_bid_sheet_review_data_access_class
 */
define([
    "exports",
    "../../../../Classes/vlmd_custom_error_object",
    "N/record",
    "N/query",
], (
    /** @type {any} */ exports,
    /** @type {any} */ CustomErrorObject,
    /** @type {any} */ record,
    /** @type {any} */ query
) => {

    class BidSheetReviewDataAccess {
        constructor() {
            this.customErrorObject = new CustomErrorObject();
        }

        /**
         * Get bid sheet items for a specific bid sheet
         * @param {string} bidSheetId - The ID of the bid sheet
         * @returns {Array} - Array of bid sheet items
         */
        getBidSheetItems(bidSheetId) {
            try {
                const bidSheetItemsQuery = `
                    SELECT
                        ROWNUM AS row_number,
                        bsi.id,
                        bsi.custrecord_spl_bsi_row_data,
                        bsi.custrecord_spl_bsi_potential_matches,
                        bsi.custrecord_spl_bsi_item_match,
                        bsi.custrecord_spl_bsi_match_override,
                        bsi.custrecord_spl_bsi_skipped,
                        item.id AS override_item_internal_id,
                        item.itemid AS override_item_id,
                        item.displayname AS override_display_name,
                        item.vendorname AS override_vendor_code
                    FROM
                        customrecord_spl_bid_sheet_items bsi
                    LEFT JOIN
                        item ON item.id = bsi.custrecord_spl_bsi_item_match
                    WHERE
                        bsi.custrecord_spl_bsi_bid_sheet = ?
                    ORDER BY
                        bsi.id
                `;

                const bidSheetItems = query.runSuiteQL({
                    query: bidSheetItemsQuery,
                    params: [bidSheetId]
                }).asMappedResults();

                return bidSheetItems;
            } catch (err) {
                this.customErrorObject.throwError({
                    summaryText: `ERROR_IN_GET_BID_SHEET_ITEMS`,
                    error: err,
                });
                throw err;
            }
        }

        /**
         * Get all items from the system
         * @returns {Array} - Array of items
         */
        getAllItems() {
            try {
                const itemsQuery = `
                    SELECT
                        item.id,
                        item.itemid,
                        item.displayname,
                        item.vendorname AS vendor_code,
                        BUILTIN.HIERARCHY(item.class, 'DISPLAY_JOINED') AS item_category
                    FROM
                        item
                    JOIN
                        itemSubsidiaryMap AS item_subsidiary ON item.id = item_subsidiary.item
                    WHERE
                        item.isinactive = 'F'
                        AND item_subsidiary.subsidiary = 1
                    ORDER BY
                        item.itemid
                `;

                // Use runSuiteQLPaged AS seen in spl_bid_sheet_processing_mr.js
                let itemPagedResults = query.runSuiteQLPaged({
                    query: itemsQuery,
                    pageSize: 1000
                });

                const items = [];

                // Process all pages
                for (let i = 0; i < itemPagedResults.pageRanges.length; i++) {
                    let currentPage = itemPagedResults.fetch(i);
                    let currentPagedData = currentPage.data.asMappedResults();

                    for (const item of currentPagedData) {
                        items.push({
                            id: item.id,
                            itemId: item.itemid,
                            displayName: item.displayname || '',
                            vendorCode: item.vendor_code || '',
                            category: item.item_category || ''
                        });
                    }
                }

                return items;
            } catch (err) {
                this.customErrorObject.throwError({
                    summaryText: `ERROR_IN_GET_ALL_ITEMS`,
                    error: err,
                });
                throw err;
            }
        }

        /**
         * Save bid sheet item record depending on the action provided
         * @param {string} action - The action to perform (verify, override, undooverride)
         * @param {string} itemId - The ID of the item to match
         * @param {string} bidItemId - The ID of the bid sheet item
         * @returns {Object} - The updated bid sheet item
         */
        saveBidSheetItem(action, itemId, bidItemId) {
            try {
                let bidSheetItemValues = {};

                switch (action) {
                    case 'verify':
                        bidSheetItemValues.custrecord_spl_bsi_item_match = itemId;
                        break;
                    case 'override':
                        bidSheetItemValues.custrecord_spl_bsi_item_match = itemId;
                        bidSheetItemValues.custrecord_spl_bsi_match_override = true;
                        break;
                    case 'undooverride':
                        bidSheetItemValues.custrecord_spl_bsi_item_match = '';
                        bidSheetItemValues.custrecord_spl_bsi_match_override = false;
                        bidSheetItemValues.custrecord_spl_bsi_skipped = false;
                        break;
                    case 'skip':
                        bidSheetItemValues.custrecord_spl_bsi_item_match = '';
                        bidSheetItemValues.custrecord_spl_bsi_match_override = false;
                        bidSheetItemValues.custrecord_spl_bsi_skipped = true;
                        break;
                    default:
                        break;
                }

                try {
                    record.submitFields({
                        type: 'customrecord_spl_bid_sheet_items',
                        id: bidItemId,
                        values: bidSheetItemValues
                    });
                } catch (submitErr) {
                    this.customErrorObject.throwError({
                        summaryText: `ERROR_SAVING_BID_SHEET_ITEM_${bidItemId}_${action}`,
                        error: submitErr
                    });
                    throw submitErr;
                }

                const updatedItemQuery = `
                    SELECT
                        ROWNUM AS row_number,
                        bsi.id,
                        bsi.custrecord_spl_bsi_bid_sheet,
                        bsi.custrecord_spl_bsi_row_data,
                        bsi.custrecord_spl_bsi_potential_matches,
                        bsi.custrecord_spl_bsi_item_match,
                        bsi.custrecord_spl_bsi_match_override,
                        item.id AS override_item_internal_id,
                        item.itemid AS override_item_id,
                        item.displayname AS override_display_name,
                        item.vendorname AS override_vendor_code
                    FROM
                        customrecord_spl_bid_sheet_items bsi
                    LEFT JOIN
                        item ON item.id = bsi.custrecord_spl_bsi_item_match
                    WHERE
                        bsi.id = ?
                `;

                const updatedItem = query.runSuiteQL({
                    query: updatedItemQuery,
                    params: [bidItemId]
                }).asMappedResults()[0];

                this.updateBidSheetStatus(updatedItem.custrecord_spl_bsi_bid_sheet);

                return updatedItem;
            } catch (err) {
                this.customErrorObject.throwError({
                    summaryText: `ERROR_IN_SAVE_BID_SHEET_ITEM`,
                    error: err
                });
                throw err;
            }
        }

        /**
         * Check and update if all bid sheet items are verified
         * @param {string} bidSheetId - The ID of the bid sheet
         * @returns {boolean} - True if all items are verified, false otherwise
         */
        updateBidSheetStatus(bidSheetId) {
            try {
                // Get all bid sheet items
                const bidSheetItemsQuery = `
                    SELECT
                        id
                    FROM
                        customrecord_spl_bid_sheet_items
                    WHERE
                        custrecord_spl_bsi_bid_sheet = ?
                `;

                const bidSheetItems = query.runSuiteQL({
                    query: bidSheetItemsQuery,
                    params: [bidSheetId]
                }).asMappedResults();
                
                // Check if all items are verified
                const allVerified = bidSheetItems.every(item => {
                    const itemQuery = `
                        SELECT
                            custrecord_spl_bsi_item_match,
                            custrecord_spl_bsi_skipped,
                            custrecord_spl_bsi_match_override
                        FROM
                            customrecord_spl_bid_sheet_items
                        WHERE
                            id = ?
                    `;

                    const itemResult = query.runSuiteQL({
                        query: itemQuery,
                        params: [item.id]
                    }).asMappedResults()[0];

                    const skipped = (itemResult.custrecord_spl_bsi_skipped === 'T') ? true : false;
                    const overridden = (itemResult.custrecord_spl_bsi_match_override === 'T') ? true : false;
                    const matched = (itemResult.custrecord_spl_bsi_item_match !== '' && itemResult.custrecord_spl_bsi_item_match !== null) ? true : false;

                    log.debug('verified', { cond: skipped || matched || overridden, skipped, matched, overridden });

                    return skipped || matched || overridden;
                });

                // Update bid sheet status
                record.submitFields({
                    type: 'customrecord_spl_bid_sheets',
                    id: bidSheetId,
                    values: {
                        custrecord_spl_bid_sheets_status: (allVerified) ? 1 : 2
                    }
                });

            } catch (err) {
                this.customErrorObject.throwError({
                    summaryText: `ERROR_IN_UPDATE_BID_SHEET_STATUS`,
                    error: err
                });
                throw err;
            }
        }

        /**
         * Get customer item price
         * @param {string} customerId - The ID of the customer
         * @param {Array} itemIds - Array of item IDs
         * @returns {Array} - Array of customer item rates
         */
        getCustomerItemPrice(customerId, itemIds) {
            try {
                // if all items null, return empty array
                if(itemIds.every(item => item === null)) return [];

                itemIds = itemIds.filter(item => item !== null);

                let purchaserQuery = /*SQL */ `
                    SELECT
                        integration.custrecord_spl_prchsng_fclty purchaser_id,
                        parent.id parent_id,
                        customer.id customer_id,
                    FROM
                        customer
                        LEFT JOIN customer parent ON customer.parent = parent.id
                        LEFT JOIN customrecord_vlmd_edi_integration integration ON (
                            parent.custentity_spl_edi_integration_record = integration.id
                            OR customer.custentity_spl_edi_integration_record = integration.id
                        )
                    WHERE
                        customer.id = ${customerId}`;

                let purchaserResult = query.runSuiteQL({
                    query: purchaserQuery,
                }).asMappedResults()?.[0];

                let purchaserId =
                    purchaserResult["purchaser_id"] ??
                    purchaserResult["parent_id"] ??
                    purchaserResult["customer_id"];

                if(!purchaserResult) this.customErrorObject.throwError({summaryText: 'ERROR_PROCESSING_BID_SHEET', error: 'No purchaser id found.'});

                let itemPricingQuery = /*SQL*/ `
                    SELECT
                        ip.item as item_internal_id,
                        ip.price rate,
                        IP.level price_level_id,
                        BUILTIN.DF(IP.level) price_level_name,
                        i.pricinggroup price_group_id,
                        BUILTin.DF(pricinggroup) price_group_name,
                    FROM
                        customerItemPricing AS IP
                        JOIN
                        item i
                        ON i.id = ip.item
                    WHERE
                        IP.customer = ${purchaserId}
                        AND IP.item IN (\'${itemIds.join(`','`)}\')
                    UNION
                    SELECT
                        i.id as item_internal_id,
                        p.unitPrice rate,
                        GP.level price_level_id,
                        BUILTIN.DF( GP.level) price_level_name,
                        GP.GROUP price_group_id,
                        BUILTIN.DF( GP.GROUP) price_group_name,
                    FROM
                        CustomerGroupPricing AS gp
                        INNER JOIN
                        item AS i
                        ON i.pricinggroup = gp.GROUP
                        LEFT JOIN
                        pricing p
                        ON p.pricelevel = gp.level
                        AND p.item = i.id
                    WHERE
                        gp.customer = ${purchaserId}
                        AND i.id IN (\'${itemIds.join(`','`)}\')
                    ORDER BY
                        price_level_id `;

                let customerItemRates = query.runSuiteQL({
                  query: itemPricingQuery,
                }).asMappedResults();

                if(!customerItemRates.length) this.customErrorObject.throwError({summaryText: 'ERROR_PROCESSING_BID_SHEET', error: 'No customer item rates found.'});

                return customerItemRates;
            } catch (queryErr) {
                this.customErrorObject.throwError({
                    summaryText: `ERROR_GETTING_CUSTOMER_ITEM_PRICE_${customerId}`,
                    error: queryErr
                });
                throw queryErr;
            }
        }

        /**
         * Get bid sheet information
         * @param {string} bidSheetId - The ID of the bid sheet
         * @returns {Object} - Bid sheet information
         */
        getBidSheetInformation(bidSheetId) {
            try {
                const bidSheetQuery = `
                    SELECT
                        bidsheet.id,
                        bidsheet.name,
                        bidsheet.custrecord_spl_bid_sheets_customer,
                        bidsheet.created,
                        customer.entityid,
                        customer.companyname,
                        COUNT(bsi.id) AS item_total
                    FROM
                        customrecord_spl_bid_sheets bidsheet
                        JOIN customrecord_spl_bid_sheet_items bsi ON bsi.custrecord_spl_bsi_bid_sheet = bidsheet.id
                        JOIN customer ON customer.id = bidsheet.custrecord_spl_bid_sheets_customer
                    WHERE
                        bidsheet.id = ?
                    GROUP BY
                        bidsheet.id,
                        bidsheet.name,
                        bidsheet.custrecord_spl_bid_sheets_customer,
                        bidsheet.created,
                        customer.entityid,
                        customer.companyname
                `;

                const bidSheetInformation = query.runSuiteQL({
                    query: bidSheetQuery,
                    params: [bidSheetId]
                }).asMappedResults();

                return bidSheetInformation[0];
            } catch (err) {
                this.customErrorObject.throwError({
                    summaryText: `ERROR_IN_GET_BID_SHEET_INFORMATION`,
                    error: err,
                });
                throw err;
            }
        }

        /**
         * Clear all verified and overridden items
         * @param {string} bidSheetId - The ID of the bid sheet
         * @returns {Object} - Result of the operation
         */
        clearVerifiedAndOverridenItems(bidSheetId) {
            try {
                // Get all bid sheet items with verified matches
                const bidSheetItemsQuery = `
                    SELECT
                        id
                    FROM
                        customrecord_spl_bid_sheet_items
                    WHERE
                        custrecord_spl_bsi_bid_sheet = ? AND
                        custrecord_spl_bsi_item_match IS NOT NULL
                `;

                const bidSheetItems = query.runSuiteQL({
                    query: bidSheetItemsQuery,
                    params: [bidSheetId]
                }).asMappedResults();

                // Clear the item match field for each item
                for (let item of bidSheetItems) {
                    record.submitFields({
                        type: 'customrecord_spl_bid_sheet_items',
                        id: item.id,
                        values: {
                            custrecord_spl_bsi_item_match: '',
                            custrecord_spl_bsi_match_override: false
                        }
                    });
                }

                return {
                    success: true,
                    clearedItems: bidSheetItems.length
                };
            } catch (err) {
                this.customErrorObject.throwError({
                    summaryText: `ERROR_IN_CLEAR_VERIFIED_AND_OVERRIDEN_ITEMS`,
                    error: err
                });
                throw err;
            }
        }
    }

    // Export the class
    exports.BidSheetReviewDataAccess = BidSheetReviewDataAccess;

    return BidSheetReviewDataAccess;
});
