/**
 * @description Factory class to handle processing and generation of CorePay payment files
 * @NApiVersion 2.1
 * <AUTHOR>
 * @module cntrs_corepay_factory
 */

define([
  "exports",
  "require",
  "N/log",
  "N/file",
  "N/sftp",
  "N/task",
  "N/error",
], (/** @type {any} */ exports, /** @type {any} */ require) => {
  const log = require("N/log");
  const file = require("N/file");
  const sftp = require("N/sftp");
  const task = require("N/task");
  const error = require("N/error");

  /**
   * CorePay Payment Processor Class
   * Handles the generation and processing of payment files for CorePay integration
   *
   * @class CorePayPaymentProcessor
   */
  class CorePayPaymentProcessor {
    constructor() {
      this.paymentsQueryString = ``;
      this.headerRow = `SubsidiaryID|Subsidiary_Name|Bank_Identifier|Vendor_ID|Vendor_Name|Vendor_Customer_Account|RemitToID|Addr1|Addr2|Addr3|City|State|Zip|Country|InvoiceID|ReferenceNumber|Bill_Transaction_ID|Payment_Transaction_ID|Orig-Amount|Amount-Paid|Terms|Discount|Payment_Memo`;
      this.fileLines = ``;
      this.fileObj = null;
      this.ftp = {
        username: "centers_health",
        keyId: "custkeycorepay",
        url: "sftp-integration.accrualify.com",
        port: 22,
        hostKey:
          "AAAAB3NzaC1yc2EAAAADAQABAAACAQCjmBaTL8fLxC983HAzI5ifcVSztzup6DIRXVUdl4SYvYQcL0SH9BQ2XnJzPNTU+/Y19HFECGRM54oMx8lZ5iQw7aVBxxyxiQzwn40mdqF6wMeCle22d9So12xc5PjPrE0V70/vFa1vV//NC+Wo46lQZ0gPSSX9hIyYCEBE78FqlNSP3mVXjhToi07XDNaujuzh5/M8F4KeHjkX3W+GXnI57DYdz8iHeI5Q+5T09wFA897jnhau6gLgFP8CX1jYNGv18gYuiH/kCmEEtPh55H2rcPlzS84BQgUUZcF4beqGJhwt2ynhf0aGeoPfa1DLm5OhucpkcJlWrcAxbpVkMHE3GtlCveUfKX5W/x/XB9BqWNXZdIQFL/+cAuHd8DdZKMk30VxPBSItGixXj3elZWYspLRqQ81Bv3r3Wk+nLHYEKnd894aaiXapeplDCHrpJQLv0uDArkmHtCD/lc5zS/Sr6sD5dmBp44Xtk28vjLWQ9pReKmKYfOrhNYYFVCB2ArjgAQhTzzXOgQwk9EFRyTHFiGgNMiicPvdXeA0Eq/PoLdqjl9uMp4gYMSWWTBbNr8Bx8zVq4WCHh4ZZVDbJKkQ7jXj4UsQeZ736UQZ73X/nH0LyDKr4hbV4CxGBvsHS0seFFOHOncaJ78IA/XpmuIT7PYtLeGXxeJBPGW0qC+zILw==",
      };
      this.referenceFolder = "993";
      this.taskId = null;
    }

    /**
     * Loads the query string to pull in the data for all payment records to be processed
     *
     * @returns {string} The SQL query string for payment information
     */
    getPaymentsQueryObj() {
      try {
        const sqlFile = file.load({
          id: "./corepay.sql",
          folder: "SuiteScripts/Centers/Corepay/Processors",
        });

        this.paymentsQueryString = sqlFile.getContents();

        log.audit("Payments Query String", this.paymentsQueryString);

        return this.paymentsQueryString;
      } catch (err) {
        throw err;
      }
    }

    /**
     * Adds body rows to the file from the context output
     * 
     * @param {Object} context - The script execution context
     * @param {Object} context.output - The output iterator from the reduce stage
     * @throws {Error} If adding rows fails
     */
    addBodyRows(context) {
      try {
        context.output &&
          context.output.iterator().each((key, value) => {
            this.fileLines += "\n" + value;
            return true;
          });
      } catch (err) {
        throw err;
      }
    }

    /**
     * Creates a CSV file with payment data     * 
     */ 
    createFile() {      
      try {
        let d = new Date();

        let fileName = `CP_Payments_${d
          .toISOString()
          .slice(5, 10)
          .replace("-", "_")}_${d.getFullYear()}_${d
          .toTimeString()
          .slice(0, 8)
          .replace(/:/g, "")}.csv`;

        this.fileObj = file.create({
          name: fileName,
          fileType: file.Type.CSV,
          contents: this.fileLines,
          folder: this.referenceFolder,
        });
      } catch (err) {
        throw err;
      }
    }

    /**
     * Creates a CSV file with payment data
     */
    uploadFile() {
      try {
        const connection = sftp.createConnection({
          username: this.ftp.username,
          keyId: this.ftp.keyId,
          url: this.ftp.url,
          port: this.ftp.port,
          hostKey: this.ftp.hostKey,
          directory: `/`,
        });

        connection.upload({
          file: this.fileObj,
          replaceExisting: true,
        });
      } catch (err) {
        throw err;
      }
    }

   
    /**
     * Saves the file to the reference folder in NetSuite
     */ 
    saveToReferenceFolder() {
      try {
        this.fileObj.save();
      } catch (err) {
        throw err;
      }
    }

    
    /**
     * Calls a MapReduce script to update payment status after processing
     * 
     * @param {Object} context - The script execution context
     * @param {Object} context.output - The output iterator from the reduce stage
     */
    callScriptToUpdatePaymentStatus(context) {
      try {
        let processedFiles = [];
        context.output &&
          context.output.iterator().each((key, value) => {
            processedFiles.push(key);
            return true;
          });

        const mrTask = task.create({
          taskType: task.TaskType.MAP_REDUCE,
          scriptId: "customscript_vlmd_crpy_updt_prcsd_pym_mr",
          params: {
            custscript_processed_payments: JSON.stringify(processedFiles),
          },
        });

        this.taskId = mrTask.submit();

        log.audit(`Updating Processed Payments`, `Task ID: ${this.taskId}`);
      } catch (err) {
        throw error.create({
          name: "ERROR_CREATING_CUSTOMER_PRICING_FILES_RECORD",
          message: `Error creating customer pricing files record: ${err}`,
        });
      }
    }
  }

  exports.CorePayPaymentProcessor = CorePayPaymentProcessor;
});
