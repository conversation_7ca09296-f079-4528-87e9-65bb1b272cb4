/**
 * @description Suitelet to review processed bid sheets
 *
 * @NApiVersion 2.1
 * @NScriptType Suitelet
 *
 * <AUTHOR>
 * @module spl_bid_sheet_review_sl
 */
var scriptURL,
    bidSheetId,
    serverWidget,
    url,
    runtime,
    query;

define([
    "require",
    "../../Classes/vlmd_custom_error_object",
    "N/ui/serverWidget",
    "N/url",
    "N/query",
    "N/runtime",
    "./Classes/Review_UI/spl_bid_sheet_review_data_access_class",
    "./Classes/Review_UI/spl_bid_sheet_review_ui_generator_class",
    "./Classes/Review_UI/spl_bid_sheet_review_client_scripts_class",
    "./Classes/Review_UI/spl_bid_sheet_review_admin_tools_class",
    "./Classes/Review_UI/spl_bid_sheet_review_file_utils_class"
], (require) => {

    const CustomError = require("../../Classes/vlmd_custom_error_object");
    const customErrorObject = new CustomError();
    serverWidget = require("N/ui/serverWidget");
    url = require("N/url");
    query = require("N/query");
    runtime = require("N/runtime");

    // Import classes directly without destructuring
    const BidSheetReviewDataAccess = require("./Classes/Review_UI/spl_bid_sheet_review_data_access_class");
    const BidSheetReviewUIGenerator = require("./Classes/Review_UI/spl_bid_sheet_review_ui_generator_class");
    const BidSheetReviewClientScripts = require("./Classes/Review_UI/spl_bid_sheet_review_client_scripts_class");
    const BidSheetReviewAdminTools = require("./Classes/Review_UI/spl_bid_sheet_review_admin_tools_class");
    const BidSheetReviewFileUtils = require("./Classes/Review_UI/spl_bid_sheet_review_file_utils_class");

    // Initialize classes
    const dataAccess = new BidSheetReviewDataAccess();
    const clientScripts = new BidSheetReviewClientScripts();
    const uiGenerator = new BidSheetReviewUIGenerator(clientScripts);
    const adminTools = new BidSheetReviewAdminTools();
    const fileUtils = new BidSheetReviewFileUtils(dataAccess);

    /**
     * Main entry point for the suitelet
     */
    function onRequest(context) {
        try {
            scriptURL = url.resolveScript({
                scriptId: runtime.getCurrentScript().id,
                deploymentId: runtime.getCurrentScript().deploymentId,
                returnExternalURL: false,
            });

            if (context.request.method === 'GET') {
                handleGet(context);
            } else {
                handlePost(context);
            }
        } catch (err) {
            customErrorObject.throwError({
                summaryText: `ERROR_IN_BID_SHEET_REVIEW_SL`,
                error: err,
            });
        }
    }

    /**
     * GET Request Function - Renders the HTML page and handles file download
     */
    function handleGet(context) {
        const bidSheetId = context.request.parameters.bidsheetid;
        const fileName = context.request.parameters.filename;
        
        if(context.request.parameters.hasOwnProperty("function")) {
            if(context.request.parameters["function"] === "downloadFile") {
                generateFile(context, bidSheetId, fileName);
            }
        } else {
            renderHTML(context, bidSheetId);
        }
    }

    /**
     * POST Request Function - Handles all POST requests and background processing for the HTML page
     */
    function handlePost(context) {
        // Process POST requests
        var requestPayload = JSON.parse(context.request.body);
        context.response.setHeader("Content-Type", "application/json");

        switch (requestPayload["function"]) {
            case "getBidSheetItems":
                getBidSheetItems(context, requestPayload);
                break;
            case "saveBidSheetItem":
                saveBidSheetItem(context, requestPayload);
                break;
            case "clearVerifiedAndOverridenItems":
                clearVerifiedAndOverridenItems(context, requestPayload);
                break;
            case "getAllItems":
                getAllItems(context, requestPayload);
                break;
            default:
                customErrorObject.throwError({
                    summaryText: `UNSUPPORTED_FUNCTION_${requestPayload["function"]}`,
                    error: new Error(`Unsupported function: ${requestPayload["function"]}`)
                });
        }
    }

    /**
     * POST Request Function - Get all items from the system
     */
    function getBidSheetItems(context, requestPayload) {
        let responsePayload;

        try {
            let paramsJsonObj = JSON.parse(requestPayload.paramsJsonObj);
            let bidSheetId = paramsJsonObj.bidSheetId;

            const bidSheetItems = dataAccess.getBidSheetItems(bidSheetId);
            responsePayload = { bidSheetItems };
        } catch (err) {
            customErrorObject.throwError({
                summaryText: `ERROR_IN_GET_BID_SHEET_ITEMS`,
                error: err,
            });

            responsePayload = { error: err };
        }

        context.response.write(JSON.stringify(responsePayload));
    }

    /**
     * POST Request Function - Gets all supplyline items
     */
    function getAllItems(context, requestPayload) {
        let responsePayload;

        try {
            const items = dataAccess.getAllItems();
            responsePayload = { items };
        } catch (err) {
            customErrorObject.throwError({
                summaryText: `ERROR_IN_GET_ALL_ITEMS`,
                error: err,
            });

            responsePayload = { error: err.message };
        }

        context.response.write(JSON.stringify(responsePayload));
    }

    /**
     * POST Request Function - Saves bid sheet item record depending on the action provided
     * 'verify': only saves the item match
     * 'overide': saves the selected item and sets the override flag to true
     * 'undooverride': clears the item match and sets the override flag to false
     */
    function saveBidSheetItem(context, requestPayload) {
        let responsePayload;
        try {
            let paramsJsonObj = JSON.parse(requestPayload.paramsJsonObj);
            let action = paramsJsonObj.action;
            let itemId = paramsJsonObj.itemId;
            let bidItemId = paramsJsonObj.bidItemId;

            const updatedItem = dataAccess.saveBidSheetItem(action, itemId, bidItemId);

            responsePayload = {
                success: true,
                updatedItem: updatedItem
            };

        } catch (err) {
            customErrorObject.throwError({
                summaryText: `ERROR_IN_SAVE_BID_SHEET_ITEM`,
                error: err
            });

            responsePayload = { error: err };
        }

        context.response.write(JSON.stringify(responsePayload));
    }

    /**
     * GET Request Function - Generates CSV and saves it to both file cabinet and link it to the bid sheet record
     */
    function generateFile(context, bidSheetId, fileName) {
        try {
            const verifiedBidSheetFile = fileUtils.generateFile(bidSheetId, fileName);
            context.response.writeFile(verifiedBidSheetFile);
        } catch (err) {
            customErrorObject.throwError({
                summaryText: `ERROR_IN_GENERATE_FILE`,
                error: err,
            });
        }
    }

    /**
     * GET Request Function - Render the HTML page using NS serverWidget API
     */
    function renderHTML(context, bidSheetId) {
        try {
            const form = serverWidget.createForm({
                title: 'Bid Sheet Review'
            });

            if(bidSheetId) {
                let htmlField = form.addField({
                    id: "custpage_field_html",
                    type: serverWidget.FieldType.INLINEHTML,
                    label: "HTML",
                });

                // Get bid sheet information for the UI
                const bidSheetInformation = dataAccess.getBidSheetInformation(bidSheetId);

                // Generate HTML with all components
                htmlField.defaultValue = uiGenerator.generateHTML(bidSheetId, scriptURL, bidSheetInformation);
            } else {
                // add bid sheet selector here
            }

            context.response.writePage(form);
        } catch (err) {
            customErrorObject.throwError({
                summaryText: `ERROR_IN_RENDER_HTML`,
                error: err
            });
        }
    }

    /**
     * Admin Tools
     * POST Request Function - Clear all verified and overridden items
     */
    function clearVerifiedAndOverridenItems(context, requestPayload) {
        let responsePayload;

        try {
            let paramsJsonObj = JSON.parse(requestPayload.paramsJsonObj);
            let bidSheetId = paramsJsonObj.bidSheetId;

            responsePayload = adminTools.clearVerifiedAndOverridenItems(bidSheetId);
        } catch (err) {
            customErrorObject.throwError({
                summaryText: `ERROR_IN_CLEAR_VERIFIED_AND_OVERRIDEN_ITEMS`,
                error: err
            });

            responsePayload = { error: err };
        }

        context.response.write(JSON.stringify(responsePayload));
    }

    return {
        onRequest: onRequest
    };
});
