/**
 *
 * @NApiVersion 2.1
 * @NScriptType Suitelet
 * @NModuleScope Public
 *
 * @description Suitelet for testing Snowflake Connection/Data
 * <AUTHOR>
 */
define(["N/ui/serverWidget", "N/https", "N/log", "./snowflake_oauth_lib"], function (
  serverWidget,
  https,
  log,
  snowflakeOAuth
) {

  /**
   * Executes a query against Snowflake using OAuth
   * @param {string} query - SQL query to execute
   * @returns {Object} Query results and response code
   */
  function executeSnowflakeQuery(query) {
    try {
      const token = snowflakeOAuth.getValidOAuthToken();

      if (!token) {
        return {
          error: "No valid OAuth token available. Please check your OAuth configuration."
        };
      }

      const data = {
        statement: query || "SELECT ptkey from bt_views.public.bt_patient where ptkey = 188197",
        timeout: 60,
        resultSetMetaData: {
          format: "json"
        },
        database: snowflakeOAuth.SNOWFLAKE_CONFIG.database,
        schema: snowflakeOAuth.SNOWFLAKE_CONFIG.schema,
        warehouse: snowflakeOAuth.SNOWFLAKE_CONFIG.warehouse,
        role: snowflakeOAuth.SNOWFLAKE_CONFIG.role
      };

      const headers = {
        "User-Agent": "NetSuite-Snowflake-Connector/1.0",
        "Accept": "application/json",
        "Content-Type": "application/json",
        "Authorization": `Bearer ${token}`
      };

      const response = https.post({
        url: "https://" + snowflakeOAuth.SNOWFLAKE_CONFIG.account + ".snowflakecomputing.com/api/v2/statements",
        body: JSON.stringify(data),
        headers: headers
      });

      return {
        code: response.code,
        body: response.body
      };
    } catch (e) {
      log.error('853d5d45-2098-46aa-8b0c-1cd250ab9318 : Error executing Snowflake query', e);
      return {
        error: e.message
      };
    }
  }
  /**
   * Suitelet entry point
   * @param {Object} context - Suitelet context
   * @param {ServerRequest} context.request - Incoming request
   * @param {ServerResponse} context.response - Suitelet response
   */
  function onRequest(context) {
    if (context.request.method === "GET") {
      const form = serverWidget.createForm({
        title: "Snowflake Customer Sync Test",
      });

      form.addField({
        id: "custpage_query",
        type: serverWidget.FieldType.TEXTAREA,
        label: "SQL Query (Optional)"
      }).defaultValue = "SELECT ptkey from bt_views.public.bt_patient where ptkey = 188197";

      form.addSubmitButton({
        label: "Run Request"
      });

      context.response.writePage(form);
    } else {
      try {
        const query = context.request.parameters.custpage_query;
        const response = executeSnowflakeQuery(query);

        const resultForm = serverWidget.createForm({
          title: "Snowflake API Response"
        });

        const responseField = resultForm.addField({
          id: "custpage_response",
          type: serverWidget.FieldType.LONGTEXT,
          label: "Response"
        });

        let formattedResponse;
        try {
          if (response.body) {
            const jsonResponse = JSON.parse(response.body);
            formattedResponse = JSON.stringify(jsonResponse, null, 2);
          } else if (response.error) {
            formattedResponse = "Error: " + response.error;
          }
        } catch (e) {
          formattedResponse = response.body || JSON.stringify(response);
        }

        responseField.defaultValue = formattedResponse;

        if (response.code) {
          resultForm.addField({
            id: "custpage_status",
            type: serverWidget.FieldType.TEXT,
            label: "Status Code"
          }).defaultValue = response.code.toString();
        }

        resultForm.addButton({
          id: "custpage_back",
          label: "Back",
          functionName: "window.history.back();"
        });

        context.response.writePage(resultForm);
      } catch (e) {
        const errorForm = serverWidget.createForm({
          title: "Error"
        });

        errorForm.addField({
          id: "custpage_error",
          type: serverWidget.FieldType.INLINEHTML,
          label: "Error"
        }).defaultValue = `<h2>Error</h2><p>${e.name}: ${e.message}</p>`;

        errorForm.addButton({
          id: "custpage_back",
          label: "Back",
          functionName: "window.history.back();"
        });

        context.response.writePage(errorForm);
      }
    }
  }

  return {
    onRequest,
  };

});