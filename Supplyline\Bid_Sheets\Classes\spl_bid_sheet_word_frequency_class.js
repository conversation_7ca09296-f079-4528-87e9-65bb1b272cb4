/**
 * @description WordFrequencyAnalyzer class for analyzing keyword frequency and importance
 *
 * @NApiVersion 2.1
 * 
 * <AUTHOR>
 * @module spl_bid_sheet_word_frequency_class
 */

define([
    "require",
    "exports",
], (
    /** @type {any} */ require,
    /** @type {any} */ exports,
    log
) => {
    const { TextMatching } = require("./spl_bid_sheet_text_matching_class");

    /**
     * WordFrequencyAnalyzer class for analyzing keyword frequency and importance
     *
     * @class
     */
    class WordFrequencyAnalyzer {
        /**
         * Create a new WordFrequencyAnalyzer instance
         * @param {Object} options - Configuration options
         * @param {Object} [options.textProcessor=null] - TextMatching instance for text processing
         * @param {Object} [options.fileModule=null] - NetSuite file module for file operations
         * @param {Object} [options.wordFrequencyMap=null] - Pre-loaded word frequency map
         */
        constructor(options = {}) {
            const {
                textProcessor = null,
                fileModule = null,
                wordFrequencyMap = null
            } = options;

            this.textProcessor = textProcessor || new TextMatching();
            this.file = fileModule;

            // Initialize word frequency data
            this.wordFrequencyMap = wordFrequencyMap || {};
            this.lastUpdated = null;
        }

        /**
         * Builds a word frequency map from all items
         * @param {Array} items - Array of items to analyze
         * @returns {Object} Word frequency map
         */
        buildWordFrequencyMap(items) {
            try {
                const wordFrequencyMap = {};
                let totalItems = items.length;

                // Process each item
                items.forEach(item => {
                    // Combine all relevant text fields
                    const itemText = [
                        item.itemId,
                        item.displayName,
                        item.vendorCode,
                        item.category
                    ].filter(Boolean).join(' ').toLowerCase();

                    // Generate tokens
                    const tokens = this.textProcessor.generateTokens({
                        stringToTokenize: itemText,
                        removeFillerWords: true
                    });

                    // Track unique words per item (to avoid counting repeated words in same item)
                    const uniqueTokensInItem = new Set(tokens);

                    // Update document frequency (number of items containing each word)
                    uniqueTokensInItem.forEach(token => {
                        if (!wordFrequencyMap[token]) {
                            wordFrequencyMap[token] = {
                                count: 0,          // Total occurrences
                                docFrequency: 0,   // Number of items containing this word
                                idf: 0             // Inverse document frequency (calculated later)
                            };
                        }

                        wordFrequencyMap[token].docFrequency++;
                        wordFrequencyMap[token].count += tokens.filter(t => t === token).length;
                    });
                });

                // Calculate IDF (Inverse Document Frequency) for each word
                Object.keys(wordFrequencyMap).forEach(word => {
                    const docFrequency = wordFrequencyMap[word].docFrequency;
                    // IDF formula: log(totalDocuments / documentsContainingTerm)
                    wordFrequencyMap[word].idf = Math.log(totalItems / docFrequency);
                });

                this.wordFrequencyMap = wordFrequencyMap;
                this.lastUpdated = new Date();

                return wordFrequencyMap;
            } catch (err) {
                log.error({
                    title: "Error building word frequency map",
                    details: err
                });
                return {};
            }
        }

        /**
         * Extracts potential keywords from a description based on frequency map
         * @param {string} description - Customer description
         * @param {Object} options - Options for keyword extraction
         * @param {number} [options.maxKeywords=10] - Maximum number of keywords to extract
         * @param {number} [options.minDocFrequency=1] - Minimum document frequency for keywords (default: 1)
         * @param {boolean} [options.sortByCount=true] - Sort by count (true) or doc frequency (false)
         * @returns {Array} Extracted keywords with frequency information
         */
        extractKeywords(description, options = {}) {
            const {
                maxKeywords = 10,
                minDocFrequency = 1,
                sortByCount = true
            } = options;

            try {
                // Check if we have a word frequency map - use size check instead of Object.keys
                if (!this.wordFrequencyMap || Object.keys(this.wordFrequencyMap).length === 0) {
                    throw new Error("Word frequency map not loaded");
                }

                // Generate tokens from description
                const tokens = this.textProcessor.generateTokens({
                    stringToTokenize: description,
                    removeFillerWords: true
                });

                // Use a Set to track unique tokens for O(1) lookups
                const uniqueTokens = new Set(tokens);
                const keywordFrequencies = [];
                
                // Process each unique token only once
                uniqueTokens.forEach(token => {
                    const freqData = this.wordFrequencyMap[token];
                    if (!freqData) return;
                    
                    const docFrequency = freqData.docFrequency;
                    
                    // Include tokens that meet minimum doc frequency
                    if (docFrequency >= minDocFrequency) {
                        keywordFrequencies.push({
                            keyword: token,
                            count: freqData.count,
                            docFrequency: docFrequency,
                            idf: freqData.idf
                        });
                    }
                });

                // Create a comparison function once instead of inline
                const compareFunction = sortByCount 
                    ? (a, b) => b.count - a.count
                    : (a, b) => b.docFrequency - a.docFrequency;
                    
                // Sort by selected criteria
                keywordFrequencies.sort(compareFunction);
                
                // Return all keywords if maxKeywords is -1, otherwise limit to maxKeywords
                return maxKeywords === -1 ? keywordFrequencies : keywordFrequencies.slice(0, maxKeywords);
            } catch (err) {
                log.error({
                    title: "Error extracting keywords",
                    details: err
                });
                return [];
            }
        }

        /**
         * Finds items matching the customer description using keyword frequency analysis
         * @param {string} customerDescription - Customer description to match
         * @param {Array} items - Items to search
         * @param {Object} options - Search options
         * @param {number} [options.maxResults=5] - Maximum number of results
         * @param {number} [options.minScore=0.3] - Minimum score for matches
         * @param {number} [options.keywordImportanceThreshold=0.1] - Minimum importance score for keywords
         * @param {number} [options.maxKeywords=10] - Maximum number of keywords to use for matching
         * @param {Object} [options.wordFrequencyMap=null] - Word frequency map for filtering keywords
         * @param {Object} [options.itemIndex=null] - Item index for faster searching. Structure should be a map where
         *                                           keys are keywords and values are arrays of item IDs that contain those keywords
         * @returns {Array} Matching items with scores
         */
        findItemsByKeywords(customerDescription, items, options = {}) {
            const {
                maxResults = 5,
                minScore = 0.3,
                keywordImportanceThreshold = 0.1,
                maxKeywords = 10,
                wordFrequencyMap = null,
                itemIndex = null
            } = options;

            try {
                // Use the instance's word frequency map if not provided in options
                const frequencyMap = wordFrequencyMap || this.wordFrequencyMap;

                // Check if we have a word frequency map
                if (Object.keys(frequencyMap).length === 0) {
                    log.debug('WordFrequencyAnalyzer', 'No word frequency map available, falling back to basic matching');
                }

                // Step 1: Generate tokens from the customer description
                const tokens = this.textProcessor.generateTokens({
                    stringToTokenize: customerDescription,
                    removeFillerWords: true
                });

                // Step 2: Calculate term frequency for this description
                const termFrequency = {};
                tokens.forEach(token => {
                    termFrequency[token] = (termFrequency[token] || 0) + 1;
                });

                // Step 3: Filter keywords based on frequency data and calculate importance
                const keywordScores = [];
                Object.keys(termFrequency).forEach(token => {
                    // If we have frequency data, use it to calculate importance
                    if (frequencyMap[token]) {
                        const tf = termFrequency[token] / tokens.length;
                        const idf = frequencyMap[token].idf;
                        const tfidf = tf * idf;

                        if (tfidf >= keywordImportanceThreshold) {
                            keywordScores.push({
                                keyword: token,
                                score: tfidf,
                                frequency: frequencyMap[token].count,
                                idf: idf
                            });
                        }
                    } else if (Object.keys(frequencyMap).length === 0) {
                        // If no frequency data available, use all tokens with equal weight
                        keywordScores.push({
                            keyword: token,
                            score: 1.0,
                            frequency: 1,
                            idf: 1
                        });
                    }
                });

                // Sort by score and get top keywords
                const topKeywords = keywordScores
                    .sort((a, b) => b.score - a.score)
                    .slice(0, maxKeywords);

                log.debug('WordFrequencyAnalyzer', `Using ${topKeywords.length} keywords for matching`);

                const results = [];

                // Step 4: Find matching items using filtered keywords
                if (itemIndex && Object.keys(itemIndex).length > 0) {
                    // Use item index for faster searching
                    log.debug('WordFrequencyAnalyzer', 'Using item index for searching');

                    // Find items that contain the top keywords
                    const matchingItemIds = new Set();
                    const itemScores = {};

                    // First pass: collect all item IDs that match any of our keywords
                    topKeywords.forEach(keywordObj => {
                        const keyword = keywordObj.keyword;
                        const keywordImportance = keywordObj.score;

                        // Get items containing this keyword from the index
                        const itemsWithKeyword = itemIndex[keyword] || [];

                        // Add each item to our tracking sets
                        itemsWithKeyword.forEach(itemId => {
                            matchingItemIds.add(itemId);

                            // Initialize or update the item's score
                            if (!itemScores[itemId]) {
                                itemScores[itemId] = {
                                    matchScore: 0,
                                    maxPossibleScore: 0,
                                    matchedKeywords: []
                                };
                            }

                            // Add to the item's score
                            itemScores[itemId].matchScore += keywordImportance;
                            itemScores[itemId].matchedKeywords.push(keyword);
                        });

                        // Add to max possible score for all items
                        Object.keys(itemScores).forEach(id => {
                            itemScores[id].maxPossibleScore += keywordImportance;
                        });
                    });

                    // Second pass: calculate final scores and create result objects
                    matchingItemIds.forEach(itemId => {
                        const item = items.find(i => i.id === itemId);
                        if (!item) return;

                        const scoreData = itemScores[itemId];

                        // Normalize score (0-1 range)
                        const normalizedScore = scoreData.maxPossibleScore > 0 ?
                            scoreData.matchScore / scoreData.maxPossibleScore : 0;

                        if (normalizedScore >= minScore) {
                            results.push({
                                item: item,
                                score: normalizedScore,
                                matchedKeywords: scoreData.matchedKeywords
                            });
                        }
                    });
                } else {
                    // Fallback to searching all items if no index is available
                    log.debug('WordFrequencyAnalyzer', 'No item index available, scanning all items');

                    items.forEach(item => {
                        // Combine all relevant text fields
                        const itemText = [
                            item.itemId,
                            item.displayName,
                            item.vendorCode,
                            item.category
                        ].filter(Boolean).join(' ').toLowerCase();

                        // Generate tokens
                        const itemTokens = this.textProcessor.generateTokens({
                            stringToTokenize: itemText,
                            removeFillerWords: true
                        });

                        // Calculate match score based on keywords
                        let matchScore = 0;
                        let maxPossibleScore = 0;
                        const matchedKeywords = [];

                        topKeywords.forEach(keywordObj => {
                            const keyword = keywordObj.keyword;
                            const keywordImportance = keywordObj.score;

                            // Add to max possible score
                            maxPossibleScore += keywordImportance;

                            // Check if item contains this keyword
                            if (itemTokens.includes(keyword)) {
                                matchScore += keywordImportance;
                                matchedKeywords.push(keyword);
                            }
                        });

                        // Normalize score (0-1 range)
                        const normalizedScore = maxPossibleScore > 0 ?
                            matchScore / maxPossibleScore : 0;

                        if (normalizedScore >= minScore) {
                            results.push({
                                item: item,
                                score: normalizedScore,
                                matchedKeywords: matchedKeywords
                            });
                        }
                    });
                }

                // Sort by score and limit results
                return results
                    .sort((a, b) => b.score - a.score)
                    .slice(0, maxResults);
            } catch (err) {
                log.error({
                    title: "Error finding items by keywords",
                    details: err
                });
                return [];
            }
        }

        /**
         * Saves word frequency map to file
         * @param {string} fileName - File name
         * @param {number} folderId - Folder ID
         * @returns {Object} Result with file ID
         */
        saveWordFrequencyMapToFile(fileName = 'bid_sheet_word_frequency.json', folderId) {
            try {
                if (!this.file) {
                    throw new Error("File module not provided");
                }

                const data = {
                    wordFrequencyMap: this.wordFrequencyMap,
                    lastUpdated: new Date().toISOString()
                };

                const fileContent = JSON.stringify(data);

                // Create or overwrite the file
                const fileObj = this.file.create({
                    name: fileName,
                    fileType: this.file.Type.JSON,
                    contents: fileContent,
                    folder: folderId
                });

                const fileId = fileObj.save();

                return {
                    fileId: fileId,
                    fileName: fileName
                };
            } catch (err) {
                log.error({
                    title: "Error saving word frequency map to file",
                    details: err
                });
                return null;
            }
        }

        /**
         * Loads word frequency map from file
         * @param {string|number} fileIdOrPath - File ID or path
         * @returns {Object} Loaded word frequency map
         */
        loadWordFrequencyMapFromFile(fileIdOrPath) {
            try {
                if (!this.file) {
                    throw new Error("File module not provided");
                }

                // Load the file
                const fileObj = this.file.load({
                    id: fileIdOrPath
                });

                const fileContent = fileObj.getContents();
                const data = JSON.parse(fileContent);

                this.wordFrequencyMap = data.wordFrequencyMap;
                this.lastUpdated = new Date(data.lastUpdated);

                return {
                    wordFrequencyMap: this.wordFrequencyMap,
                    lastUpdated: this.lastUpdated
                };
            } catch (err) {
                log.error({
                    title: "Error loading word frequency map from file",
                    details: err
                });
                return null;
            }
        }
    }

    // Export the class
    exports.WordFrequencyAnalyzer = WordFrequencyAnalyzer;

    return WordFrequencyAnalyzer;
});


