/**
 * @description Bridge RIP Vendor class for Fedway
 *
 * @NApiVersion 2.1
 * <AUTHOR>
 */

define(["exports", "require", "N/error", "./brdg_rip_vendor"], function (
  /** @type {any} */ exports,
  /** @type {any} */ require
) {
  const error = require("N/error");
  const { BridgeRIPVendor } = require("./brdg_rip_vendor");

  // Includes the added element for line count
  const ROW_LENGTH = 14;

  // Capture segments with commas enclosed in double quotes
  const COMMA_DELIMITED_SEGMENT = /(?:\"([^\"]*)\")|([^",]+)/g;

  // 1C $12 stands for 1 Case(s) $12
  const QUANTITY_UNIT_AMOUNT = /(\d+)([A-Z])\s*\$(\d+)/;

  // Date Format e.g. 1/1/2025, 12/5/2020
  const DATE_FORMAT = /\b\d{1,2}\/\d{1,2}\/\d{4}\b/;

  /**
   * Return the unit of measure based on the abbreviation
   *
   * @param {string} uom Abbreviation
   * @returns {string} Unit of measure
   */
  const getUnitOfMeasure = (uom) => {
    switch (uom) {
      case "C":
        return "Case(s)";
      case "B":
        return "Bottle(s)";
      default:
        return "Unit(s)";
    }
  };

  /**
   * Bridge RIP Fedway Class
   */
  class BridgeRIPFedway extends BridgeRIPVendor {
    /** @param {{[key:string]: any}} props Constructor params */
    constructor(props) {
      super(props);
    }

    /**
     * Split the row string from the CSV file and attach the line index
     *
     * @param {import("N/file").File} ripFile NetSuite File
     * @returns {any[]} Row strings split by commas
     */
    splitLines(ripFile) {
      const fileIterator = ripFile.lines.iterator();

      let lineCount = 0;
      let expectedHeader =
        "RIP Name,Product SKU,FROMDATE,TODATE,RIP CODE,Item Name,SIZE,PACK,CountsAs,Month,FAMILY,RIP Level,ALL RIPS CUST";
      let extractedHeader = "";

      // Since we iterate through the file from top to bottom, we cannot skip the header
      // Iterate over the header by returning false after reading it
      fileIterator.each((/** @type {any} */ header) => {
        extractedHeader = header.value.trim();
        return false;
      });

      if (expectedHeader !== extractedHeader) {
        throw this.customErrorObject.updateError({
          errorType: this.customErrorObject.ErrorTypes.INVALID_DATA,
          summary: "22175d41-84e7-44e5-b851-37ec3b1d19ff: SPLIT_LINES_ERROR",
          details: `The header from the input file (${extractedHeader}) doesn't match the expected header for Federal Wine & Spirits (${expectedHeader}).`,
        });
      }

      // Continue reading the file, and store the contents to a data lines array
      const /** @type {any[]} */ dataArr = [];
      fileIterator.each((/** @type {any} */ line) => {
        const cellValues = line.value.trim().match(COMMA_DELIMITED_SEGMENT);

        let lineArr = [];
        if (!cellValues) {
          this.customErrorObject.updateError({
            errorType: this.customErrorObject.ErrorTypes.INVALID_DATA,
            summary: `93b158a5-86b7-419a-91b2-309e2d730987: LINE_${
              lineCount + 1
            }_FORMAT_ERROR`,
            details: `Line ${
              lineCount + 1
            } does not match the expected format or has empty cells.`,
          });
        } else {
          lineArr = cellValues.map((/** @type {string} */ field) =>
            field.replace(/^"|"$/g, "")
          );
          dataArr.push([...lineArr, lineCount]);
        }
        lineCount++;

        return true;
      });

      if (this.customErrorObject.ERROR_TYPE) {
        this.customErrorObject.throwError({
          summaryText:
            "0ed62bd6-809c-4c7c-82c1-7276a5dc1285: SPLIT_LINES_ERROR",
          error: error.create({
            name: "SPLIT_LINES_ERROR",
            message: `Errors encountered while splitting the lines from the input file.`,
          }),
          recordId: null,
          recordName: null,
          recordType: null,
          errorWillBeGrouped: false,
        });
      }

      return dataArr;
    }

    /**
     * Create an object from the line extracted from the CSV file
     *
     * @param {any[]} fields Array of column values
     * @returns {{[key:string]: any}} Key-value pairs of column values
     */
    parseLine(fields) {
      if (fields.length !== ROW_LENGTH) {
        throw this.customErrorObject.updateError({
          errorType: this.customErrorObject.ErrorTypes.INVALID_DATA,
          summary: "89705615-97dc-4b77-8125-2e8ebd759a62: INVALID_ROW_LENGTH",
          details: "Row should have exactly 13 values.",
        });
      }

      const fieldNames = [
        "ripName",
        "sku",
        "fromDate",
        "toDate",
        "ripCode",
        "description",
        "comments",
        "pack",
        "countAs",
        "month",
        "family",
        "level",
        "allRips",
        "lineCount",
      ];
      const trimIfString = (/** @type {any} */ x) =>
        typeof x === "string" ? x.trim() : x;
      const values = fields.map(trimIfString);
      const rip = Object.fromEntries(
        fieldNames.map((key, index) => [key, values[index]])
      );

      const fromDateMatch = rip.fromDate.match(DATE_FORMAT);
      if (!fromDateMatch) {
        this.customErrorObject.updateError({
          errorType: this.customErrorObject.ErrorTypes.SYNTAX_ERROR,
          summary:
            "6f0084bc-27ad-433d-881c-a9b4b93a0f0b: FROMDATE_FORMAT_MISMATCH",
          details: `FROMDATE from the parsed row does not match the expected format: ${rip.fromDate}`,
        });
      }

      const toDateMatch = rip.toDate.match(DATE_FORMAT);
      if (!toDateMatch) {
        this.customErrorObject.updateError({
          errorType: this.customErrorObject.ErrorTypes.SYNTAX_ERROR,
          summary:
            "f02197af-c257-44b6-acf9-bd8556f57a25: TODATE_FORMAT_MISMATCH",
          details: `TODATE from the parsed row does not match the expected format: ${rip.toDate}`,
        });
      }

      if (!rip.countAs || !parseFloat(rip.countAs)) {
        this.customErrorObject.updateError({
          errorType: this.customErrorObject.ErrorTypes.SYNTAX_ERROR,
          summary:
            "fb1f356f-9de3-42c1-932d-b8f3aa4db7b5: COUNT_AS_FORMAT_MISMATCH",
          details: `CountsAs from the parsed row does not match the expected format: ${rip.countAs}`,
        });
      }

      // Add quantity, unit of measure and amount as properties based on the RIP Level e.g. 1C $12
      const level = rip.level.match(QUANTITY_UNIT_AMOUNT);

      if (!level || level.length < 4) {
        this.customErrorObject.updateError({
          errorType: this.customErrorObject.ErrorTypes.SYNTAX_ERROR,
          summary:
            "d429c3b2-93e0-4d62-97e2-0a496f6399e0: RIP_LEVEL_FORMAT_MISMATCH",
          details: `RIP Level from the parsed row does not match the expected format: ${rip.level}`,
        });
      }

      if (this.customErrorObject.ERROR_TYPE) {
        this.customErrorObject.throwError({
          summaryText: "f5f94b15-1563-4cda-92e6-b0aa781499a1: PARSING_ERROR",
          error: error.create({
            name: "PARSING_ERROR",
            message: `Errors enountered while parsing the row from the input file.`,
          }),
          recordId: null,
          recordName: null,
          recordType: null,
          errorWillBeGrouped: false,
        });
      }

      rip.qty = Number(level[1] || 0);
      rip.uom = getUnitOfMeasure(level[2]);
      rip.amt = Number(level[3] || 0);

      return rip;
    }

    /**
     * Merge RIP Levels into a single row
     * - Use parseFloat to remove unit from countAs
     *
     * @param {string[]} levels RIP levels in JSON String format
     * @returns {any[]} Merged levels
     */
    mergeLevels(levels) {
      const baseLevel = JSON.parse(levels[0]);
      const /** @type {{[key:string]: any}} */ ripImportData = {
          sku: baseLevel.sku,
          ripCode: baseLevel.ripCode,
          brandRegistration: null,
          fromDate: baseLevel.fromDate,
          toDate: baseLevel.toDate,
          description: baseLevel.description,
        };

      // Ensure the order of tier levels are sorted by increasing amount
      levels.sort(
        (a, b) =>
          Number(JSON.parse(a)?.amt || 0) - Number(JSON.parse(b)?.amt || 0)
      );

      for (let i = 0; i < 5; i++) {
        const level = i < levels.length ? JSON.parse(levels[i]) : {};
        ripImportData[`uom${i + 1}`] = level.uom;
        ripImportData[`qty${i + 1}`] = level.qty || "0";
        ripImportData[`amt${i + 1}`] = level.amt || "0";
      }

      ripImportData.countAs = parseFloat(baseLevel.countAs);
      ripImportData.comments = baseLevel.comments;
      ripImportData.lineCount = baseLevel.lineCount;

      return Object.values(ripImportData);
    }
  }

  exports.BridgeRIPFedway = BridgeRIPFedway;
});
