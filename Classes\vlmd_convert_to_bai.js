/**
 * @description Class to convert files to BAI and upload to correct folders
*
 * @NApiVersion 2.1
 */
define(['N/sftp', 'N/log'], function(sftp, log) {
    /**
     * BAI File Converter Class
     * Handles conversion of CSV files to BAI format and manages BAI files on SFTP
     */
    class BaiConverter {
        constructor(sftpConfig) {
            this.SFTP_CONFIG = sftpConfig;
            this.sftp = sftp;
            this.log = log;
            
            this.PATHS = {
                ROOT: "/",
                POPULAR_BANK: "PopularBank/",
                CONVERT_TO_BAI: "ConvertToBAI/"
            };

            // Define BAI transaction codes
            this.BAI_TRANS_CODES = {
                CREDIT: '399', // Credit
                DEBIT: '699'   // Debit
            };
        }

        /**
         * Create an SFTP connection
         * 
         * @param {string} directory - Directory to connect to
         * @returns {Object} SFTP connection
         */
        createSftpConnection(directory) {
            try {
                return this.sftp.createConnection({
                    username: this.SFTP_CONFIG.username,
                    passwordGuid: this.SFTP_CONFIG.passwordGuid,
                    url: this.SFTP_CONFIG.url,
                    directory: directory,
                    hostKey: this.SFTP_CONFIG.hostKey,
                    port: this.SFTP_CONFIG.port
                });
            } catch (error) {
                this.log.error('Error creating SFTP connection', error);
                throw error;
            }
        }

        /**
         * Format data according to BAI file specifications
         *
         * @param {Array} rows - Array of row objects
         * @param {String} filename - Optional filename for account number extraction
         * @returns {String} - BAI formatted content
         * @throws {Error} If formatting fails
         */
        formatBAIContent(rows, fileName) {
            if (!rows || rows.length === 0) {
                throw new Error('No data to process');
            }

            let baiContent = '';
            
            // Get account number from filename if it starts with "Chase"
            let accountNumber;
            
            if (fileName && fileName.toString().toLowerCase().indexOf('chase') >= 0) {
                // Log the filename for debugging
                this.log.debug('Processing Chase filename', fileName);
                
                // Extract digits after "Chase" with more robust regex to handle spaces and text
                const match = fileName.toString().match(/Chase\s+(?:cc\s+)?(\d+)/i);
                if (match && match[1]) {
                    accountNumber = match[1];
                    this.log.audit('Account Number from Filename', `Extracted account number ${accountNumber} from filename ${fileName}`);
                } else {
                    this.log.error('Failed to extract account number', `Could not extract account number from filename ${fileName}`);
                }
            }
            
            // If account number wasn't found in filename, get it from the data
            if (!accountNumber) {
                this.log.debug('Looking for account number in data', JSON.stringify(rows[0]));
                accountNumber = rows[0].Account || rows[0]['Account #'] || rows[0]['Account Number'];
                
                if (accountNumber) {
                    this.log.audit('Account Number from Data', `Using account number ${accountNumber} from data`);
                } else {
                    this.log.error('Account number not found in data from this file: '+fileName, JSON.stringify(rows[0]));
                }
            }
            
            // Process the account number: remove negative sign and leading x's
            if (accountNumber) {
                // Convert to string if it's not already
                accountNumber = accountNumber.toString();
                
                // Remove negative sign if present
                if (accountNumber.startsWith('-')) {
                    accountNumber = accountNumber.substring(1);
                    this.log.debug('Removed negative sign', `Account number after removing negative: ${accountNumber}`);
                }
                
                // Remove leading x's (case insensitive)
                const originalAccount = accountNumber;
                accountNumber = accountNumber.replace(/^[xX]+/, '');
                
                if (originalAccount !== accountNumber) {
                    this.log.debug('Removed leading Xs', `Account number changed from ${originalAccount} to ${accountNumber}`);
                }
            }
            
            if (!accountNumber) {
                throw new Error(`Account number is required but not found in data or filename: ${fileName}`);
            }
            
            const processedTransactions = this.processTransactions(rows);
            const dates = Object.keys(processedTransactions).sort();
            const earliestDate = dates.length > 0 ? dates[0] : this.formatDateTime(new Date());
            const fileHeaderTime = this.formatDateTime(new Date(), null, 'time');

            // File Header Record (01)
            baiContent += `01,**********,2918458,${earliestDate},${fileHeaderTime},1,80,80,2/\n`;

            let totalTransactionCount = 0;
            let totalAmount = 0;

            // Process each date group
            Object.keys(processedTransactions).forEach(date => {
                const dateGroup = processedTransactions[date];

                // Group Header Record (02)
                baiContent += `02,2918458,*********,1,${date},0000,USD,2/\n`;

                // Account Identifier Record (03)
                baiContent += `03,${accountNumber},USD/\n`;

                // Add status information
                baiContent += `88,918,0,0,/\n`;

                baiContent += dateGroup.transactionContent;

                // Add Account Trailer with transaction amounts
                baiContent += `49,${dateGroup.totalAmount},${dateGroup.transactionCount + 3}/\n`;

                // Add Group Trailer
                baiContent += `98,${dateGroup.totalAmount},1,${dateGroup.transactionCount + 5}/\n`;

                totalTransactionCount += dateGroup.transactionCount;
                totalAmount += dateGroup.totalAmount;
            });
            
            //File Trailer
            baiContent += `99,${totalAmount},${totalTransactionCount},${totalTransactionCount + Object.keys(processedTransactions).length * 5}/\n`;
            
            return baiContent;
        }

        /**
         * Extract account number from CSV data or filename
         * @param {Array} rows - Array of row objects
         * @param {String} filename - Optional filename to extract account from
         * @returns {String} Account number
         */
        extractAccountNumber(rows, filename) {
            if (filename && filename.toLowerCase().includes('chase')) {
                const chaseMatch = filename.match(/Chase\s+(?:cc\s+)?(\d+)/i);
                if (chaseMatch && chaseMatch[1]) {
                    return chaseMatch[1];
                } else {
                    this.log.error("33cb8951-5b45-4c14-b3f1-d578abb976b6 : Chase filename pattern not matched", {
                        filename: filename,
                        expectedPattern: "Chase followed by optional space, optional 'cc', and digits"
                    });
                }
            }
            if (rows.length > 0) {
                const accountNumberFields = [
                    'Account Number', 'Account #', 'Account',
                    'account number', 'account #', 'account',
                    'ACCOUNT NUMBER', 'ACCOUNT #', 'ACCOUNT'
                ];

                const rowsToCheck = [rows[0]];
                if (rows.length > 1) {
                    rowsToCheck.push(rows[1]);
                }

                for (const row of rowsToCheck) {
                    for (const field of accountNumberFields) {
                        if (field in row && row[field]) {
                            let accountNumber = row[field].toString();

                            if (accountNumber.startsWith('x') || accountNumber.startsWith('X')) {
                                accountNumber = accountNumber.substring(1);
                            }
                            accountNumber = accountNumber.replace(/[^0-9]/g, '');
                            if (accountNumber && accountNumber.startsWith('-')) {
                                accountNumber = accountNumber.substring(1);
                            }

                            if (accountNumber) {
                                return accountNumber;
                            }
                        }
                    }
                }
            }
            
            this.log.error("375bb692-c595-4f51-9d46-47811cf8f1b5 : No account number found", {
                filename: filename,
                hasRows: rows && rows.length > 0,
                firstRowKeys: rows && rows.length > 0 ? Object.keys(rows[0]) : []
            });

            return accountNumber;
        }

        /**
         * Parse CSV content to array of row objects
         * @param {String} csvContent - CSV content to parse
         * @returns {Array} Array of row objects
         */
        parseCSV(csvContent) {
            try {
                const normalizedContent = this.normalizeCSVContent(csvContent);
                const lines = normalizedContent.split(/\r?\n/);
                
                if (lines.length < 2) {
                    throw new Error('********-9603-4706-9539-657f9db04ec5 : CSV file must have at least a header row and one data row');
                }
                
                const headers = this.parseIndividualCsvLine(lines[0]).map(h => h.trim()).filter(h => h);
                const rows = [];
                const hasDebitCredit = headers.includes('Debit') && headers.includes('Credit');
                
                for (let i = 1; i < lines.length; i++) {
                    const line = lines[i].trim();
                    if (!line) continue;
                    
                    let values = this.parseIndividualCsvLine(line);
                    
                    if (values.length !== headers.length) {
                        if (values.length < headers.length) {
                            while (values.length < headers.length) {
                                values.push('');
                            }
                        } else {
                            values = values.slice(0, headers.length);
                        }
                    }
                    
                    const rowObj = {};
                    headers.forEach((header, index) => {
                        rowObj[header] = values[index] || '';
                    });
                    
                    if (hasDebitCredit) {
                        const debit = parseFloat(rowObj['Debit'].replace(/[$,]/g, '')) || 0;
                        const credit = parseFloat(rowObj['Credit'].replace(/[$,]/g, '')) || 0;
                        
                        if (debit > 0) {
                            rowObj['Amount'] = `-${debit}`;
                        } else if (credit > 0) {
                            rowObj['Amount'] = `${credit}`;
                        } else {
                            rowObj['Amount'] = '0';
                        }
                    }
                    
                    const hasDate = rowObj['Date'] || rowObj['Post Date'] || rowObj['Posted Date'] || rowObj['Transaction Date'] || rowObj['Posting Date'];
                    const hasAmount = rowObj['Amount'] || hasDebitCredit;
                    
                    if (hasDate && hasAmount) {
                        rows.push(rowObj);
                    } else {
                        this.log.audit(`Skipping row ${i+1} - missing essential data`, {
                            hasDate: !!hasDate,
                            hasAmount: !!hasAmount
                        });
                    }
                }
                
                return rows;
            } catch (error) {
                this.log.error('4066f2cd-915c-493c-9b15-8161085d2f22 : Error parsing CSV', error.message);
                throw error;
            }
        }

        /**
         * Parse a single CSV line, handling quoted values correctly
         * @param {String} line - CSV line to parse
         * @returns {Array} Array of values
         */
        parseIndividualCsvLine(line) {
            const result = [];
            let current = '';
            let inQuotes = false;
            
            for (let i = 0; i < line.length; i++) {
                const char = line[i];
                
                if (char === '"') {
                    inQuotes = !inQuotes;
                } else if (char === ',' && !inQuotes) {
                    result.push(current);
                    current = '';
                } else {
                    current += char;
                }
            }
            
            result.push(current);
            
            return result;
        }

        /**
         * Creates an SFTP connection with the specified directory
         * @param {string} directory - The directory to connect to
         * @returns {Object} SFTP connection
         * @throws {Error} If connection fails
         */
        createSftpConnection(directory) {
            try {
                const connectionParams = Object.assign({}, this.SFTP_CONFIG, { directory: directory });
                return this.sftp.createConnection(connectionParams);
            } catch (error) {
                this.log.error(`1ffbca38-90c3-45a3-80a9-ceb0b50afa31 : SFTP Connection Error for directory ${directory}`, {
                    message: error.message,
                    stack: error.stack
                });
                throw new Error(`9ba86a28-7e13-4937-b8d3-2d2ef729f03f : Failed to create SFTP connection to ${directory}: ${error.message}`);
            }
        }

        /**
         * Format date and time for BAI file
         * @param {Date|string} date - Date to format or row object containing date information
         * @param {Object} [row] - Optional row object containing transaction date
         * @param {String} [format] - 'date' for YYMMDD, 'time' for HHMM, 'both' for object with both
         * @returns {String|Object} Formatted date/time
         */
        formatDateTime(date, row, format = 'date') {
            let targetDate = date;

            // Extract date from row if provided
            if (row) {
                const postDate = row['Post Date'] || row['Posted Date'] || row['Transaction Date'] || row['Date'] ||
                                row['Posting Date'] || row['posting date'] || row['POSTING DATE'] ||
                                row['post date'] || row['posted date'] || row['transaction date'] || row['date'] ||
                                row['POST DATE'] || row['POSTED DATE'] || row['TRANSACTION DATE'] || row['DATE'];

                if (postDate) {
                    try {
                        if (typeof postDate === 'string' && postDate.match(/^\d{2}\/\d{2}\/\d{4}$/)) {
                            const [month, day, year] = postDate.split('/');
                            targetDate = new Date(year, parseInt(month) - 1, day);
                        } else {
                            targetDate = new Date(postDate);
                        }

                        if (isNaN(targetDate.getTime())) {
                            targetDate = date;
                        }
                    } catch (e) {
                        this.log.error('08feaf79-c356-45a8-9d33-f776b221d6a7 : Failed to parse transaction date', {
                            postDate: postDate,
                            error: e.message
                        });
                        targetDate = date;
                    }
                }
            }

            // Ensure we have a valid date
            if (typeof targetDate === 'string') {
                try {
                    targetDate = new Date(targetDate);
                } catch (e) {
                    targetDate = new Date();
                }
            }

            if (!(targetDate instanceof Date) || isNaN(targetDate.getTime())) {
                targetDate = new Date();
            }

            // Format based on requested format
            const year = targetDate.getFullYear().toString().slice(-2);
            const month = (targetDate.getMonth() + 1).toString().padStart(2, '0');
            const day = targetDate.getDate().toString().padStart(2, '0');
            const hours = targetDate.getHours().toString().padStart(2, '0');
            const minutes = targetDate.getMinutes().toString().padStart(2, '0');

            if (format === 'time') {
                return `${hours}${minutes}`;
            } else if (format === 'both') {
                return {
                    date: `${year}${month}${day}`,
                    time: `${hours}${minutes}`
                };
            } else {
                return `${year}${month}${day}`;
            }
        }

        /**
         * Process transactions: group by date and generate BAI transaction content
         * @param {Array} rows - Array of transaction rows
         * @returns {Object} Object with grouped transactions and their BAI content by date
         */
        processTransactions(rows) {
            const transactionsByDate = {};

            // First pass: Group transactions by date
            rows.forEach((row, index) => {
                let transactionDate = null;

                const dateFields = [
                    'Transaction Date', 'transaction date', 'TRANSACTION DATE',
                    'Post Date', 'post date', 'POST DATE',
                    'Posted Date', 'posted date', 'POSTED DATE',
                    'Posting Date', 'posting date', 'POSTING DATE',
                    'Date', 'date', 'DATE'
                ];

                let postDateValue = null;

                for (const field of dateFields) {
                    if (row[field]) {
                        postDateValue = row[field];
                        break;
                    }
                }

                if (postDateValue) {
                    try {
                        let parsedDate;
                        if (typeof postDateValue === 'string' && postDateValue.match(/^\d{2}\/\d{2}\/\d{4}$/)) {
                            const [month, day, year] = postDateValue.split('/');
                            parsedDate = new Date(year, parseInt(month) - 1, day);
                        } else {
                            parsedDate = new Date(postDateValue);
                        }

                        if (!isNaN(parsedDate.getTime())) {
                            transactionDate = this.formatDateTime(parsedDate);

                        } else {
                            this.log.error(`71eda43e-92a1-4371-8254-556ac033468f : Invalid date value in row ${index+1}`, {
                                dateValue: postDateValue
                            });
                        }
                    } catch (e) {
                        this.log.error('10d1f1b6-14fb-4623-a224-40f4c9877c69 : Failed to parse transaction date', {
                            postDate: postDateValue,
                            error: e.message,
                            row: index + 1
                        });
                    }
                }

                if (!transactionDate) {
                    this.log.audit('No valid transaction date found in row, using default date', {
                        rowData: JSON.stringify(row),
                        row: index + 1
                    });
                    const defaultDate = new Date();
                    transactionDate = this.formatDateTime(defaultDate);
                }

                if (!transactionsByDate[transactionDate]) {
                    transactionsByDate[transactionDate] = {
                        rows: [],
                        totalAmount: 0,
                        transactionCount: 0,
                        transactionContent: ''
                    };
                }

                transactionsByDate[transactionDate].rows.push(row);
            });

            // Second pass: Process each date group and generate BAI content
            Object.keys(transactionsByDate).forEach(date => {
                const dateGroup = transactionsByDate[date];
                const dateRows = dateGroup.rows;
                let totalAmount = 0;
                let transactionCount = 0;
                let transactionContent = '';
                
                dateRows.forEach(row => {
                    let amount = 0;
                    let description = '';
                    let transCode = '940'; // Default transaction code
                    let isDebit = false;
                    
                    const rowDescription = row.Description || row.description || row['Extended Details'] ||
                                         row['Bank Text'] || row['bank text'] || row['BANK TEXT'] || '';
                    
                    if (rowDescription.includes('TAX')) {
                        transCode = '455';
                    } else if (rowDescription.includes('LLC')) {
                        transCode = '495';
                    } else if ((row.Reference || row.Receipt || row.Check) && 
                              (row.Reference?.length === 11 || row.Receipt?.length === 11 || row.Check?.length === 11)) {
                        transCode = '475';
                    }
                    
                    let amountStr = row.Amount || row.amount || row.AMOUNT;
                    
                    if (!amountStr && (row.Debit || row.Credit)) {
                        amountStr = row.Amount; // This was set in parseCSV
                    }
                    
                    isDebit = (amountStr && (
                        amountStr.startsWith('-') || 
                        (amountStr.startsWith('(') && amountStr.endsWith(')'))
                    )) || (row.Debit && parseFloat(row.Debit.replace(/[$,]/g, '')) > 0);
                    
                    amountStr = (amountStr || '0').replace(/[$€£¥]/g, '').replace(/,/g, '');
                    
                    if (amountStr.startsWith('(') && amountStr.endsWith(')')) {
                        amountStr = amountStr.substring(1, amountStr.length - 1);
                    } else if (amountStr.startsWith('-')) {
                        amountStr = amountStr.substring(1);
                    }
                    
                    amount = Math.abs(parseFloat(amountStr));
                    
                    if (isNaN(amount) || amount === 0) {
                        return; // Skip rows with invalid or zero amount
                    }
                    
                    const formattedAmount = Math.round(amount * 100).toString();
                    
                    const reference = row.Reference || row.Receipt || row.Check || row['Check or Slip #'] || '000000000000000';
                    
                    const statementDesc = row['Appears On Your Statement As'] || '';
                    
                    const combinedDesc = statementDesc ? 
                        `${rowDescription} - ${statementDesc}` : rowDescription;
                    
                    const finalTransCode = isDebit ? this.BAI_TRANS_CODES.DEBIT : this.BAI_TRANS_CODES.CREDIT;
                    
                    transactionContent += `16,${finalTransCode},${formattedAmount},0,${reference},,${combinedDesc}/\n`;
                    
                    if (row['Card Member'] || row['Category'] || row['Status']) {
                        const additionalInfo = [
                            row['Card Member'] ? `Card Member: ${row['Card Member']}` : '',
                            row['Category'] ? `Category: ${row['Category']}` : '',
                            row['Status'] ? `Status: ${row['Status']}` : ''
                        ].filter(Boolean).join(' - ');
                        
                        if (additionalInfo) {
                            transactionContent += `88,${additionalInfo}/\n`;
                        }
                    }
                    
                    totalAmount += parseInt(formattedAmount, 10);
                    transactionCount++;
                });
                
                if (transactionCount === 0) {
                    this.log.error("21ff4eb9-1e05-41a6-9a49-83f67b2f4d25 : No transactions found in CSV data for date", {
                        date: date,
                        rowCount: dateRows.length
                    });
                }

                // Store the processed data for this date
                dateGroup.totalAmount = totalAmount;
                dateGroup.transactionCount = transactionCount;
                dateGroup.transactionContent = transactionContent;
            });

            return transactionsByDate;
        }

        /**
         * Normalize CSV content by handling multiline fields within quotes
         * @param {String} csvContent - Raw CSV content
         * @returns {String} Normalized CSV content
         */
        normalizeCSVContent(csvContent) {
            let result = '';
            let inQuotes = false;
            
            for (let i = 0; i < csvContent.length; i++) {
                const char = csvContent[i];
                
                if (char === '"') {
                    inQuotes = !inQuotes;
                    result += char;
                } else if ((char === '\n' || char === '\r') && inQuotes) {
                    // Replace line breaks within quotes with space
                    result += ' ';
                } else {
                    result += char;
                }
            }
            
            return result;
        }
    }

    return BaiConverter;
})
