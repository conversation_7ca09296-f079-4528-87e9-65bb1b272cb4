/**
 * @description Import RIP file and trigger the MRs that will create
 * tier levels, tier groups, agreement records and agreement details
 *
 * @NApiVersion 2.1
 * @NScriptType Suitelet
 * @NAmdConfig /SuiteScripts/config.json
 *
 * @param {import ("N/types")}
 * <AUTHOR>
 * <AUTHOR>
 * @module brdg_rip_upload_sl
 */

define([
  "require",
  "N/ui/serverWidget",
  "N/redirect",
  "N/runtime",
  "N/task",
  "../../../../Classes/vlmd_custom_error_object",
], function (require) {
  const serverWidget = require("N/ui/serverWidget");
  const redirect = require("N/redirect");
  const runtime = require("N/runtime");
  const task = require("N/task");

  return {
    /**
     * Displays the general instructions on how to import RIP records from a file
     * Accepts a file and vendor as input in GET
     * Uploads the file to RIP Imports folder and submits the RIP Import Records MR in POST
     *
     * @param {import("N/types").EntryPoints.Suitelet.onRequestContext} context Suitelet context
     */
    onRequest: function (context) {
      /** @type {import("../../../../Classes/vlmd_custom_error_object").CustomErrorObject} */

      const CustomErrorObject = require("../../../../Classes/vlmd_custom_error_object");
      const customErrorObject = new CustomErrorObject();

      try {
        const request = context.request;
        const response = context.response;
        const folderId = "4525344"; // RIP Imports folder

        if (context.request.method === "GET") {
          const form = serverWidget.createForm({
            title: "Import RIPs",
          });

          const instructions = form.addField({
            id: "custpage_instructions",
            type: serverWidget.FieldType.INLINEHTML,
            label: "Instructions",
          });
          instructions.updateLayoutType({
            layoutType: serverWidget.FieldLayoutType.OUTSIDEABOVE,
          });
          instructions.defaultValue = `
            <div class="instructions">
              <h2>Prepare the File for Upload</h2>
              <h3>Allied Beverage Group</h2>
                <ol>
                  <li>1. Convert the RIPs workbook sheet to a <b>CSV</b> file.</li>
                  <li>2. Edit the header so that there is only <b>1 header row</b>.</li>
                  <li>3. Remove any <b>forward slashes</b> from the headers</li>
                  <li>4. Remove all <b>lines before the header</b>. This results to line 1 containing the header, and line 2 marking the start of the data rows.</li>
                  <li>5. Search for all <b>commas</b> in the RIP sheet and replace them with whitespaces.</li>
                </ol>
              <h3>Federal Wine and Spirits</h2>
                <ol>
                  <li>1. Convert the RIPs workbook sheet to a <b>CSV</b> file.</li>
                  <li>2. Edit the header so that there is only <b>1 header row</b>.</li>
                  <li>2a. Delete the new line character from <b>E1</b> so the header cell <b>RIP CODE</b> stays on a single line.</li>
                  <li>3. The final header should be <b>RIP Name,FROMDATE,TODATE,Product SKU,RIP CODE,Item Name,SIZE,PACK,CountsAs,Month,RIP Level,ALL RIPS CUST,FAMILY</b>.</li>
                </ol>
              <h3>Royal Wine Corporation</h2>
                <ol>
                  <li>1. Open the XLSX file.</li>
                  <li>2. Go to the <b>RIP</b> tab and download or export it to a CSV.</li>
                  <li>3. Open the downloaded CSV.</li>
                  <li>4. Remove all the lines above the header row.</li>
                  <li>5. Delete Column A as it is a duplicate of Column B. Both represent <b>RIP CODE</b>.</li>
                  <li>6. Edit the header so that there is only <b>1 header row</b>. </li>
                  <li>6a. Delete the new line character from <b>E1</b> so the header cell <b>RIP DESCRIPTION</b> stays on a single line.</li>
                  <li>6b. Replace cell <b>F1</b> with <b>SKU</b> to shorten the column header.</li>
                  <li>6c. Delete the new line characters from <b>S1</b> so the header cell <b>COMMENTS INSTRUCTIONS EXPLANATIONS</b> stays on a single line.</li>
                  <li>7. The final header should be <b>RIP CODE,FROM DATE (MM/DD/YYYY),TO DATE (MM/DD/YYYY),RIP DESCRIPTION,SKU,R UNIT 1,R QTY 1,$ R AMT 1,R UNIT 2,R QTY 2,$ R AMT 2,R UNIT 3,R QTY 3,$ R AMT 3,R UNIT 4,R QTY 4,$ R AMT 4,COMMENTS INSTRUCTIONS EXPLANATIONS</b>.</li>
                </ol>
              <br/>
                <p> After you click the "Import File" button, you will be redirected to <b><a href='https://5802576.app.netsuite.com/app/common/search/searchresults.nl?searchid=4628'>RIP Import Tasks<a/></b> saved search where you can track the import progress.<br/>
                When the scipt is finished processing an email with the results will be sent to the RIP import owner.
                </p>
            </div>
            <style type = "text/css"> 
              .instructions {
                border: 1px solid #417ed9;
                background: #cfeefc;
                padding: 0 10px 0 10px
              }
              .instructions ol {
                padding-left: unset;
                list-style: none !important;
              }
              .instructions li {
                font-size: 12px;
              }
            </style>  
          `;

          const vendorField = form.addField({
            id: "custpage_vendor",
            type: serverWidget.FieldType.SELECT,
            source: "vendor",
            label: "Vendor",
          });

          vendorField.defaultValue = 13694; //Allied Beverage

          vendorField.updateLayoutType({
            layoutType: serverWidget.FieldLayoutType.STARTROW,
          });

          const fileField = form.addField({
            id: "file",
            type: serverWidget.FieldType.FILE,
            label: "Rip Information Sheet",
          });
          fileField.updateLayoutType({
            layoutType: serverWidget.FieldLayoutType.OUTSIDEBELOW,
          });

          form.addSubmitButton({
            label: "Import File",
          });

          response.writePage(form);
        } else {
          const vendorId = context.request.parameters.custpage_vendor;

          if (!vendorId) {
            throw customErrorObject.updateError({
              errorType: customErrorObject.ErrorTypes.MISSING_USER_INPUT,
              summary: "MISSING_VENDOR_ID",
              details: `No vendor was selcted from the input form`,
            });
          }

          const fileToSave = request.files.file;

          if (!fileToSave) {
            throw customErrorObject.updateError({
              errorType: customErrorObject.ErrorTypes.MISSING_USER_INPUT,
              summary: "MISSING_FILE",
              details: `No file was selected for upload.`,
            });
          }

          fileToSave.name = new Date().toISOString() + "_" + fileToSave.name;
          fileToSave.folder = folderId;
          const fileId = fileToSave.save();

          const importRecordsMr = task.create({
            taskType: task.TaskType.MAP_REDUCE,
            scriptId: "customscript_brdg_rip_import_records_mr",
            deploymentId: "customdeploy_brdg_rip_import_records_mr",
            params: {
              custscript_brdg_rip_import_file: fileId,
              custscript_brdg_rip_import_owner: runtime.getCurrentUser().id,
              custscript_brdg_rip_vendor: vendorId,
            },
          });

          importRecordsMr.submit();

          redirect.toSavedSearchResult({
            id: "customsearch_brdg_rip_import_tasks",
          });
        }
      } catch (err) {
        customErrorObject.throwError({
          summaryText: `ERROR_UPLOADING_RIPS`,
          error: err,
        });
      }
    },
  };
});
