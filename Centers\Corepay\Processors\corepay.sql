WITH cteTransactions AS 
(
   SELECT
      s.id AS SubsidiaryID,
      s.Name AS Subsidiary_Name,
      CASE
         WHEN
            p.recordtype = 'vendorcredit' 
         THEN
            NULL 
         ELSE
            a.acctnumber 
      END
      AS Bank_Identifier , v.externalid AS Vendor_ID , v.companyname AS Vendor_Name , c.name AS Vendor_Customer_Account , e.nkey AS RemitToID , e.addr1 , e.addr2 , e.addr3 , e.city , e.state , e.zip , e.country , 
      CASE
         WHEN
            p.recordtype = 'vendorcredit' 
         THEN
            p.tranid 
         ELSE
            b.tranid 
      END
      AS ReferenceNumber , b.transactionnumber AS Bill_Transaction_ID , p.transactionnumber AS Payment_Transaction_ID ,p.id AS Payment_Internal_ID,  p.type as Payment_Or_Credit , n.foreignamount , 
      CASE
         WHEN
            p.recordtype = 'vendorpayment' 
         THEN
            n.foreignamount 
         ELSE
            0 
      END
      AS Payment_Amount , 
      CASE
         WHEN
            p.recordtype = 'vendorcredit' 
         THEN
            - n.foreignamount 
         ELSE
            0 
      END
      AS Credit_Amount , 
      CASE
         WHEN
            p.memo = '0' 
         THEN
            NULL 
         ELSE
            p.memo 
      END
      AS Memo , b.terms , p.recordtype , b.id AS BillID , p.id AS PaymentID 
   FROM
      Transaction b 
      LEFT JOIN
         Customrecord_RSM_Vendor_Account_Number c 
         ON b.custbody_vendor_account_number = c.id 
      LEFT JOIN
         NextTransactionLineLink n 
         ON b.id = n.previousdoc 
      LEFT JOIN
         Transaction p 
         ON n.nextdoc = p.id 
         AND p.recordtype IN 
         (
            'vendorpayment', 'vendorcredit'
         )
      LEFT JOIN
         TransactionAddressMapping m 
         ON p.id = m.Transaction 
      LEFT JOIN
         VendorAddressBookEntityAddress e 
         ON m.address = e.nkey 
      INNER JOIN
         TransactionLine l 
         ON p.id = l.Transaction 
         AND l.linesequencenumber = 0 
      LEFT JOIN
         Account a 
         ON l.expenseaccount = a.id 
      INNER JOIN
         Subsidiary s 
         ON l.subsidiary = s.id 
         AND s.Isinactive = 'F' 
      INNER JOIN
         Vendor v 
         ON p.entity = v.ID 
   WHERE
      b.recordtype = 'vendorbill' 
      AND p.voided = 'F' 		
      AND p.custbody_vlmd_corepay_status = '1'     
)
, cteMinPayment as 
(
   SELECT
      Bill_Transaction_ID,
      MIN(PaymentID) AS MinID,
      recordtype 
   FROM
      cteTransactions 
   WHERE
      recordtype in
      (
         'vendorpayment',
         'vendorcredit'
      )
   GROUP BY
      Bill_Transaction_ID,
      recordtype 
)
,
cteAcctNumber as 
(
   SELECT
      Bank_Identifier,
      Bill_Transaction_ID,
      Payment_Transaction_ID,
      Payment_Internal_ID,
      Payment_Or_Credit ,
      recordtype,
      PaymentID 
   FROM
      cteTransactions 
   WHERE
      recordtype = 'vendorpayment' 
)
,
cteCredits as 
(
   SELECT
      Bill_Transaction_ID,
      SUM(
      CASE
         WHEN
            recordtype <> 'vendorcredit' 
         THEN
            0 
         ELSE
            foreignamount 
      END
) AS Credit_Sum , MAX(
      CASE
         WHEN
            recordtype = 'vendorcredit' 
         THEN
            0 
         ELSE
            PaymentID 
      END
) AS MaxPayment 
   FROM
      cteTransactions 
   GROUP BY
      Bill_Transaction_ID 
)
SELECT
   t.SubsidiaryID,
   t.Subsidiary_Name,
   COALESCE(t.Bank_Identifier, aa.Bank_Identifier) AS Bank_Identifier,
   t.Vendor_ID,
   t.Vendor_Name,
   t.Vendor_Customer_Account,
   t.RemitToID,
   t.Addr1,
   t.Addr2,
   t.Addr3,
   t.City,
   t.State,
   t.Zip,
   t.Country,
   t.Bill_Transaction_ID || '-' || t.Payment_Transaction_ID AS InvoiceID,
   t.ReferenceNumber,
   t.Bill_Transaction_ID,
   t.Payment_Transaction_ID,
   t.Payment_Internal_ID,
   t.Payment_Or_Credit ,
   t.Payment_Amount + COALESCE(c.Credit_Sum, 0) + t.Credit_Amount AS Orig_Amount,
   t.Payment_Amount + COALESCE(c.Credit_Sum, 0) + t.Credit_Amount AS Amount_Paid,
   m.Name AS Terms,
   NULL AS Discount,
   t.Memo as Payment_Memo 
FROM
   cteTransactions t 
   LEFT JOIN
      cteMinPayment a 
      ON t.Bill_Transaction_ID = a.Bill_Transaction_ID 
      AND a.recordtype = 'vendorpayment' 
   LEFT JOIN
      cteAcctNumber aa 
      ON aa.PaymentID = a.MinID 
      AND t.recordtype = 'vendorcredit' 
   LEFT JOIN
      cteCredits c 
      ON t.Bill_Transaction_ID = c.Bill_Transaction_ID 
      AND t.recordtype = 'vendorpayment' 
      AND c.MaxPayment = t.PaymentID 
   LEFT JOIN
      Term m 
      ON t.terms = m.id 
ORDER BY
   t.SubsidiaryID,
   t.Vendor_ID,
   t.Bill_Transaction_ID,
   t.Payment_Transaction_ID