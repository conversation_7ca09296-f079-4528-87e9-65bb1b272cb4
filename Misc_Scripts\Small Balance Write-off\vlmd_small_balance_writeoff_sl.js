/**
 * @description Displays records for write-offs.
 * @NApiVersion 2.1
 * @NScriptType Suitelet
 * <AUTHOR>
 * @module
 */

define(["N/search", "N/ui/serverWidget"], function (search, serverWidget) {
	let onRequest = (scriptContext) => {
		//Create Small Balance Write-off form
		var form = serverWidget.createForm({
			title: "Small Balance Write-off List",
		});

		form.addButton({
			id: 'custpage_btn_applyfilter',
			label: 'Apply Filters ',
			functionName: '' // Add the function triggered on button click
		});

		form.addButton({
			id: 'custpage_btn_resetfilter',
			label: 'Reset Filters ',
			functionName: '' // Add the function triggered on button click
		});

		form.addButton({
			id: 'custpage_btn_submit',
			label: 'Submit ',
			functionName: '' // Add the function triggered on button click
		});

		var transactionSublist = form.addSublist({
			id: "transaction",
			type: serverWidget.SublistType.INLINEEDITOR,
			label: "Inline Editor Sublist",
		});

		transactionSublist.addField({
			id: "custpage_mark",
			type: serverWidget.FieldType.CHECKBOX,
			label: "Date",
		});

		transactionSublist.addField({
			id: "custpage_internalid",
			type: serverWidget.FieldType.TEXT,
			label: "ID",
		});

		transactionSublist.addField({
			id: "custpage_date",
			type: serverWidget.FieldType.DATE,
			label: "Date",
		});

		transactionSublist.addField({
			id: "custpage_customer",
			type: serverWidget.FieldType.TEXT,
			label: "Customer",
		});

		transactionSublist.addField({
			id: "custpage_account",
			type: serverWidget.FieldType.TEXT,
			label: "Account",
		});

		var transactionSearchObj = search.load({
			type: search.Type.TRANSACTION,
			id: 7887
		});
		transactionSearchObj.run().each(function (result) {
			// .run().each has a limit of 4,000 results

			return true;
		});

		scriptContext.response.writePage(form);
	};

	return { onRequest };
});
