/**
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * @NAmdConfig /SuiteScripts/config.json
 */

define([
  "require",
  "GetEdiIntegrationsLib",
  "Get810InternalIdsLib",
  "GetEdiPartnerValuesLib",
  "N/log",
  "N/runtime",
  "N/format",
  "N/cache",
  "../../Libraries/Process_EDI_File/spl_process_outgoing_810_lib",
  "../../Libraries/Process_EDI_File_End/spl_edi_processing_record_lib",
  "../../../Classes/vlmd_custom_error_object",
  "../../../Classes/vlmd_mr_summary_handling",
], function (
  require,
  getEdiIntegrationsLib,
  getInvoiceInternalIdsLib,
  getEdiPartnerValuesLib
) {
  const log = require("N/log");
  const runtime = require("N/runtime");
  const format = require("N/format");
  const cache = require("N/cache");

  const process810Lib = require("../../Libraries/Process_EDI_File/spl_process_outgoing_810_lib");
  const ediProcessingRecordLib = require("../../Libraries/Process_EDI_File_End/spl_edi_processing_record_lib");

  /** @type {import("../../../Classes/vlmd_custom_error_object").CustomErrorObject} */
  const CustomErrorObject = require("../../../Classes/vlmd_custom_error_object");
  const customErrorObject = new CustomErrorObject();

  var noTransactionsToProcess = [];

  var dataObj = {
    prodGuidBool: true,
    prodDirectoryBool: true,
    decodeContent: true,
    prodGUID: "e527a8c4c6c5458683acca4d595a6087",
    sandboxGUID: "",
    prodDirectory: `/users/Agora/OUT/810`,
    testDirectory: "/users/Agora/Test",
    transactionType: "Invoice/Credit Memo",
    purchasingSoftware: "Agora",
    documentTypeId: 6,
    purchasingSoftwareId: 6,
  };

  function getInputData(context) {
    const transactionObjArr = [];

    try {
      var ediTransactionProcessingResultsObj =
        ediProcessingRecordLib.getInitialDataObj(
          {
            purchasingSoftware: dataObj.purchasingSoftware,
            transactionType: dataObj.transactionType,
            purchasingSoftwareId: dataObj.purchasingSoftwareId,
            documentTypeId: dataObj.documentTypeId,
          },
          customErrorObject
        );

      let ediTransactionRecordId = ediProcessingRecordLib.createRecord(
        ediTransactionProcessingResultsObj,
        customErrorObject
      );

      let ediTransactionRecordIdCache = cache.getCache({
        name: "ediTransactionRecordId",
        scope: cache.Scope.PRIVATE,
      });

      ediTransactionRecordIdCache.put({
        key: "ediTransactionRecordId",
        value: ediTransactionRecordId,
      });

      const currentScript = runtime.getCurrentScript();

      const savedSearchId = currentScript.getParameter({
        name: "custscript_saved_search",
      });

      let startDate = currentScript.getParameter({
        name: "custscript_agora_start_date",
      });

      startDate = startDate
        ? format.format({
            value: startDate,
            type: format.Type.DATE,
          })
        : null;

      let endDate = currentScript.getParameter({
        name: "custscript_agora_end_date",
      });

      endDate = endDate
        ? format.format({
            value: endDate,
            type: format.Type.DATE,
          })
        : null;

      let integrationCustomerInternalId = currentScript.getParameter({
        name: "custscript_agora_integration_company",
      });

      var parentCustomerFoldersArr =
        getEdiIntegrationsLib.getInfoForOutgoingDocument(
          dataObj.purchasingSoftwareId,
          dataObj.documentTypeId,
          integrationCustomerInternalId
        );

      if (!parentCustomerFoldersArr || parentCustomerFoldersArr.length <= 0) {
        throw customErrorObject.updateError({
          errorType: customErrorObject.ErrorTypes.NO_VALUE_RETURNED,
          summary: "PARENT_CUSTOMER_FOLDERS_NOT_RETURNED",
          details: `Error getting array of parent customer folders`,
        });
      }

      //Iterate over each parent and get all internal ids of transactions to submit based on the given params
      parentCustomerFoldersArr.forEach((parentCustomer) => {
        const remainingUsage = runtime.getCurrentScript().getRemainingUsage();

        /*Not enough usage to get the information for the next folder
					Stop iteration here and return the array with whatever was gotten so far*/
        if (remainingUsage < 10) {
          customErrorObject.updateError({
            errorType: customErrorObject.ErrorTypes.NO_VALUE_RETURNED,
            summary: "SCRIPT_USAGE_MAX_REACHED",
            details: `Execution stopped at getting the files for ${folder.integrationName}.`,
          });

          return;
        }

        const partnerValues = getEdiPartnerValuesLib.getAgoraValues(
          parentCustomer.integrationFolder
        );

        if (!partnerValues) {
          customErrorObject.updateError({
            errorType: customErrorObject.ErrorTypes.NO_VALUE_RETURNED,
            summary: "NO_PARTNER_VALUES_RETURNED",
            details: `Error getting EDI partner values for ${parentCustomer?.integrationFolder}.`,
          });

          return;
        }

        var transactionsArr = getInvoiceInternalIdsLib.getInternalIds(
          parentCustomer.customerId,
          savedSearchId,
          startDate,
          endDate,
          "", //Blank Value Passed for documentNumbers
          customErrorObject
        );

        if (transactionsArr.length <= 0) {
          noTransactionsToProcess.push(parentCustomer.customerName);
        } else {
          transactionsArr.forEach((internalId) => {
            transactionObjArr.push({
              transactionObj: internalId,
              parentCustomer: parentCustomer,
              partnerValues: partnerValues,
              dataObj: dataObj,
              ediTransactionRecordId,
            });

            return true;
          });
        }
      });
    } catch (err) {
      customErrorObject.throwError({
        summaryText: "GET_INPUT_DATA_ERROR",
        error: err,
      });
    }

    if (customErrorObject.ERROR_TYPE) {
      customErrorObject.throwError({
        summaryText: "SOFT_ERROR_GET_INPUT_DATA_ERROR",
      });
    }

    log.audit(
      "Transactions to Process",
      transactionObjArr.map((obj) => obj.transactionObj.transactionId).join(", ")
    );

    return transactionObjArr;
  }

  function map(context) {
    const customErrorObject = new CustomErrorObject();

    var {
      transactionObj,
      parentCustomer,
      partnerValues,
      dataObj,
      ediTransactionRecordId,
    } = JSON.parse(context.value);

    try {
      const documentControlNumber = process810Lib.process810(
        transactionObj,
        parentCustomer,
        partnerValues,
        dataObj,
        customErrorObject,
        ediTransactionRecordId
      );

      context.write({
        key: parentCustomer.customerName,
        value: {
          documentControlNumber,
          ediTransactionRecordId,
        },
      });
    } catch (err) {
      customErrorObject.throwError({
        summaryText: `MAP_ERROR`,
        error: err,
        recordId: transactionObj?.transactionId,
        recordType: transactionObj?.transactionType,
        errorWillBeGrouped: true,
      });
    }
  }

  function reduce(context) {
    context.write({
      key: context.key,
      value: context.values.map((val) => JSON.parse(val)),
    });
  }

  function summarize(context) {
    try {
      let ediTransactionRecordIdCache = cache.getCache({
        name: "ediTransactionRecordId",
        scope: cache.Scope.PRIVATE,
      });

      let ediTransactionRecordId = ediTransactionRecordIdCache.get({
        key: "ediTransactionRecordId",
      });

      ediTransactionRecordIdCache.remove({
        key: "ediTransactionRecordId",
      });

      const StageHandling = require("../../../Classes/vlmd_mr_summary_handling");
      const stageHandling = new StageHandling(context);

      stageHandling.printScriptProcessingSummary();

      const { resultsLog, recordsProcessedMessage } =
        stageHandling.printRecordsProcessed();

      const { errorsMessage } = stageHandling.printErrors({
        groupErrors: true,
      });

      var ediTransactionProcessingResultsObj =
        ediProcessingRecordLib.getSummaryDataObj(
          {
            recordsProcessedMessage,
            errorsMessage,
            noTransactionsToProcess,
            purchasingSoftware: dataObj.purchasingSoftware,
            purchasingSoftwareId: dataObj.purchasingSoftwareId,
            transactionType: dataObj.transactionType,
            documentTypeId: dataObj.documentTypeId,
          },
          customErrorObject
        );

      ediProcessingRecordLib.updateRecord(
        { ...ediTransactionProcessingResultsObj, ediTransactionRecordId },
        customErrorObject
      );
    } catch (err) {
      customErrorObject.throwError({
        summaryText: `SUMMARIZE_ERROR`,
        error: err,
      });
    }
  }

  return {
    getInputData,
    map,
    reduce,
    summarize,
  };
});
