/**
 * @description Constants for text matching operations. Contains filler words, unit replacements, and other reference data
 *
 * @NApiVersion 2.1
 * <AUTHOR>
 * 
 * @module spl_bid_sheet_text_matching_constants
 */

define(['exports'], (exports) => {
    /**
     * Common filler words to be removed during normalization
     */
    exports.FILLER_WORDS = new Set([
        'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of',
        'with', 'by', 'as', 'from', 'that', 'this', 'these', 'those', 'w/', 'f/', 'w', 'f'
    ]);

    /**
     * Replacements for abbreviations, shortcuts, and other variations
     */
    exports.UNIT_REPLACEMENTS = {
        // Common abbreviations
        "w/": "with",
        "f/": "for",

        // Dimension labels
        "l": "length",
        "w": "width",
        "h": "height",
        "d": "depth",

        // Length/Distance units
        "in": "inch",
        "in.": "inch",
        "\"": "inch",
        "''": "inch",
        "ft": "foot",
        "ft.": "foot",
        "'": "foot",
        "yd": "yard",
        "yd.": "yard",
        "mm": "millimeter",
        "cm": "centimeter",
        "m": "meter",
        "km": "kilometer",
        "mi": "mile",

        // Area and Volume descriptors
        "sq": "square",
        "sq.": "square",
        "cu": "cubic",
        "cu.": "cubic",

        // Geometric measurements
        "dia": "diameter",
        "dia.": "diameter",
        "ø": "diameter",
        "rad": "radius",
        "rad.": "radius",
        "r": "radius",
        "r.": "radius",

        // Quantity units
        "pc": "piece",
        "pcs": "piece",
        "ea": "each",
        "pk": "package",
        "pk.": "package",
        "pkg": "package",
        "bx": "box",
        "ctn": "carton",
        "cs": "case",
        "dz": "dozen",
        "pr": "pair",
        "st": "set",
        "bdl": "bundle",
        "rl": "roll",
        "un": "unit",

        // Volume units - Liquid
        "fl oz": "fluid ounce",
        "fl. oz.": "fluid ounce",
        "fl.oz.": "fluid ounce",
        "floz": "fluid ounce",
        "ml": "milliliter",
        "gal": "gallon",
        "qt": "quart",
        "pt": "pint",

        // Volume units - Cubic
        "cu in": "cubic inch",
        "cu ft": "cubic foot",
        "cu yd": "cubic yard",
        "cc": "cubic centimeter",
        "cu m": "cubic meter",

        // Weight units
        "oz": "ounce",
        "oz.": "ounce",
        "lb": "pound",
        "lb.": "pound",
        "lbs": "pound",
        "g": "gram",
        "mg": "milligram",
        "kg": "kilogram",
        "t": "ton",
        "mt": "metric ton"
    };

    exports.UNITS = new Set([
        // Dimensions
        "length", "L", "l",
        "width", "W", "w",
        "height", "H", "h",
        "depth", "D", "d",

        // Distance / Linear units
        "inch", "in",
        "foot", "ft",
        "yard", "yd",
        "millimeter", "mm",
        "centimeter", "cm",
        "meter", "m",
        "kilometer", "km",
        "mile", "mi",

        // Area / Volume descriptors
        "square", "sq",
        "cubic", "cu",

        // Circular dimensions
        "diameter", "dia",
        "radius", "rad",

        // Count / Packaging
        "piece", "pc",
        "each", "ea",
        "package", "pkg",
        "box", "bx",
        "carton", "ctn",
        "case",
        "dozen", "dz",
        "pair", "pr",
        "set",
        "bundle", "bdl",
        "roll", "rl",
        "unit", "u",

        // Liquid volume
        "fluid ounce", "fl oz",
        "milliliter", "ml",
        "liter", "l",
        "gallon", "gal",
        "quart", "qt",
        "pint", "pt",

        // Volume (solid)
        "cubic inch", "cu in",
        "cubic foot", "cu ft",
        "cubic yard", "cu yd",
        "cubic centimeter", "cc",
        "cubic meter", "cu m",

        // Weight / Mass
        "ounce", "oz",
        "pound", "lb", "lbs",
        "gram", "g",
        "milligram", "mg",
        "kilogram", "kg",
        "ton", "t",
        "metric ton", "mt"
    ]);


    /**
     * Packaging information patterns to standardize unit conversion ratios
     */
    exports.PACKAGING_PATTERNS = [
        // 100ea/cs - 100 each per case
        { pattern: /(\d+)\s*ea\s*\/\s*cs/gi, replacement: '$1 each per case' },

        // 100ea/bx - 100 each per box
        { pattern: /(\d+)\s*ea\s*\/\s*bx/gi, replacement: '$1 each per box' },

        // 12pc/pk - 12 piece per package
        { pattern: /(\d+)\s*pc\s*\/\s*pk/gi, replacement: '$1 piece per package' },

        // 24/cs - 24 per case
        { pattern: /(\d+)\s*\/\s*cs/gi, replacement: '$1 per case' },

        // 6/bx - 6 per box
        { pattern: /(\d+)\s*\/\s*bx/gi, replacement: '$1 per box' },

        // cs/6ea - case of 6 each
        { pattern: /cs\s*\/\s*(\d+)\s*ea/gi, replacement: 'case of $1 each' },

        // bx/12pc - box of 12 piece
        { pattern: /bx\s*\/\s*(\d+)\s*pc/gi, replacement: 'box of $1 piece' },

        // 12 PC / BX - 12 piece per box
        { pattern: /(\d+)\s*pc\s*\/\s*bx/gi, replacement: '$1 piece per box' }
    ];
});
