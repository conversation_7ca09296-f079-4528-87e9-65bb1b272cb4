/**
 * @NApiVersion 2.1
 * @NScriptType scheduledscript
 * @NAmdConfig /SuiteScripts/config.json
 *
 * @description get all SPL item fulfillments that need to be emailed, email out, mark as emailed
 */

define([
  "require",
  "N/log",
  "N/search",
  "N/record",
  "N/email",
  "SuiteScripts/Classes/vlmd_custom_error_object",
  "GetOrderEmailAddressLib",
  "ItemFulfillmentEmailLib",
], (/** @type {any} */ require) => {
  const log = require("N/log");
  const record = require("N/record");
  const search = require("N/search");
  const email = require("N/email");

  /** @type {import("SuiteScripts/Classes/vlmd_custom_error_object").CustomErrorObject} */
  const CustomErrorObject = require("SuiteScripts/Classes/vlmd_custom_error_object");
  const customErrorObject = new CustomErrorObject();

  /** @type {import("GetOrderEmailAddressLib").getOrderEmailAddress} */
  const getOrderEmailAddress = require("GetOrderEmailAddressLib");

  /** @type {import("ItemFulfillmentEmailLib").sendEmailLib} */
  const sendEmailLib = require("ItemFulfillmentEmailLib");

  function execute(context) {
    try {
      var helperFunctions = (function () {
        function getRecordsToEmail() {
          var searchObj = search.create({
            type: "itemfulfillment",
            filters: [
              ["type", "anyof", "ItemShip"],
              "AND",
              ["custbody_spl_email_itm_flflmnt", "is", "T"],
              "AND",
              ["custbody_spl_itm_flflmnt_eml_was_sent", "is", "F"],
              "AND",
              ["datecreated", "onorafter", "8/31/2022 5:00 pm"],
              "AND",
              ["mainline", "is", "T"],
              "AND",
              ["subsidiary", "anyof", "1"],
            ],
          });

          var results = [];

          searchObj.run().each((result) => {
            results.push(result.id);
            return true;
          });

          return results;
        }

        function getRecord(itemFulfillmentId) {
          return record.load({
            type: record.Type.ITEM_FULFILLMENT,
            id: itemFulfillmentId,
          });
        }

        function setSuccessCheckboxesOnRecord(
          itemFulfillment,
          itemFulfillmentName
        ) {
          try {
            itemFulfillment.setValue("custbody_spl_email_itm_flflmnt", false);

            itemFulfillment.setValue(
              "custbody_spl_itm_flflmnt_eml_was_sent",
              true
            );

            itemFulfillment.save();
          } catch (e) {
            const errorMessage = `The "Item Fulfillment Email Was Sent" checkbox was not set successfully for ${itemFulfillmentName}.
						Please investigate immediately so that the item fulfillment will not be emailed out multiple times.`;

            email.send({
              author: 3288,
              recipients: "<EMAIL>",
              cc: [
                "<EMAIL>",
                "<EMAIL>",
              ],
              subject: `Error setting "Item Fulfillment Email Was Sent" - ${itemFulfillmentName}`,
              body: errorMessage,
            });

            customErrorObject.throwError({
              summaryText: `3744b693-98ea-4b13-9efa-294b6d087fca : ERROR_SETTING_SUCCESS_CHECKBOXES_ON_REC`,
              error: errorMessage,
              recordType: `ITEM_FULFILLMENT`,
              errorWillBeGrouped: true,
            });
          }
        }

        return {
          getRecordsToEmail,
          getRecord,
          setSuccessCheckboxesOnRecord,
        };
      })();

      let recordsSuccessfullyEmailed = [];

      let recordsToEmail = helperFunctions.getRecordsToEmail();

      if (!recordsToEmail || recordsToEmail.length <= 0) {
        log.audit("NO_RECORDS_TO_EMAIL");
        return;
      }

      recordsToEmail.forEach((itemFulfillmentInternalId) => {
        try {
          var itemFulfillmentRecord = helperFunctions.getRecord(
            itemFulfillmentInternalId
          );
          var customerId = itemFulfillmentRecord.getValue("entity");
          var itemFulfillmentName = itemFulfillmentRecord.getValue("tranid");

          var emailAddresses =
            getOrderEmailAddress.getSalesOrderEmailAddress(customerId);

          var processingResultObj = sendEmailLib.sendItemFulfillmentEmail(
            itemFulfillmentRecord,
            emailAddresses
          );

          if (
            processingResultObj &&
            processingResultObj.processedSuccessfully
          ) {
            helperFunctions.setSuccessCheckboxesOnRecord(
              itemFulfillmentRecord,
              itemFulfillmentName
            );

            recordsSuccessfullyEmailed.push(itemFulfillmentInternalId);
          } else {
            throw {
              name: itemFulfillmentName,
              message: processingResultObj.errorLog.join(""),
            };
          }
        } catch (error) {
          customErrorObject.updateError({
            errorType: customErrorObject.ErrorTypes.UNHANDLED_ERROR,
            summary: "ERROR_EMAILING_RECORD",
            details: `Item Fulfillment, ${itemFulfillmentInternalId} not emailed: ${error.message}`,
          });
        }
      });

      if (recordsSuccessfullyEmailed.length > 0) {
        log.audit(
          "Emails Sent Successfully",
          recordsSuccessfullyEmailed.join(",")
        );
      }

      if (customErrorObject.summary) {
        throw `Soft Error`;
      }
    } catch (error) {
      customErrorObject.throwError({
        summaryText: `ERROR_SENDING_ITEM_FULFILLMENT_EMAIL`,
        error: error,
        recordType: `ITEM_FULFILLMENT`,
        errorWillBeGrouped: true,
      });
    }
  }

  return {
    execute,
  };
});
