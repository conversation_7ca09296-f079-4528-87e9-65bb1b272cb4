import { EDI810InvoiceProcessor } from "../../../../../EDI/Classes/Decorators/810/edi_810_invoice_processor";
import { EDIWalmart } from "../../../../../EDI/Classes/Models/Partner/Customer/edi_walmart";
import { EDIInvoice } from "../../../../../EDI/Classes/Models/Transaction/edi_invoice";
import { EDISalesTransaction } from "../../../../../EDI/Classes/Models/Transaction/edi_sales_transaction";

const mockDate = new Date(2025, 0, 1);

beforeAll(() => {
    jest.spyOn(global, "Date").mockImplementation(() => mockDate);
});

afterAll(() => {
    jest.restoreAllMocks();
});

describe("EDI810InvoiceProcessor", () => {
  beforeAll(() => {
    jest
      .spyOn(EDISalesTransaction.prototype, "formatToYYYYMMDD")
      .mockReturnValue("transactionDateFormatted");
    jest
      .spyOn(EDISalesTransaction.prototype, "getPurchaseOrderDate")
      .mockReturnValue("purchaseOrderDate");
    jest
      .spyOn(EDISalesTransaction.prototype, "getPurchaseOrderNumber")
      .mockReturnValue("purchaseOrderNumber");
    jest
      .spyOn(EDIInvoice.prototype, "getDepartmentNumber")
      .mockReturnValue("departmentNumber");
    jest
      .spyOn(EDIInvoice.prototype, "getLineItems")
      .mockImplementation(function () {
        this.lineItems = [
          {
            amount: 0,
            quantity: "quantity",
            rate: "rate",
            name: "Unsaleable Merchandise Allowance",
            description: "description",
            gtin: "0850052327668",
            upc: "850052327668",
          },
          {
            quantity: "quantity",
            rate: "rate",
            name: "name",
            description: "description",
            gtin: "0850052327668",
            upc: "850052327668",
          },
          {
            amount: 1,
            quantity: "quantity",
            rate: "rate",
            name: "Walmart 1% Discount",
            description: "description",
            gtin: "0850052327668",
            upc: "850052327668",
          },
          {
            quantity: "quantity",
            rate: "rate",
            name: "name",
            description: "description",
            gtin: "0850052327668",
            upc: "850052327668",
          },
          {
            amount: 2,
            quantity: "quantity",
            rate: "rate",
            name: "Unsaleable Merchandise Allowance",
            description: "description",
            gtin: "0850052327668",
            upc: "850052327668",
          },
          {
            quantity: "quantity",
            rate: "rate",
            name: "IJS.7675",
            description: "description",
            gtin: "00850052327675",
            upc: "850052327675",
          },
        ];
        this.lineCount = this.lineItems.length;
        this.subtotal = "100.00";
      });
  });
  describe("process", () => {
    const walmart = new EDIWalmart({
      direction: "out",
      transactionType: "810",
    });
    let /** @type {EDI810InvoiceProcessor} */ processor;
    beforeEach(() => {
      processor = new EDI810InvoiceProcessor({
        customError: null,
        invoice: new EDIInvoice({
          customError: null,
          index: 1,
          id: "id",
          type: "CustInvc",
          customerParent: 8454,
          otherrefnum: null,
          tranid: "documentNumber",
          departmentNumber: "departmentNumber",
          createdfrom: null,
          trandate: null,
          street: "street",
          city: "city",
          state: "state",
          zip: "zip",
          customerFullName: "customer: Name",
          customerEntityId: walmart.code,
          customerGLN: "gln",
          controlNumber: 0,
          supplierName: "supplierName",
          supplierStreet: "supplierStreet",
          supplierCity: "supplierCity",
          supplierState: "supplierState",
          supplierZip: "supplierZip",
          supplierGLN: "supplierGLN",
        }),
        parsingInformation: walmart.getParsingInformation({
          delimiters: walmart.delimiters,
          direction: "out",
        }),
        template: walmart.template.OUT[810],
        purchasingSoftwareId: walmart.purchasingSoftwareId,
      });
      processor.process();
    });
    it("formats transaction date", () => {
      expect(
        EDISalesTransaction.prototype.formatToYYYYMMDD
      ).toHaveBeenCalledWith(mockDate);
    });
    it("retrieves the purchase order date linked to the invoice", () => {
      expect(
        EDISalesTransaction.prototype.getPurchaseOrderDate
      ).toHaveBeenCalled();
    });
    it("retrieves the otherrefnum value linked to the invoice through the sales order", () => {
      expect(
        EDISalesTransaction.prototype.getPurchaseOrderNumber
      ).toHaveBeenCalled();
    });
    it("retrieves the custbody_crh_walmart_department value linked to the invoice through the sales order", () => {
      expect(EDIInvoice.prototype.getDepartmentNumber).toHaveBeenCalled();
    });
    it("parses the item sublist of the invoice", () => {
      expect(EDIInvoice.prototype.getLineItems).toHaveBeenCalled();
    });
    it("generates file content", () => {
      const expectedFileContent = `ISA*00*          *00*          *ZZ*7188210570     *08*925485US00     *250101*0000*U*00501*000000001*0*P*>~
GS*IN*7188210570*925485US00*20250101*0000*000000001*X*005010~
ST*810*0001~
BIG*transactionDateFormatted*documentNumber*purchaseOrderDate*purchaseOrderNumber~
REF*IA*084047631~
N1*SU*supplierName*UL*supplierGLN~
N3*supplierStreet~
N4*supplierCity*supplierState*supplierZip~
N1*ST*Name*UL*gln~
N3*street~
N4*city*state*zip~
ITD*08*3*2**30**35~
DTM*011*transactionDateFormatted~
FOB*CC~
SAC*A*I410***0*******02~
IT1**quantity*EA*rate**IN*name*UP*850052327668*UK*00850052327668~
PID*F****description~
SAC*A*C310***100*******02~
IT1**quantity*EA*rate**IN*name*UP*850052327668*UK*00850052327668~
PID*F****description~
SAC*A*I410***200*******02~
IT1**quantity*EA*rate**IN*IJS.7675*UP*850052327675*UK*00850052327675~
PID*F****description~
TDS*10000~
CTT*3~
SE*24*0001~
GE*1*000000001~
IEA*1*000000001~
`;
      expect(processor.fileContent).toEqual(expectedFileContent);
    });
  });
});