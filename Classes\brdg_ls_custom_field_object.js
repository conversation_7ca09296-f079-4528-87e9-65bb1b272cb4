/**
 * @description Class representing a LS custom field objects
 *
 * @NApiVersion 2.1
 * <AUTHOR>
 */

define(["exports"], (/** @type {any} */ exports) => {
	/**
	 * LS Custom Fields Object Class
	 *
	 */
	class LSCustomFieldObject {
		/**
		 * Create the custom field object
		 *
		 * @param {string} message
		 */
		constructor() {

			/** @type {{[key:string]: string}} */
			this.CustomFields = {
                ORDER_TYPE: {
                    //VineYard North
                    "0ac54c19-8c55-11ed-f913-3920e12a6941": {
                        "2461a25f-2e95-4cb9-a52a-b0a4713f365d": "Corporate Order Type",
                        "b0cce767-6b13-4858-9eb7-02d51c8bad1f": "Gift Order Type",
                        "729edf21-5f03-438a-8096-b4b7b4147580": "Wedding / Bar Mitzvah Order Type"
                    },
                    //Vineyard South
                    "0ac54c19-8c55-11ed-fd4a-75a7fae850fa": 
                    {
                        "644407bb-8032-4308-b50b-7214a013d052": "Corporate Order Type",
                        "801dc6a5-82e7-49b7-8071-2aa2fa336d8f": "Gift Order Type",
                        "f49a251c-e871-4111-9063-0276b3cba314": "Wedding / Bar Mitzvah Order Type"
                    },
                    //Vineyard Express
                    "0604d568-81ed-11ed-f947-b2006653c61d": 
                    {
                        "1253500c-94b9-4bef-be66-e4b2c0f6955e": "Corporate Order Type",
                        "c484b2e8-52d3-4a43-8305-7199f39a0794": "Gift Order Type",
                        "88d215a5-ce12-4510-b91c-9efe766f7ffc": "Wedding / Bar Mitzvah Order Type"
                    },
                    //Vineyard Westgate
                    "0ac54c19-8c55-11ed-fd4a-8d314c8bb4e4": 
                    {
                        "d34d94c3-6aa8-442f-a007-91f5c54c774b": "Corporate Order Type",
                        "b5520fc6-931f-4867-985d-976bfd547737": "Gift Order Type",
                        "600170f7-7c77-4409-aeb4-0177a590014a": "Wedding / Bar Mitzvah Order Type"
                    },
                    //Vineyard Bergenfield
                    "0ac54c19-8c55-11ed-fd4a-8d31f3232984": 
                    {
                        "bf75423e-665d-4800-8746-ec87961dd594": "Corporate Order Type",
                        "e30936e7-bffb-48cb-9f50-9cc33836aa6d": "Gift Order Type",
                        "42797395-4482-467a-9073-bf61a5c1e36d": "Wedding / Bar Mitzvah Order Type"
                    }
                },
                ORDER_SOURCE: {
                    //Vineyard North
                    "0ac54c19-8c55-11ed-f913-3920e12a6941": {
                        "004f80c4-0438-4aa7-a2de-5cca899302e8": "WhatsApp Order",
                        "0c1ac9c1-f4bd-4a81-9cd6-797d99be55aa": "Retail Order",
                        "9140155a-76c2-40b1-9447-97bad91528ee": "Phone Order",
                        "788947e4-3e2f-4848-afe4-3233d23cc17b": "Email Order",
                        "5cf1594e-2354-4ba2-ac9b-740d9c178fd2": "DoorDash Order",
                        "31f506b0-4549-4dd5-a7f4-8680bd6ec836":
                            "WhatsApp Order - Yehuda Stefansky",
                        "bdcc430a-02c1-4e90-aeab-5bd505fe15b6":
                            "WhatsApp Order - Simcha Ingber",
                        "8ac99481-1d0b-45bf-9f08-9c51e09a1470":
                            "WhatsApp Order - Sruly Iowiniski",
                        "7d153078-5770-40bd-bb75-9ce4e336e07f":
                            "WhatsApp Order - Shea Berkowitz",
                        "909c01ff-8c6f-4f76-860c-9fe952299782":
                            "WhatsApp Order - Aryeh Kranz"
                    },
                    //Vineyard Westgate
                    "0ac54c19-8c55-11ed-fd4a-8d314c8bb4e4": {
                        "11e7ec50-6c5a-4dd8-bc3f-d76db9da927f": "WhatsApp Order",
                        "d2e49788-b9da-4c4d-b005-e3321f6f369c": "Retail Order",
                        "caa0fe15-fcf7-451a-8bcd-1fed09b0554c": "Phone Order",
                        "058b8fea-dd73-4bec-8fe3-9278ad767bff": "Email Order",
                        "c1bb73ea-1d20-4eab-9e84-69898744b052": "DoorDash Order",
                        "80b1ac6f-5d7d-467f-8beb-06f7b1e3409d":
                            "WhatsApp Order - Yehuda Stefansky",
                        "7bf319a4-a009-4afa-9b72-b377a94cc90c":
                            "WhatsApp Order - Simcha Ingber",
                        "62725517-6d98-4c27-aa64-ea99f4e6c9c0":
                            "WhatsApp Order - Naftali Bandman"
                    },
                    //Vineyard South
                    "0ac54c19-8c55-11ed-fd4a-75a7fae850fa": {
                        "79fb502e-c56f-456e-be2b-f4c6903e6bf0": "WhatsApp Order",
                        "eaf3794e-e539-419e-9fa4-0cf5c79d4868": "Retail Order",
                        "7f400917-00b0-432b-881c-e0f08f7ac150": "Phone Order",
                        "41cd9d93-837a-4f1a-9a89-278781fe432c": "Email Order",
                        "9c6ca88e-1086-4c6d-a7c9-93ae78153fb5": "DoorDash Order",
                        "fc6668aa-717c-404c-bded-d8c526801a68":
                            "WhatsApp Order - Yehuda Stefansky",
                        "8ff27c43-add2-47e1-ad81-ffc93b109b73":
                            "WhatsApp Order - Simcha Ingber",
                        "ec0737bb-9540-4a38-b47d-1d402213952a":
                            "WhatsApp Order - Shuki Waldman"
                    },
                    //Vineyard Express
                    "0604d568-81ed-11ed-f947-b2006653c61d": {
                        "5b7bc88d-966a-4aa6-95e0-6285e6edcebb": "WhatsApp Order",
                        "40cac8da-5eef-4b69-a828-3677c600a117": "Retail Order",
                        "9bbaf4ed-8a7b-4a59-9185-201e8213d21b": "Phone Order",
                        "93ed6774-38b6-4c1e-9de7-e1d4d1d5b76a": "Email Order",
                        "26bab28d-d289-435b-b437-72b87e334d95": "DoorDash Order",
                        "59405c22-a5f9-469f-9ea1-41787c1fd3fd":
                            "WhatsApp Order - Yehuda Stefansky",
                        "ba7cc74e-3470-4e56-b38e-11864bab7959":
                            "WhatsApp Order - Simcha Ingber",
                        "7429141b-9785-486a-839e-bf721eac9e14":
                            "WhatsApp Order - Meir Walden"
                    },
                    //Vineyard Bergenfield
                    "0ac54c19-8c55-11ed-fd4a-8d31f3232984": {
                        "d8a0a5a1-c087-4c99-a36a-41ecb1059935": "WhatsApp Order",
                        "522f8b57-8abe-45ad-bd64-da75f935d9d4": "Retail Order",
                        "f134f2f5-c6ec-47e4-b03a-5b27ea21f35a": "Phone Order",
                        "f6a2070f-4584-43b0-a90e-538ea44bb62a": "Email Order",
                        "bb2c5458-2c1a-4f08-8d51-c6496fbef756": "DoorDash Order",
                        "39cc04bc-ce4f-4384-b5cd-89606a9b5efc":
                            "WhatsApp Order - Yehuda Stefansky",
                        "f4a35eee-128f-4ea6-b870-5fc8591339c2":
                            "WhatsApp Order - Simcha Ingber",
                        "e004d093-a5fc-4ff5-94e0-4f184a05ea80":
                            "WhatsApp Order - Dovi Hirsch"
                    },
                    //Lots of Liquor
                    "0ac54c19-8c55-11ed-fd4a-75a7dd482da4": {
                        "5cdc12ee-dd76-44e5-91c0-156017e46158": "WhatsApp Order",
                        "1e0a762a-196f-4435-9653-5a4dc9c5e0d9": "Retail Order",
                        "01b1744f-4d0b-4a2d-8493-f06e77dbb61d": "Phone Order",
                        "923b0323-a444-413f-9ca7-3c1260961198": "Email Order",
                        "1acbaeaa-db10-4b13-836d-280ef0ef6555": "DoorDash Order",
                        "6492d76e-f5ac-481b-952d-0b8ef2e1ce68":
                            "WhatsApp Order - Yehuda Stefansky",
                        "685b474c-d093-4e58-96a5-ad8705c51586":
                            "WhatsApp Order - Simcha Ingber"
                    }
                }
			};
		}
	}

	exports.LSCustomFieldObject = LSCustomFieldObject;

	return LSCustomFieldObject;
});
