/**
 * @description Client-side scripts for bid sheet review functionality
 *
 * @NApiVersion 2.1
 *
 * <AUTHOR>
 * @module spl_bid_sheet_review_client_scripts_class
 */
define([
    "exports",
    "../../../../Classes/vlmd_custom_error_object",
], (
    /** @type {any} */ exports,
    /** @type {any} */ CustomErrorObject
) => {

    class BidSheetReviewClientScripts {
        constructor() {
            this.customErrorObject = new CustomErrorObject();
        }

        /**
         * Generate all client-side JavaScript functions
         * @param {string} scriptURL - The URL of the script
         * @returns {string} - JavaScript string with all client-side functions
         */
        generateAllClientScripts(scriptURL) {
            return `
                ${this.jsStringBidSheetItemsRequestFunction(scriptURL)}
                ${this.jsStringLoadAllItemsRequestFunction(scriptURL)}
                ${this.jsStringclearVerifiedAndOverridenItemsRequestFunction(scriptURL)}
                ${this.jsStringRenderBidSheetItemsTableFunction()}
                ${this.jsStringEventHandlerFunction(scriptURL)}
                ${this.jsStringDataTableLogic()}
                ${this.jsStringModalLogic(scriptURL)}
            `;
        }

        /**
         * Generate JavaScript for bid sheet items request function
         * @param {string} scriptURL - The URL of the script
         * @returns {string} - JavaScript string
         */
        jsStringBidSheetItemsRequestFunction(scriptURL) {
            return /*js*/`
                function bidSheetItemsRequest(bidSheetId) {
                    if (!bidSheetId) {
                        alert('Missing bid sheet ID.');
                        return;
                    }

                    let paramsJsonObj = JSON.stringify({bidSheetId});

                    var requestPayload = {
                        "function": "getBidSheetItems",
                        "paramsJsonObj": paramsJsonObj,
                    }

                    var xhr = new XMLHttpRequest();
                    xhr.open('POST', '${scriptURL}', true);
                    xhr.setRequestHeader('Accept', 'application/json');
                    xhr.send(JSON.stringify(requestPayload));
                    xhr.onload = function() {
                        if (xhr.status === 200) {
                            try {
                                bidSheetItemsResponsePayload = JSON.parse(xhr.response);
                            } catch (err) {
                                alert('Unable to parse the response.');
                                return;
                            }

                            if(bidSheetItemsResponsePayload.bidSheetItems.length === 0) {
                                alert('No items found for the bid sheet.');
                                return;
                            }

                            if(bidSheetItemsResponsePayload.error === undefined) {
                                renderBidSheetItemsTable();
                            } else {
                                alert('Error: ' + bidSheetItemsResponsePayload.error.message);
                                return;
                            }

                        } else {
                            alert("Error: " + xhr.status);
                        }
                    }

                    // Add error handler for network issues
                    xhr.onerror = function() {
                        alert("Network error occurred while trying to fetch bid sheet items.");
                    };
                }
            `;
        }

        /**
         * Generate JavaScript for load all items request function
         * @param {string} scriptURL - The URL of the script
         * @returns {string} - JavaScript string
         */
        jsStringLoadAllItemsRequestFunction(scriptURL) {
            return /*js*/`
                function loadAllItems() {
                    // Try to load from localStorage first
                    const cachedItems = localStorage.getItem('bidSheetItemsCache');
                    const cacheTimestamp = localStorage.getItem('bidSheetItemsCacheTimestamp');
                    const cacheExpiry = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

                    // Check if we have a valid cache (not expired)
                    if (cachedItems && cacheTimestamp && (Date.now() - parseInt(cacheTimestamp) < cacheExpiry)) {
                        try {
                            // Use the cached data
                            const parsedCache = JSON.parse(cachedItems);
                            allItemsCache = parsedCache;

                            // Rebuild the indexed version
                            itemsIndexed = {};
                            allItemsCache.forEach(item => {
                                itemsIndexed[item.id] = item;
                            });

                            console.log('Loaded ' + allItemsCache.length + ' items from localStorage cache');
                            return;
                        } catch (err) {
                            console.error('Error parsing cached items, will fetch from server: ' + err.message);
                            // Continue to fetch from server if parsing fails
                        }
                    }

                    // If no valid cache exists, fetch from server
                    var requestPayload = {
                        "function": "getAllItems"
                    };

                    var xhr = new XMLHttpRequest();
                    xhr.open('POST', '${scriptURL}', true);
                    xhr.setRequestHeader('Accept', 'application/json');
                    xhr.send(JSON.stringify(requestPayload));
                    xhr.onload = function() {
                        if (xhr.status === 200) {
                            try {
                                const response = JSON.parse(xhr.response);
                                if(response.error === undefined) {
                                    // Store items in cache
                                    allItemsCache = response.items;

                                    // Create indexed version for faster lookups
                                    allItemsCache.forEach(item => {
                                        itemsIndexed[item.id] = item;
                                    });

                                    // Create search index for faster filtering
                                    allItemsCache.forEach(item => {
                                        // Create a searchable string for each item
                                        item.searchText = [
                                            item.id,
                                            item.itemId,
                                            item.displayName,
                                            item.vendorCode,
                                            item.category
                                        ].filter(Boolean).join(' ').toLowerCase();
                                    });

                                    // Save to localStorage with timestamp
                                    try {
                                        localStorage.setItem('bidSheetItemsCache', JSON.stringify(allItemsCache));
                                        localStorage.setItem('bidSheetItemsCacheTimestamp', Date.now().toString());
                                        console.log('Saved ' + allItemsCache.length + ' items to localStorage cache');
                                    } catch (storageErr) {
                                        // Handle storage errors (e.g., quota exceeded)
                                        console.error('Failed to save to localStorage: ' + storageErr.message);
                                    }

                                    console.log('Loaded ' + allItemsCache.length + ' items from server');
                                } else {
                                    console.error('Error loading items: ' + response.error.message);
                                }
                            } catch (err) {
                                console.error('Unable to parse the response: ' + err.message);
                            }
                        } else {
                            console.error("Error loading items: " + xhr.status);
                        }
                    };
                }
            `;
        }

        /**
         * Generate JavaScript for clear verified and overridden items request function
         * @param {string} scriptURL - The URL of the script
         * @returns {string} - JavaScript string
         */
        jsStringclearVerifiedAndOverridenItemsRequestFunction(scriptURL) {
            return /*js*/`
                function clearVerifiedAndOverridenItemsRequest(bidSheetId) {
                    if (!bidSheetId) {
                        alert('Missing bid sheet ID.');
                        return;
                    }

                    let paramsJsonObj = JSON.stringify({bidSheetId});

                    var requestPayload = {
                        "function": "clearVerifiedAndOverridenItems",
                        "paramsJsonObj": paramsJsonObj,
                    }

                    var xhr = new XMLHttpRequest();
                    xhr.open('POST', '${scriptURL}', true);
                    xhr.setRequestHeader('Accept', 'application/json');
                    xhr.send(JSON.stringify(requestPayload));
                    xhr.onload = function() {
                        if (xhr.status === 200) {
                            try {
                                let response = JSON.parse(xhr.response);
                                if(response.error === undefined) {
                                    alert('All verified and overridden items have been cleared.');
                                    // Reload the bid sheet items to refresh the table
                                    bidSheetItemsRequest(bidSheetId);
                                } else {
                                    alert('Error: ' + response.error.message);
                                }
                            } catch (err) {
                                alert('Unable to parse the response.');
                            }
                        } else {
                            alert("Error: " + xhr.status);
                        }
                    }
                }
            `;
        }

        /**
         * Generate JavaScript for render bid sheet items table function
         * @returns {string} - JavaScript string
         */
        jsStringRenderBidSheetItemsTableFunction() {
            return /*js*/`
                function renderBidSheetItemsTable() {

                    // Check for empty items first
                    if (bidSheetItemsResponsePayload.bidSheetItems.length === 0) {
                        document.getElementById('matches-container').innerHTML =
                            '<div class="alert alert-warning">No bid sheet items found.</div>';
                        return;
                    }

                    // If we have items, proceed with rendering
                    let itemsHtml = '';

                    // Create table structure
                    itemsHtml += '<div class="container-fluid" style="width: 100%; max-width: 100%; padding: 0;">';
                    itemsHtml += '<div id="items-container" style="width: 100%; max-width: 100%;">';
                    itemsHtml += '<table id="bidItemsTable" class="table table-bordered match-table" style="width: 100%; max-width: 100%;">';

                    // Add table header
                    itemsHtml += '<thead>';
                    itemsHtml += '<tr>';
                    itemsHtml += '<th class="align-middle py-3" style="width: 22%;">Customer Item Description</th>';
                    itemsHtml += '<th class="align-middle py-3" style="width: 22%;">Rank 1 Match</th>';
                    itemsHtml += '<th class="align-middle py-3" style="width: 22%;">Rank 2 Match</th>';
                    itemsHtml += '<th class="align-middle py-3" style="width: 22%;">Rank 3 Match</th>';
                    itemsHtml += '<th class="align-middle text-center py-3" style="width: 6%;">Actions</th>';
                    itemsHtml += '</tr>';
                    itemsHtml += '</thead>';
                    itemsHtml += '<tbody>';

                    // Process each bid sheet item
                    bidSheetItemsResponsePayload.bidSheetItems.forEach(function(item) {

                        let customerItemDescription = item.custrecord_spl_bsi_row_data || '';
                        let rowNumber = item.row_number || '';

                        let shortDescription = customerItemDescription.length > 100 ?
                            customerItemDescription.substring(0, 100) + '...' :
                            customerItemDescription;

                        let potentialMatches = [];
                        try {
                            potentialMatches = JSON.parse(item.custrecord_spl_bsi_potential_matches || '[]');
                        } catch (e) {
                            console.error('Error parsing potential matches for item ' + item.id + ': ' + e.message);
                        }

                        let hasVerifiedMatch = item.custrecord_spl_bsi_item_match ? true : false;
                        let hasOverrideMatch = item.custrecord_spl_bsi_match_override ? ((item.custrecord_spl_bsi_match_override === 'T') ? true : false) : false;
                        let hasSkipped = false;
                        if(item.custrecord_spl_bsi_skipped && item.custrecord_spl_bsi_skipped === 'T') {
                            hasSkipped = true;
                            hasVerifiedMatch = true;
                        }

                        // Start row with verified class if needed
                        const rowClass = hasVerifiedMatch ? 'verified-row' : '';
                        itemsHtml += '<tr class="' + rowClass + '" data-has-verified="' + hasVerifiedMatch + '">';

                        // Customer description cell
                        itemsHtml += '<td>';
                        itemsHtml += '<div class="item-header">';
                        itemsHtml += '<div style="font-size: 12px;">';
                        itemsHtml += '<div style="color: #777; font-size: 10px; margin-bottom: 3px;">Row: ' + rowNumber + '</div>';
                        itemsHtml += '<span id="short-' + item.id + '" style="display: inline;">' + shortDescription + '</span>';
                        itemsHtml += '<span id="full-' + item.id + '" style="display: none;">' + customerItemDescription + '</span>';

                        if (customerItemDescription.length > 100) {
                            itemsHtml += '<br><button type="button" onclick="toggleText(' + item.id + ')" ';
                            itemsHtml += 'id="btn-' + item.id + '" style="color: blue; background: none; border: none; ';
                            itemsHtml += 'text-decoration: underline; cursor: pointer; padding: 0; font-size: 11px;">';
                            itemsHtml += 'Show more</button>';
                        }

                        // Check if user is admin
                        const userRole = document.getElementById('adminTools') ? true : false;
                        if(userRole) {
                            itemsHtml += '<div class="admin-tools mt-2">';
                            itemsHtml += '<a href="/app/common/custom/custrecordentry.nl?rectype=3376&id=' + item.id + '" ';
                            itemsHtml += 'target="_blank" class="btn btn-outline-danger btn-sm">';
                            itemsHtml += 'Go To Bid Sheet Item</a>';
                            itemsHtml += '</div>';
                        }

                        itemsHtml += '</div>';
                        itemsHtml += '</div>';
                        itemsHtml += '</td>';

                        for (let i = 0; i < 3; i++) {
                            let match = potentialMatches[i];

                            if(hasOverrideMatch || hasSkipped) {

                                // Format item details
                                let itemIdHtml = '<div style="font-size: 14px; font-weight: bold;">' + item.override_item_id + '</div>';
                                let vendorCodeHtml = '<div style="font-size: 12px;">Vendor Code: ' + item.override_vendor_code + '</div>' ;
                                let displayNameHtml = '<div style="font-size: 13px;">' + item.override_display_name + '</div>';

                                // Create view item link
                                let viewItemLink = '';
                                if (item.override_item_internal_id) {
                                    viewItemLink = '<a href="#" class="action-link view-link" data-item-id="' + item.override_item_internal_id + '"><i class="fas fa-eye"></i> View</a>';
                                }

                                itemsHtml += '<td class="match-cell">';
                                itemsHtml += '<div class="match-details">';
                                itemsHtml += '<div class="item-info">';
                                itemsHtml += '<div style="display: flex; justify-content: space-between; align-items: flex-start; width: 100%; overflow: hidden;">';
                                itemsHtml += '<div style="flex: 1; min-width: 0; padding-right: 10px;">';
                                itemsHtml += (hasSkipped) ? '' : itemIdHtml;
                                itemsHtml += (hasSkipped) ? '' : vendorCodeHtml;
                                itemsHtml += (hasSkipped) ? '' : displayNameHtml;
                                itemsHtml += '</div>';
                                itemsHtml += '<div style="flex-shrink: 0;">';
                                if(hasSkipped) {
                                    itemsHtml += '<span class="score-pill verified-pill">Skipped</span>';
                                } else {
                                    itemsHtml += '<span class="score-pill verified-pill">Overridden</span>';
                                }
                                itemsHtml += '</div>';
                                itemsHtml += '</div>';
                                itemsHtml += '</div>';
                                itemsHtml += '<div class="action-buttons">';
                                itemsHtml += viewItemLink;
                                itemsHtml += '</div>';
                                itemsHtml += '</div>';
                                itemsHtml += '</td>';

                                // add blank cells
                                itemsHtml += '<td class="match-cell"></td>';
                                itemsHtml += '<td class="match-cell"></td>';

                                break;

                            } else if (match) {
                                // Check if this match is the verified one
                                const isVerified = hasVerifiedMatch && match.itemInternalId === item.custrecord_spl_bsi_item_match;

                                // Determine score class
                                let scoreClass = '';
                                if (isVerified) {
                                    scoreClass = 'verified-pill';
                                } else if (match.score >= 80) {
                                    scoreClass = 'score-high';
                                } else if (match.score >= 50) {
                                    scoreClass = 'score-medium';
                                } else {
                                    scoreClass = 'score-low';
                                }

                                // Format item details
                                let itemIdHtml = match.itemId ?
                                    '<div style="font-size: 14px; font-weight: bold;">' + match.itemId + '</div>' : '';
                                let vendorCodeHtml = match.vendorCode ?
                                    '<div style="font-size: 12px;">Vendor Code: ' + match.vendorCode + '</div>' : '';
                                let displayNameHtml = match.displayName ?
                                    '<div style="font-size: 13px;">' + match.displayName + '</div>' : '';

                                itemsHtml += '<td class="match-cell ' + (hasVerifiedMatch && !isVerified ? 'unselected-match' : '') + '">';
                                itemsHtml += '<div class="match-details">';
                                itemsHtml += '<div class="item-info">';
                                itemsHtml += '<div style="display: flex; justify-content: space-between; align-items: flex-start; width: 100%; overflow: hidden;">';
                                itemsHtml += '<div style="flex: 1; min-width: 0; padding-right: 10px;">';
                                itemsHtml += itemIdHtml;
                                itemsHtml += vendorCodeHtml;
                                itemsHtml += displayNameHtml;
                                itemsHtml += '</div>';
                                itemsHtml += '<div style="flex-shrink: 0;">';

                                if(hasVerifiedMatch) {
                                    if(isVerified) {
                                        itemsHtml += '<span class="score-pill verified-pill">Verified</span>';
                                    }
                                    else {
                                        itemsHtml += '<span class="score-pill ' + scoreClass + ' unselected-pill">' + match.score + '% match</span>';
                                    }
                                } else {
                                    itemsHtml += '<span class="score-pill ' + scoreClass + '">' + match.score + '% match</span>';
                                }


                                itemsHtml += '</div>';
                                itemsHtml += '</div>';
                                itemsHtml += '</div>';
                                itemsHtml += '<div class="action-buttons">';
                                itemsHtml += '<a href="#" class="action-link view-link" data-item-id="' + match.itemInternalId + '"><i class="fas fa-eye"></i> View</a>';
                                itemsHtml += '<a href="#" class="action-link verify-link" data-item-id="' + match.itemInternalId + '" data-bid-item-id="' + item.id + '">';
                                itemsHtml += '<i class="fas fa-check"></i> Verify</a>';
                                itemsHtml += '</div>';
                                itemsHtml += '</div>';
                                itemsHtml += '</td>';
                            } else {
                                itemsHtml += '<td class="match-cell' + (hasVerifiedMatch ? ' unselected-match' : '') + '"></td>';
                            }
                        }

                        // Add action column
                        itemsHtml += '<td style="text-align: center; vertical-align: middle; padding: 8px;">';
                        itemsHtml += '<a href="javascript:void(0)" class="action-link mb-2 override-link" data-toggle="modal" ';
                        itemsHtml += 'data-target="#overrideModal" data-bid-item-id="' + item.id + '" data-bid-sheet-id="' + bidSheetId + '" ';
                        itemsHtml += 'data-customer-description="' + customerItemDescription.replace(/"/g, '&quot;') + '" ';
                        itemsHtml += (hasOverrideMatch || hasSkipped ? 'style="display:none;"' : '') + '>';
                        itemsHtml += '<i class="fas fa-exchange-alt"></i> Override</a>';

                        itemsHtml += '<a href="javascript:void(0)" class="action-link mb-2 undo-override-link" ';
                        itemsHtml += 'data-bid-item-id="' + item.id + '" data-bid-sheet-id="' + bidSheetId + '" ';
                        itemsHtml += (hasOverrideMatch || hasSkipped ? '' : 'style="display:none;"') + '>';
                        itemsHtml += '<i class="fas fa-undo"></i> Undo</a>';
                        
                        itemsHtml += '<a href="javascript:void(0)" class="action-link skip-link" ';
                        itemsHtml += 'data-bid-item-id="' + item.id + '" data-bid-sheet-id="' + bidSheetId + '" ';
                        itemsHtml += (hasOverrideMatch || hasSkipped ? 'style="display:none;"' : '') + '>';
                        itemsHtml += '<i class="fas fa-forward"></i> Skip</a>';

                        itemsHtml += '</td>';
                        itemsHtml += '</tr>';
                    });

                    // Close table structure
                    itemsHtml += '</tbody>';
                    itemsHtml += '</table>';
                    itemsHtml += '</div>';
                    itemsHtml += '</div>';

                    // Replace the loading spinner with the table
                    document.getElementById('matches-container').innerHTML = itemsHtml;

                    // Initialize DataTable
                    initializeDataTable();
                }
            `;
        }

        /**
         * Generate JavaScript for event handler function
         * @param {string} scriptURL - The URL of the script
         * @returns {string} - JavaScript string
         */
        jsStringEventHandlerFunction(scriptURL) {
            return /*js*/`
                // Handle button clicks and interactions
                function setupEventHandlers() {
                    // Handle verify button clicks
                    jQuery(document).on('click', '.verify-link', function(e) {
                        e.preventDefault();
                        const itemId = jQuery(this).data('item-id');
                        const bidItemId = jQuery(this).data('bid-item-id');
                        if (itemId && bidItemId) {
                            let paramsJsonObj = JSON.stringify({
                                action: 'verify',
                                itemId,
                                bidItemId
                            });

                            var requestPayload = {
                                "function": "saveBidSheetItem",
                                "paramsJsonObj": paramsJsonObj,
                            }

                            // Show loading indicator for the specific row
                            const rowElement = jQuery(this).closest('tr');
                            rowElement.addClass('updating-row');

                            var xhr = new XMLHttpRequest();
                            xhr.open('POST', '${scriptURL}', true);
                            xhr.setRequestHeader('Accept', 'application/json');
                            xhr.send(JSON.stringify(requestPayload));
                            xhr.onload = function() {
                                if (xhr.status === 200) {
                                    try {
                                        verifyMatchResponsePayload = JSON.parse(xhr.response);
                                    } catch (err) {
                                        alert('Unable to parse the response.');
                                        return;
                                    }

                                    if(verifyMatchResponsePayload.error === undefined) {
                                        updateSingleRow(verifyMatchResponsePayload.updatedItem, 'verify');
                                    } else {
                                        alert('Error: ' + verifyMatchResponsePayload.error.message);
                                        return;
                                    }

                                } else {
                                    alert("Error: " + xhr.status);
                                }
                            }
                        }
                    });

                    // Handle View button clicks
                    jQuery(document).on('click', '.view-link', function(e) {
                        e.preventDefault();
                        const itemId = jQuery(this).data('item-id');
                        if (itemId) {
                            window.open('/app/common/item/item.nl?id=' + itemId, '_blank');
                        }
                    });

                    // Handle Undo Override link clicks
                    jQuery(document).on('click', '.undo-override-link', function() {
                        const bidItemId = jQuery(this).data('bid-item-id');
                        const bidSheetId = jQuery(this).data('bid-sheet-id');

                        if (confirm('Are you sure you want to remove this override?')) {
                            let paramsJsonObj = JSON.stringify({
                                action: 'undooverride',
                                bidSheetId: bidSheetId,
                                bidItemId: bidItemId
                            });

                            var requestPayload = {
                                "function": "saveBidSheetItem",
                                "paramsJsonObj": paramsJsonObj,
                            };

                            // Show loading indicator for the specific row
                            const rowElement = jQuery(this).closest('tr');
                            rowElement.addClass('updating-row');

                            var xhr = new XMLHttpRequest();
                            xhr.open('POST', '${scriptURL}', true);
                            xhr.setRequestHeader('Accept', 'application/json');
                            xhr.send(JSON.stringify(requestPayload));
                            xhr.onload = function() {
                                if (xhr.status === 200) {
                                    try {
                                        const response = JSON.parse(xhr.response);

                                        if(response.error === undefined && response.success) {
                                            updateSingleRow(response.updatedItem, 'undooverride');
                                        } else {
                                            alert('Error: ' + (response.error ? response.error.message : 'Unknown error'));
                                            rowElement.removeClass('updating-row');
                                        }
                                    } catch (err) {
                                        alert('Unable to parse the response.');
                                        rowElement.removeClass('updating-row');
                                    }
                                } else {
                                    alert("Error: " + xhr.status);
                                    rowElement.removeClass('updating-row');
                                }
                            };
                        }
                    });
                
                    // Handle Skip link clicks
                    jQuery(document).on('click', '.skip-link', function() {
                        const bidItemId = jQuery(this).data('bid-item-id');
                        const bidSheetId = jQuery(this).data('bid-sheet-id');

                        if (confirm('Are you sure you want to skip this item?')) {
                            let paramsJsonObj = JSON.stringify({
                                action: 'skip',
                                bidSheetId: bidSheetId,
                                bidItemId: bidItemId
                            });

                            var requestPayload = {
                                "function": "saveBidSheetItem",
                                "paramsJsonObj": paramsJsonObj,
                            };

                            // Show loading indicator for the specific row
                            const rowElement = jQuery(this).closest('tr');
                            rowElement.addClass('updating-row');

                            var xhr = new XMLHttpRequest();
                            xhr.open('POST', '${scriptURL}', true);
                            xhr.setRequestHeader('Accept', 'application/json');
                            xhr.send(JSON.stringify(requestPayload));
                            xhr.onload = function() {
                                if (xhr.status === 200) {
                                    try {
                                        const response = JSON.parse(xhr.response);

                                        if(response.error === undefined && response.success) {
                                            updateSingleRow(response.updatedItem, 'skip');
                                        } else {
                                            alert('Error: ' + (response.error ? response.error.message : 'Unknown error'));
                                            rowElement.removeClass('updating-row');
                                        }
                                    } catch (err) {
                                        alert('Unable to parse the response.');
                                        rowElement.removeClass('updating-row');
                                    }
                                } else {
                                    alert("Error: " + xhr.status);
                                    rowElement.removeClass('updating-row');
                                }
                            };
                        }
                    });
                    
                    // Handle File Download button clicks
                    jQuery(document).on('click', '.file-download-btn', function() {
                        if(confirm("Any unverified or non-overridden rows will have blank SPL item information in the CSV. Continue?")) {
                            const fileName = prompt('Enter the file name:');
                            if(!fileName) {
                                alert('Please enter a file name');
                                return;
                            };

                            const fileType = jQuery(this).data('file-type');
                            if(fileType === 'csv') {
                                window.open('${scriptURL}&function=downloadFile&bidsheetid=' + bidSheetId + '&filename=' + fileName);
                            } else {
                                alert('File type not supported');
                            }
                        }
                    });

                    // Toggle text function for description expansion
                    window.toggleText = function(id) {
                        var shortSpan = document.getElementById("short-" + id);
                        var fullSpan = document.getElementById("full-" + id);
                        var button = document.getElementById("btn-" + id);
                        if (shortSpan.style.display === "inline") {
                            shortSpan.style.display = "none";
                            fullSpan.style.display = "inline";
                            button.textContent = "Show less";
                        } else {
                            shortSpan.style.display = "inline";
                            fullSpan.style.display = "none";
                            button.textContent = "Show more";
                        }
                    }
                }
            `;
        }

        /**
         * Generate JavaScript for DataTable logic
         * @returns {string} - JavaScript string
         */
        jsStringDataTableLogic() {
            return /*js*/`
                // Initialize DataTable
                function initializeDataTable() {
                    table = new DataTable('#bidItemsTable', {
                        responsive: true,
                        pageLength: 10,
                        ordering: true,
                        language: {
                            lengthMenu: "_MENU_ _ENTRIES_ per page",
                            info: "Showing _START_ to _END_ of _TOTAL_ _ENTRIES_",
                            entries: {
                                _: 'items',
                                1: 'item'
                            }
                        },
                        layout: {
                            topStart: 'pageLength',
                            topEnd: 'search',
                            bottomStart: 'info',
                            bottomEnd: 'paging'
                        },
                        columnDefs: [
                            { responsivePriority: 1, targets: 0 }, // Customer Item Description
                            { responsivePriority: 2, targets: 1 }, // Best Match
                            { responsivePriority: 3, targets: 4 }, // Override Matches
                        ]
                    });

                    DataTable.ext.search.push(function(settings, data, dataIndex) {
                        const showUnverifiedOnly = document.getElementById('show-unverified-only').checked;
                        if (!showUnverifiedOnly) return true;

                        const row = table.row(dataIndex).node();
                        const isVerified = row.getAttribute('data-has-verified') === 'true';
                        return !isVerified;
                    });

                    document.getElementById('show-unverified-only').addEventListener('change', () => table.draw());

                    return table; // Return the table instance for later use
                }

                // Function to update a single row in the DataTable
                function updateSingleRow(updatedItem, action) {
                    if (!updatedItem) return;

                    const rowIndex = table.rows().indexes().filter(function(value, index) {
                        return jQuery(table.row(value).node()).find('a[data-bid-item-id="' + updatedItem.id + '"]').length > 0;
                    });

                    const rowNode = table.row(rowIndex[0]).node();
                    const $row = jQuery(rowNode);

                    // Check if this is an override or undo operation
                    if (action === 'override') {
                        // Override operation
                        $row.addClass('verified-row');
                        $row.attr('data-has-verified', 'true');

                        // Update the action links visibility
                        $row.find('.override-link').hide();
                        $row.find('.undo-override-link').show();

                        // Clear all match cells first
                        $row.find('.match-cell').empty();

                        const matchCell = $row.find('.match-cell').eq(0);
                        matchCell.removeClass('unselected-match');
                        matchCell.html(
                            '<div class="match-details">' +
                                '<div class="item-info">' +
                                    '<div style="display: flex; justify-content: space-between; align-items: flex-start; width: 100%; overflow: hidden;">' +
                                        '<div style="flex: 1; min-width: 0; padding-right: 10px;">' +
                                            '<div style="font-size: 14px; font-weight: bold;">' + (updatedItem.override_item_id || '') + '</div>' +
                                            '<div style="font-size: 12px;">Vendor Code: ' + (updatedItem.override_vendor_code || '') + '</div>' +
                                            '<div style="font-size: 13px;">' + (updatedItem.override_display_name || '') + '</div>' +
                                        '</div>' +
                                        '<div style="flex-shrink: 0;">' +
                                            '<span class="score-pill verified-pill">Overridden</span>' +
                                        '</div>' +
                                    '</div>' +
                                '</div>' +
                            '</div>'
                        );
                    } else if (action === 'undooverride') {
                        // Undo override operation
                        $row.removeClass('verified-row');
                        $row.attr('data-has-verified', 'false');

                        // Update the action links visibility
                        $row.find('.override-link').show();
                        $row.find('.skip-link').show();
                        $row.find('.undo-override-link').hide();

                        $row.removeClass('updating-row');
                        updatedItem.custrecord_spl_bsi_potential_matches = JSON.parse(updatedItem.custrecord_spl_bsi_potential_matches);
                        updatedItem.custrecord_spl_bsi_potential_matches.forEach(function(match, index) {
                            const $cell = $row.find('.match-cell').eq(index);
                            $cell.removeClass('unselected-match');
                            $cell.html(
                                '<div class="match-details">' +
                                    '<div class="item-info">' +
                                        '<div class="d-flex justify-content-between align-items-start w-100 overflow-hidden">' +
                                            '<div class="flex-grow-1 min-width-0 pr-2">' +
                                                '<div class="font-weight-bold" style="font-size: 14px;">' + (match.itemId || '') + '</div>' +
                                                '<div class="small">Vendor Code: ' + (match.vendorCode || '') + '</div>' +
                                                '<div style="font-size: 13px;">' + (match.displayName || '') + '</div>' +
                                            '</div>' +
                                            '<div class="flex-shrink-0">' +
                                                '<span class="score-pill ' + (match.score >= 80 ? 'score-high' : match.score >= 50 ? 'score-medium' : 'score-low') + '">' + match.score + '% match</span>' +
                                            '</div>' +
                                        '</div>' +
                                        '<div class="action-buttons">' +
                                            '<a href="#" class="action-link view-link" data-item-id="' + match.itemInternalId + '"><i class="fas fa-eye"></i> View</a>' +
                                            '<a href="#" class="action-link verify-link" data-item-id="' + match.itemInternalId + '" data-bid-item-id="' + updatedItem.id + '"><i class="fas fa-check"></i> Verify</a>' +
                                        '</div>' +
                                    '</div>' +
                                '</div>'
                            );
                        });
                    } else if (action === 'verify') {
                        $row.addClass('verified-row');
                        $row.attr('data-has-verified', 'true');

                        // Replace the score pill with "Verified" text
                        const $cell = $row.find('.match-cell:has(.verify-link[data-item-id="' + updatedItem.custrecord_spl_bsi_item_match + '"])');
                        $cell.find('.score-pill').text('Verified').addClass('verified-pill');
                        $cell.removeClass('unselected-match unselected-pill');

                        // Apply unselected-match class to all other match cells in this row
                        $row.find('.match-cell').not($cell).addClass('unselected-match')
                            .find('.score-pill').addClass('unselected-pill');

                        $row.removeClass('updating-row');
                    } else if (action === 'skip') {
                        // Skip operation
                        $row.addClass('verified-row');
                        $row.attr('data-has-verified', 'true');

                        // Update the action links visibility
                        $row.find('.override-link').hide();
                        $row.find('.skip-link').hide();
                        $row.find('.undo-override-link').show();

                        // Clear all match cells first
                        $row.find('.match-cell').empty();
                        
                        // Remove any existing unselected-match classes from all cells
                        $row.find('.match-cell').removeClass('unselected-match');

                        // Add the "Skipped" indicator to the first match cell only
                        const firstMatchCell = $row.find('.match-cell').eq(0);
                        firstMatchCell.html(
                            '<div class="match-details">' +
                                '<div class="item-info">' +
                                    '<div style="display: flex; justify-content: space-between; align-items: flex-start; width: 100%; overflow: hidden;">' +
                                        '<div style="flex: 1; min-width: 0; padding-right: 10px;">' +
                                            '<div style="font-size: 14px; font-weight: bold;"></div>' +
                                            '<div style="font-size: 12px;"></div>' +
                                            '<div style="font-size: 13px;"></div>' +
                                        '</div>' +
                                        '<div style="flex-shrink: 0;">' +
                                            '<span class="score-pill verified-pill">Skipped</span>' +
                                        '</div>' +
                                    '</div>' +
                                '</div>' +
                            '</div>'
                        );
                        
                        // Don't add unselected-match class to any cells - all should be highlighted
                        
                        $row.removeClass('updating-row');
                    }
                    
                    // Check if the "Show unverified only" toggle is active
                    if (jQuery('#show-unverified-only').prop('checked') && 
                        (action === 'verify' || action === 'override' || action === 'skip')) {
                            // Get current Y scroll position
                            const scrollPos = jQuery('#body.ns-child-component').scrollTop();
                            table.draw(false);
                            // Restore scroll position
                            jQuery('#body.ns-child-component').scrollTop(scrollPos);
                    }
                }
            `;
        }

        /**
         * Generate JavaScript for modal logic
         * @param {string} scriptURL - The URL of the script
         * @returns {string} - JavaScript string
         */
        jsStringModalLogic(scriptURL) {
            return /*js*/`
                // Handle Override modal interactions
                jQuery(document).on('click', '.override-link', function() {
                    const bidItemId = jQuery(this).data('bid-item-id');
                    const bidSheetId = jQuery(this).data('bid-sheet-id');
                    const customerItemDescription = jQuery(this).data('customer-description');

                    // Set values in the modal
                    jQuery('#overrideModal').data('bid-item-id', bidItemId);
                    jQuery('#overrideModal').data('bid-sheet-id', bidSheetId);

                    // Set the customer item description
                    jQuery('#customerItemDescription').text(customerItemDescription || 'No description available');

                    // Clear previous search and selection
                    jQuery('#itemSearchInput').val('');
                    jQuery('#searchResults').hide();
                    jQuery('#selectedItemDetails').hide();
                    jQuery('#confirmOverrideBtn').prop('disabled', true);

                    // Show the modal
                    jQuery('#overrideModal').modal('show');
                });

                // Handle item search input with optimized client-side filtering
                jQuery('#itemSearchInput').on('input', function() {
                    const searchTerm = jQuery(this).val().trim().toLowerCase();
                    if (searchTerm.length >= 2) {
                        // Filter items from cache
                        clearTimeout(window.searchTimeout);
                        window.searchTimeout = setTimeout(function() {

                            const filteredItems = allItemsCache
                                .filter(item => item.searchText.includes(searchTerm))
                                .slice(0, 50);

                            let resultsHtml = '';

                            if (filteredItems.length > 0) {
                                filteredItems.forEach(item => {
                                    resultsHtml +=
                                    '<div class="search-result-item p-2 border-bottom" ' +
                                            'data-item-id="' + item.id + '" ' +
                                            'data-item-name="' + item.itemId + '"' +
                                            'data-item-display-name="' + (item.displayName || '') + '"' +
                                            'data-item-vendor="' + (item.vendorCode || '') + '"' +
                                            'data-item-category="' + (item.category || '') + '">' +
                                        '<div class="d-flex justify-content-between">' +
                                            '<strong>' + item.itemId + '</strong>' +
                                            '<span class="badge badge-secondary">' + (item.category || 'Uncategorized') + '</span>' +
                                        '</div>' +
                                        '<div class="small text-muted">' +
                                            (item.displayName || '') + ' ' + (item.vendorCode ? '| Vendor: ' + item.vendorCode : '') +
                                        '</div>' +
                                    '</div>';
                                });
                            } else {
                                resultsHtml = '<div class="p-3 text-center">No items found. Try different search terms.</div>';
                            }

                            jQuery('#searchResults').html(resultsHtml);
                            jQuery('#searchResults').show();
                        }, 200);
                    } else {
                        jQuery('#searchResults').hide();
                    }
                });

                // Handle search result selection
                jQuery(document).on('click', '.search-result-item', function() {
                    const itemId = jQuery(this).data('item-id');
                    const itemName = jQuery(this).data('item-name');
                    const displayName = jQuery(this).data('item-display-name');
                    const vendorCode = jQuery(this).data('item-vendor');
                    const category = jQuery(this).data('item-category');

                    // Display selected item
                    jQuery('#selectedItemName').text(itemName);
                    jQuery('#selectedItemId').text('Item ID: ' + itemId);
                    jQuery('#selectedItemDetails').html(
                        '<strong>Display Name:</strong> ' + (displayName || 'N/A') + '<br>' +
                            '<strong>Vendor Code:</strong> ' + (vendorCode || 'N/A') + '<br>' +
                            '<strong>Category:</strong> ' + (category || 'N/A')
                    );
                    jQuery('#selectedItemDetails').show();

                    // Store the selected item ID in the modal
                    jQuery('#overrideModal').data('selected-item-id', itemId);

                    // Enable confirm override button
                    jQuery('#confirmOverrideBtn').prop('disabled', false);

                    // Hide search results
                    jQuery('#searchResults').hide();
                });

                // Handle confirm override button
                jQuery('#confirmOverrideBtn').on('click', function() {
                    const bidItemId = jQuery('#overrideModal').data('bid-item-id');
                    const bidSheetId = jQuery('#overrideModal').data('bid-sheet-id');
                    const itemId = jQuery('#overrideModal').data('selected-item-id');

                    if (itemId && bidItemId) {
                        let paramsJsonObj = JSON.stringify({
                            action: 'override',
                            itemId,
                            bidItemId,
                            bidSheetId
                        });

                        var requestPayload = {
                            "function": "saveBidSheetItem",
                            "paramsJsonObj": paramsJsonObj,
                        }

                        // Close the modal
                        jQuery('#overrideModal').modal('hide');

                        // Show loading indicator for the specific row
                        const rowElement = jQuery('a.override-link[data-bid-item-id="' + bidItemId + '"]').closest('tr');
                        rowElement.addClass('updating-row');

                        var xhr = new XMLHttpRequest();
                        xhr.open('POST', '${scriptURL}', true);
                        xhr.setRequestHeader('Accept', 'application/json');
                        xhr.send(JSON.stringify(requestPayload));
                        xhr.onload = function() {
                            if (xhr.status === 200) {
                                try {
                                    const response = JSON.parse(xhr.response);

                                    if(response.error === undefined && response.success) {
                                        updateSingleRow(response.updatedItem, 'override');
                                    } else {
                                        alert('Error: ' + (response.error ? response.error.message : 'Unknown error'));
                                    }
                                } catch (err) {
                                    alert('Unable to parse the response.');
                                } finally {
                                    // Remove loading indicator
                                    rowElement.removeClass('updating-row');
                                    rowElement.find('.loading-spinner-cell').remove();
                                }
                            } else {
                                alert("Error: " + xhr.status);
                                rowElement.removeClass('updating-row');
                            }
                        }
                    }
                });
            `;
        }
    }

    // Export the class
    exports.BidSheetReviewClientScripts = BidSheetReviewClientScripts;

    return BidSheetReviewClientScripts;
});
