/**
 * @description Return a Bridge RIP Vendor instance
 *
 * @NApiVersion 2.1
 * <AUTHOR>
 */

define([
  "exports",
  "require"
], function (/** @type {any} */ exports, /** @type {any} */ require) {
  /**
   * Bridge RIP Vendor Factory Class
   *
   * @typedef {import("../Models/Vendor/brdg_rip_vendor").BridgeRIPVendor} BridgeRIPVendor
   */
  class BridgeRIPVendorFactory {
    constructor(){}

    /**
     * Create a vendor instance based on the NetSuite internal ID
     *
     * @param {string} vendorId Vendor Internal ID
     * @param {{[key:string]: any}} props Vendor Constructor params
     * @returns {BridgeRIPVendor} Bridge RIP Vendor
     */
    static createVendor(vendorId, props) {
      let /** @type {BridgeRIPVendor} */ vendorInstance;
      switch (vendorId) {
        case "13694":
          require(["../Models/Vendor/brdg_rip_allied"], function () {
            const { BridgeRIPAllied } = require("../Models/Vendor/brdg_rip_allied");
            vendorInstance = new BridgeRIPAllied(props);
          });
          break;
        case "13423":
          require(["../Models/Vendor/brdg_rip_fedway"], function () {
            const { BridgeRIPFedway } = require("../Models/Vendor/brdg_rip_fedway");
            vendorInstance = new BridgeRIPFedway(props);
          });
          break;
        case "13410":
        case "13366":
          require(["../Models/Vendor/brdg_rip_royal"], function () {
            const { BridgeRIPRoyal } = require("../Models/Vendor/brdg_rip_royal");
            vendorInstance = new BridgeRIPRoyal(props);
          });
          break;
        default:
          break;
      }

      // @ts-ignore Variable 'vendorInstance' is used before being assigned.ts(2454)
      return vendorInstance;
    }
  }

  exports.BridgeRIPVendorFactory = BridgeRIPVendorFactory;
});