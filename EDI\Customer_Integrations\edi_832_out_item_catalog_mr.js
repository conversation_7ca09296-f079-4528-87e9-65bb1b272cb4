/**
 * @description Create the EDI Outgoing File instance and decorate it using the 832 Item Catalog Class
 *
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * @NAmdConfig /SuiteScripts/config.json
 * <AUTHOR> <<EMAIL>>
 */

define([
  "require",
  "N/log",
  "N/runtime",
  "../Classes/Models/Partner/Customer/edi_agora",
  "../Classes/Models/Partner/Customer/edi_dssi",
  "../Classes/Models/Partner/Customer/edi_adelpo",
  "../Classes/Models/File/edi_outgoing",
  "../Classes/Models/Server/edi_server",
  "../Classes/Decorators/832/ItemCatalog/edi_832_item_catalog",
  "../Classes/Decorators/832/ItemCatalog/edi_832_item_catalog_processor",
  "../../Classes/vlmd_mr_summary_handling",
], function (/** @type {any} */ require) {
  const log = require("N/log");
  const runtime = require("N/runtime");
  const { EDIOutgoing } = require("../Classes/Models/File/edi_outgoing");
  const { EDIServer } = require("../Classes/Models/Server/edi_server");
  const {
    EDIItemCatalog,
  } = require("../Classes/Decorators/832/ItemCatalog/edi_832_item_catalog");
  const {
    EDIItemCatalogProcessor,
  } = require("../Classes/Decorators/832/ItemCatalog/edi_832_item_catalog_processor");
  const StageHandling = require("../../Classes/vlmd_mr_summary_handling");

  /**
   * @typedef {import("../Classes/Interfaces/Models/Partner/edi_partner").EDIPartnerInterface} EDIPartnerInterface
   * @typedef {import("../Classes/Interfaces/Models/Partner/edi_partner").EDIPartnerOverrides} EDIPartnerOverrides
   * @typedef {import("../Classes/Interfaces/Models/File/edi_outgoing").SuiteQLObjectReference} SuiteQLObjectReference
   * @typedef {import("../Classes/Interfaces/Models/File/edi_file").EDIFileInterface} EDIFileInterface
   */

  /**
   * Setup the EDI File and return the query string
   *
   * @returns {SuiteQLObjectReference|undefined} Query object
   */
  function getInputData() {
    const ediFile = new EDIOutgoing();

    try {
      const currentScript = runtime.getCurrentScript();
      const purchasingSoftwareId = currentScript.getParameter({
        name: "custscript_832_out_software",
      });

      log.audit(
        "Get Input Data: Parameters",
        JSON.stringify({
          purchasingSoftwareId,
        })
      );

      if (!purchasingSoftwareId) {
        throw ediFile.customError.updateError({
          errorType: ediFile.customError.ErrorTypes.MISSING_PARAM,
          summary: "MISSING_PURCHASING_SOFTWARE",
          details:
            "No purchasing software id param was passed in for this deployment.",
        });
      }

      const ediItemCatalogDecorator = new EDIItemCatalog({
        processor: new EDIItemCatalogProcessor({
          customError: ediFile.customError,
          partner: resolvePartner(ediFile, purchasingSoftwareId.toString()),
        }),
        type: "Item Catalog",
        typeId: 8,
      });
      ediItemCatalogDecorator.decorate(ediFile);

      /** @type {SuiteQLObjectReference} */
      const queryObject = ediFile.load();

      log.audit("Query String", queryObject.query);

      return queryObject;
    } catch (err) {
      ediFile.customError.throwError({
        summaryText: "GET_INPUT_DATA_ERROR",
        error: err,
        recordId: null,
        recordName: null,
        recordType: null,
        errorWillBeGrouped: false,
      });
    }
  }

  /**
   * Run the Item Catalog query and generate the EDI File string for each result
   *
   * @param {import("N/types").EntryPoints.MapReduce.mapContext} context Map context
   * @returns {void}
   */
  function map(context) {
    const ediFile = new EDIOutgoing();
    const currentScript = runtime.getCurrentScript();

    const purchasingSoftwareId = currentScript.getParameter({
      name: "custscript_832_out_software",
    });

    const ediItemCatalogDecorator = new EDIItemCatalog({
      processor: new EDIItemCatalogProcessor({
        customError: ediFile.customError,
        partner: resolvePartner(ediFile, purchasingSoftwareId.toString()),
      }),
      type: "Item Catalog",
      typeId: 8,
    });

    ediItemCatalogDecorator.decorate(ediFile);

    ediFile.resetCustomError();

    /** @type {{[key:string]: any}} */
    let queryResultsRowObj = {};

    try {
      const parsedItemRow = JSON.parse(context.value).values;

      //These values are dependant on the order of the results in the query string in the getItemsQueryString function.
      const baseFields = {
        internalId: parsedItemRow[0],
        itemNumber: parsedItemRow[1],
        description: parsedItemRow[2],
        category: parsedItemRow[3],
        itemType: parsedItemRow[4],
        imageUrl: parsedItemRow[5],
        parentUnitIsEach: parsedItemRow[6],
        unitAbbreviation: parsedItemRow[7],
        vendorName: parsedItemRow[8],
        vendorItemName: parsedItemRow[9],
        deactivate: parsedItemRow[10],
      };

      queryResultsRowObj = {
        ...baseFields,
        ...(parsedItemRow[11] && { isReplacementFor: parsedItemRow[11] }),
      };

      const itemAsString = ediFile.process({ queryResultsRowObj });

      context.write(ediFile.type, itemAsString);

      if (ediFile.customError.summary) {
        throw `Soft Error`;
      }
    } catch (err) {
      ediFile.customError.throwError({
        summaryText: "MAP_ERROR",
        error: err,
        recordId: queryResultsRowObj.internalId ?? "",
        recordName: queryResultsRowObj.itemNumber ?? "",
        recordType: "ITEM",
        errorWillBeGrouped: true,
      });
    }
  }

  /**
   * Build the EDI File rows into a single string
   *
   * @param {import("N/types").EntryPoints.MapReduce.reduceContext} context Reduce context
   * @returns {void}
   */
  function reduce(context) {
    const ediFile = new EDIOutgoing();
    try {
      const ediItemCatalogDecorator = new EDIItemCatalog({
        processor: new EDIItemCatalogProcessor({
          customError: ediFile.customError,
          partner: null,
        }),
        type: "Item Catalog",
        typeId: 8,
      });
      ediItemCatalogDecorator.decorate(ediFile);

      const fileContent = context.values.reduce((body, itemAsString) => {
        return body + "\n" + itemAsString;
      }, "");

      context.write(ediFile.type, fileContent);

      log.audit("Items Added to Catalog", context.values.length);
    } catch (err) {
      ediFile.customError.throwError({
        summaryText: "REDUCE_ERROR",
        error: err,
        recordId: null,
        recordName: null,
        recordType: null,
        errorWillBeGrouped: false,
      });
    }
  }

  /**
   * Email, upload or move the file created from the object
   * Expect booleans passed as strings if the MR is executed through task.submit
   *
   * @param {import("N/types").EntryPoints.MapReduce.summarizeContext} context Summarize context
   * @returns {void}
   */
  function summarize(context) {
    const ediFile = new EDIOutgoing();
    try {
      const currentScript = runtime.getCurrentScript();
      const purchasingSoftwareId = currentScript.getParameter({
        name: "custscript_832_out_software",
      });
      // @ts-ignore type of expected values and getParameter return type don't match
      const isEmailingFile =
        ["T", "true", true].indexOf(
          // @ts-ignore Type 'number' is not assignable to type 'string | boolean'.ts(2345)
          currentScript.getParameter({
            name: "custscript_832_out_is_emailing",
          })
        ) !== -1;
      // @ts-ignore type of expected values and getParameter return type don't match
      const isSubmittingFileOnServer =
        ["T", "true", true].indexOf(
          // @ts-ignore Type 'number' is not assignable to type 'string | boolean'.ts(2345)
          currentScript.getParameter({
            name: "custscript_832_out_is_submitting",
          })
        ) !== -1;
      // @ts-ignore type of expected values and getParameter return type don't match
      const isUsingProd =
        ["T", "true", true].indexOf(
          // @ts-ignore Type 'number' is not assignable to type 'string | boolean'.ts(2345)
          currentScript.getParameter({
            name: "custscript_832_out_is_prod",
          })
        ) !== -1;

      log.audit(
        "Summarize: Parameters",
        JSON.stringify({
          purchasingSoftwareId,
          isEmailingFile,
          isSubmittingFileOnServer,
          isUsingProd,
        })
      );

      if (!purchasingSoftwareId) {
        throw ediFile.customError.updateError({
          errorType: ediFile.customError.ErrorTypes.MISSING_PARAM,
          summary: "MISSING_PURCHASING_SOFTWARE",
          details:
            "No purchasing software id param was passed in for this deployment.",
        });
      }

      let fileContent = "";

      // There should only be one key, which is "Item Catalog"
      // Iterator will run only once
      context.output
        .iterator()
        .each((/** @type {string} */ key, /** @type {string} */ value) => {
          fileContent += value;
          return true;
        });

      const isProdEnv =
        isUsingProd && runtime.envType === runtime.EnvType.PRODUCTION;
      const partner = resolvePartner(ediFile, purchasingSoftwareId.toString());
      const partnerOverrides = resolvePartnerOverrides(
        purchasingSoftwareId.toString()
      );
      log.debug("Is Prod?", isProdEnv);
      log.debug("Connection Overrides", JSON.stringify(partnerOverrides));

      const ediItemCatalogDecorator = new EDIItemCatalog({
        processor: new EDIItemCatalogProcessor({
          customError: ediFile.customError,
          partner,
        }),
        type: "Item Catalog",
        typeId: 8,
      });
      ediItemCatalogDecorator.decorate(ediFile);

      ediFile.create({ fileContent });

      ediFile.server = new EDIServer({
        prodGUID: "263d80ce526e471d85fefa3182ef5458",
        partner,
        customError: ediFile.customError,
      });
      log.debug("Server", JSON.stringify({ server: ediFile.server, partner }));

      // Upload to Partners folder
      if (isSubmittingFileOnServer) {
        ediFile.server.connect({
          target: isProdEnv ? "PROD" : "SANDBOX",
        });
        ediFile.upload();

        // Connect to upload to secondary SFTP directory
        ediFile.server.connect({
          directory: partnerOverrides?.prodDirectory,
          username: partnerOverrides?.username,
          passwordGuid: partnerOverrides?.passwordGuid,
        });
        ediFile.upload();
      }

      // Upload to Reference Folder
      ediFile.server.connect({
        target: "REF",
      });
      ediFile.upload();

      ediFile.server.connect({
        directory: partnerOverrides?.referenceDirectory,
        username: partnerOverrides?.referenceUsername,
        passwordGuid: partnerOverrides?.referencePasswordGuid,
      });
      ediFile.upload();

      if (isEmailingFile) {
        ediFile.save();
        ediFile.email();
      }

      // @ts-ignore Cannot remove return statement of Stage Handling yet
      const stageHandling = new StageHandling(context);

      stageHandling.printErrors({
        groupErrors: true,
      });

      stageHandling.printScriptProcessingSummary();
    } catch (/** @type {any} */ err) {
      ediFile.customError.throwError({
        summaryText: "SUMMARIZE_ERROR",
        error: err,
        recordId: null,
        recordName: null,
        recordType: null,
        errorWillBeGrouped: false,
      });
    }
  }

  /**
   * Identify the correct Partner based on the ID
   *
   * @param {EDIFileInterface} ediFile EDI File of type Outgoing
   * @param {string} id Partner ID
   * @returns {EDIPartnerInterface} Partner instance
   * @throws {Error} For unsupported purchasing software
   */
  const resolvePartner = (ediFile, id) => {
    switch (id) {
      case "2":
        const {
          EDIDSSI,
        } = require("../Classes/Models/Partner/Customer/edi_dssi");
        return new EDIDSSI();
      case "5":
        const {
          EDIAdelpo,
        } = require("../Classes/Models/Partner/Customer/edi_adelpo");
        return new EDIAdelpo();
      case "6":
        const {
          EDIAgora,
        } = require("../Classes/Models/Partner/Customer/edi_agora");
        return new EDIAgora();
      default:
        log.error({
          title: "UNSUPPORTED_PURCHASING_SOFTWARE_ID",
          details: `The selected purchasing software '${id}' is not supported by this script.`
        });
        throw ediFile.customError?.updateError({
          errorType: ediFile.customError.ErrorTypes.INVALID_DATA,
          summary: "UNSUPPORTED_PURCHASING_SOFTWARE_ID",
          details: `The selected purchasing software '${id}' is not supported by this script.`,
        });
    }
  };

  /**
   * Retrieve the directory and password overrides while we upload to 2 different sets of directories
   * Note: This is a temporary function. We need to remove this once we switch to uploading
   *  to the new EDI location
   *
   * @param {string} id Partner ID
   * @returns {EDIPartnerOverrides | undefined} Partner overrides for Item Catalog Outgoing MR
   */
  const resolvePartnerOverrides = (id) => {
    const referenceDirectory = "/EDI Reference Files/OUT/832";
    const referenceUsername = "DHSftp";
    const referencePasswordGuid = "6c922dc18d3f45c8ab323ff5ea8cd175";

    switch (id) {
      case "2":
        return {
          prodDirectory: "/users/DSSI/OUT/832",
          testDirectory: "/users/DSSI/Test",
          referenceDirectory,
          username: "dssi",
          referenceUsername,
          passwordGuid: "0e2a03dceeb24321b0b03e8649cb3190",
          referencePasswordGuid,
        };
      case "5":
        return {
          prodDirectory: "/users/Adelpo/OUT/832",
          testDirectory: "/users/Adelpo/Test",
          referenceDirectory,
          username: "Adelpo",
          referenceUsername,
          passwordGuid: "52501b93115646eea9614c1ebc02c770",
          referencePasswordGuid,
        };
      case "6":
        return {
          prodDirectory: "/users/Agora/OUT/832",
          testDirectory: "/users/Agora/Test",
          referenceDirectory,
          username: "agora",
          referenceUsername,
          passwordGuid: "fada361e3e484397841f9dd6170f5022",
          referencePasswordGuid,
        };
    }
  };

  return {
    getInputData,
    map,
    reduce,
    summarize,
  };
});