/**
 * @description Sets the payment method on vendor transactions if it is not already set.
 * First pulls from the vendor account number, if empty pulls from the vendor
 *
 * </br><b>Deployed On:</b> Vendor bills, vendor credits
 * </br><b>Execution Context:</b> ALL
 * </br><b>Event Type/Mode:</b> CREATE
 * </br><b>Entry Points:</b> beforeSubmit
 *
 * @NApiVersion 2.1
 * @NScriptType UserEventScript
 *
 * <AUTHOR>
 * @module vlmd_set_payment_method_on_vendor_transactions_ue
 */

define(["N/log", "N/search"], function (log, search) {
  /**
   * @description On transactions create, sets payment method from vendor account or vendor record
   * @param {Object} context - The context object
   * @param {Record} context.newRecord - New form record
   * @param {string} context.type - The trigger type
   * @param {Object} context.UserEventType - User event type constants
   * @returns {void}
   */
  function beforeSubmit(context) {
    if (context.type !== context.UserEventType.CREATE) return;
    const vendorTransaction = context.newRecord;

    // Check if payment method is already set
    let paymentMethod = vendorTransaction.getValue(
      "custbody_rsm_payment_method"
    );
    log.debug({
      title: "Payment Method",
      details: paymentMethod,
    });
    if (paymentMethod) return;

    // If not, try to get payment method from vendor account number first
    const vendorAccountNumber = vendorTransaction.getValue(
      "custbody_vendor_account_number"
    );
    log.debug({
      title: "Vendor Account Number",
      details: vendorAccountNumber,
    });

    if (vendorAccountNumber) {
      paymentMethod = search.lookupFields({
        type: "customrecord_rsm_vendor_account_number",
        id: vendorAccountNumber,
        columns: "custrecord_rsm_payment_method",
      })?.["custrecord_rsm_payment_method"]?.[0]?.value;
      log.debug({
        title: "Payment Method from Vendor Account",
        details: paymentMethod,
      });
    }

    // If not found, try to get from vendor record
    if (!paymentMethod) {
      const vendorId = vendorTransaction.getValue("entity");
      log.debug({
        title: "Vendor ID",
        details: vendorId,
      });
      paymentMethod = search.lookupFields({
        type: "vendor",
        id: vendorId,
        columns: "custentity_rsm_payment_method",
      })?.["custentity_rsm_payment_method"]?.[0]?.value;
      log.debug({
        title: "Payment Method from Vendor Record",
        details: paymentMethod,
      });
    }

    // Set the payment method, log error if none was found
    if (paymentMethod) {
      log.debug({
        title: "Setting Payment Method",
        details: paymentMethod,
      });
      vendorTransaction.setValue({
        fieldId: "custbody_rsm_payment_method",
        value: paymentMethod,
      });
    } else {
      const transactionNumber = vendorTransaction.getValue("transactionnumber");
      const vendorAccountNumberName = vendorTransaction.getText(
        "custbody_vendor_account_number"
      );
      const vendorName = vendorTransaction.getText("entity");

      throw new Error(
        `Missing Payment Method - ${transactionNumber}. No payment method found for vendor account # ${vendorAccountNumberName} or vendor ${vendorName}`
      );
    }
  }

  return {
    beforeSubmit: beforeSubmit,
  };
});
