/**
 * @description MR Script that matches products from CSV to SPL items.
 *
 * Schedule: on-demand
 *
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 *
 * <AUTHOR>
 * @module spl_bid_sheet_processing_mr
 */
define(["require",
    "N/record",
    "N/file",
    "N/runtime",
    "N/query",
    "N/search",
    "N/email",
    "N/url",
    "./Classes/Text_Matching/spl_bid_sheet_text_matching_class",
    "./Classes/Text_Matching/spl_bid_sheet_item_repository_class",
    "./Classes/Text_Matching/spl_bid_sheet_word_frequency_class",
    "../../Classes/vlmd_mr_summary_handling",
    "../../Classes/vlmd_custom_error_object",
], (require) => {

    const record = require('N/record');
    const file = require('N/file');
    const runtime = require('N/runtime');
    const query = require('N/query');
    const search = require('N/search');
    const email = require('N/email');
    const url = require('N/url');

    const TextMatching = require('./Classes/Text_Matching/spl_bid_sheet_text_matching_class');
    const ItemRepository = require('./Classes/Text_Matching/spl_bid_sheet_item_repository_class');
    const WordFrequencyAnalyzer = require('./Classes/Text_Matching/spl_bid_sheet_word_frequency_class');

    const CustomErrorObject = require("../../Classes/vlmd_custom_error_object");
    const customErrorObject = new CustomErrorObject();

    function getInputData(context) {
        const bidSheetFileId = runtime.getCurrentScript().getParameter({
            name: 'custscript_spl_bid_sheet_file_id'
        });

        const customerId = runtime.getCurrentScript().getParameter({
            name: 'custscript_spl_bid_sheets_customer'
        });

        let binSheetRecord = record.create({
            type: 'customrecord_spl_bid_sheets'
        });

        binSheetRecord.setValue({
            fieldId: 'custrecord_spl_bid_sheets_customer',
            value: customerId
        });

        binSheetRecord.setValue({
            fieldId: 'custrecord_spl_bid_sheets_file',
            value: bidSheetFileId
        });

        const bidSheetRecordId = binSheetRecord.save();

        const bidSheetFile = file.load({
            id: bidSheetFileId
        });

        const rowItems = bidSheetFile.lines.iterator();
        // Skip header row
        rowItems.each(() => false);

        const rows = [];
        let rowNumber = 0;
        rowItems.each((line) => {
            rowNumber++;
            rows.push({
                rowNumber: rowNumber,
                bidSheetId: bidSheetRecordId,
                customerItemDescription: line.value,
            });
            return true;
        });

        return rows;
    }

    function map(context) {
        try {
            const rowData = JSON.parse(context.value);
            let exactMatchFound = false;
            let bidSheetItemRecordId;
            let vendorCodes = [];
            let bestMatches = [];

            // Get all vendor codes BUT exclude 4 digit numbers
            let vendorCodeQuery = /*sql*/`
            SELECT DISTINCT
                item.id,
                item.itemid,
                item.displayname,
                item.vendorname as vendor_code,
                BUILTIN.HIERARCHY(item.class, 'DISPLAY_JOINED') as item_category
            FROM
                item
            WHERE
                item.isinactive = 'F'
                AND BUILTIN.DF(item.subsidiary) LIKE '%Supplyline%'
                AND item.vendorname IS NOT NULL
                AND NOT (LENGTH(item.vendorname) <= 4 AND REGEXP_LIKE(item.vendorname, '^[A-Za-z0-9]+$'))
            `;
            let vendorCodePagedResults = query.runSuiteQLPaged({
                query: vendorCodeQuery,
                pageSize: 1000
            });

            const textMatching = new TextMatching();
            let tokens = textMatching.generateTokens({stringToTokenize: rowData.customerItemDescription});

            // Create a Set of tokens for O(1) lookups
            const tokenSet = new Set(tokens);

            // Process all pages until we find matches or exhaust all pages
            for (let i = 0; i < vendorCodePagedResults.pageRanges.length && (!exactMatchFound || bestMatches.length < 2); i++) {
                const currentPage = vendorCodePagedResults.fetch(i);
                const currentPagedData = currentPage.data.asMappedResults();

                // Store all items for later use if needed
                vendorCodes.push(...currentPagedData);

                // Find matches in current page (single pass)
                for (const item of currentPagedData) {
                    const vendorCode = item.vendor_code?.toLowerCase();
                    const itemId = item.itemid?.toLowerCase();

                    if (tokenSet.has(vendorCode) || tokenSet.has(itemId)) {
                        bestMatches.push({
                            itemInternalId: item.id,
                            customerItemDescription: rowData.customerItemDescription,
                            displayName: item.displayname,
                            itemCategory: item.item_category,
                            vendorCode: item.vendor_code,
                            itemId: item.itemid,
                            combinedDescription: '',
                            score: 100
                        });
                        exactMatchFound = true;

                        // Exit early once we have enough matches
                        if (bestMatches.length >= 2) break;
                    }
                }
            }

            if (exactMatchFound) {
                bidSheetItemRecordId = saveRowRecord(rowData.customerItemDescription, bestMatches, rowData.bidSheetId);
            }

            if(exactMatchFound) {
                context.write({
                    key: rowData.bidSheetId,
                    value: {
                        matches: bestMatches,
                        rowData: rowData
                    }
                });

                return;
            }

            let wordFrequency = new WordFrequencyAnalyzer({
                textProcessor: textMatching,
                fileModule: file
            });

            // Load indexed items from file
            let itemRepository = new ItemRepository({
                queryModule: query,
                fileModule: file
            });

            const WORD_FREQUENCY_FILE_ID = runtime.getCurrentScript().getParameter({ name: 'custscript_spl_bdsh_wrd_frqnc_fle_id' });
            const ITEM_INDEX_FILE_ID = runtime.getCurrentScript().getParameter({ name: 'custscript_spl_bdsh_itms_indx_fle_id' });
            wordFrequency.loadWordFrequencyMapFromFile(WORD_FREQUENCY_FILE_ID);
            itemRepository.loadItemIndexFromFile(ITEM_INDEX_FILE_ID);

            // Extract keywords from customer description
            // MaxKeywords = -1 to get all keywords
            // Min Doc Frequency = 1, as long as a word appears in at least one item, it should be considered
            const customerDescriptionKeywords = wordFrequency.extractKeywords(rowData.customerItemDescription, {
                maxKeywords: -1,
                minDocFrequency: 1,
                sortByCount: true
            });

            // Find matching items using extracted keywords
            // Right now, set to 0.1 to get majority of matches
            const matchingItems = itemRepository.searchItems(customerDescriptionKeywords.map(keywordDetail => keywordDetail.keyword), 0.1);

            // Now, use textMatcher to get the score and details for each item using calculateDistance
            const validMatchingItems = [];

            for (let i = 0; i < matchingItems.length; i++) {
                const matchResult = matchingItems[i];
                const result = textMatching.calculateDistance({
                    stringOne: rowData.customerItemDescription,
                    stringTwo: `${matchResult.itemId} ${matchResult.displayName} ${matchResult.vendorCode} ${matchResult.category}`.trim(),
                    threshold: 15
                });

                if (result) {
                    matchResult.score = result.score;
                    matchResult.matchDetails = result.details;
                    validMatchingItems.push(matchResult);
                }
            }

            // Sort by score and keep only top 3 matches
            bestMatches = validMatchingItems
                .sort((a, b) => b.score - a.score)
                .slice(0, 3);

            bidSheetItemRecordId = saveRowRecord(rowData.customerItemDescription, bestMatches, rowData.bidSheetId);
            context.write({
                key: rowData.bidSheetId,
                value: {
                    matches: bestMatches,
                    rowData: rowData
                }
            });

            return;
        } catch (err) {
            customErrorObject.throwError({summaryText: 'ERROR_PROCESSING_BID_SHEET', error: err});
        }
    }

    function reduce(context) {
        try {
            const bidSheetRecordId = context.key;
            const bidSheetRecord = record.load({
                type: 'customrecord_spl_bid_sheets',
                id: bidSheetRecordId,
                isDynamic: false
            });

            const processedBidSheetFolderId = runtime.getCurrentScript().getParameter({name: 'custscript_spl_bid_sheets_prcssd_fldr_id'});
            const customerId = bidSheetRecord.getValue({ fieldId: 'custrecord_spl_bid_sheets_customer' });
            const customerName = bidSheetRecord.getText({ fieldId: 'custrecord_spl_bid_sheets_customer' });
            const employeeId = bidSheetRecord.getValue({ fieldId: 'lastmodifiedby' });
            const employeeEmail = search.lookupFields({
                type: search.Type.EMPLOYEE,
                id: employeeId,
                columns: ['email']
            }).email;
            const originalFileId = bidSheetRecord.getValue('custrecord_spl_bid_sheets_file');
            const originalFile = file.load({ id: originalFileId });
            const headers = originalFile.lines.iterator().next().value;
            const matchHeaders = [
                'Match 1 Item ID',
                'Match 1 Vendor Code',
                'Match 1 Item Name',
                'Match 1 Item Price',
                'Match 1 Matching Score',
                'Match 2 Item ID',
                'Match 2 Vendor Code',
                'Match 2 Item Name',
                'Match 2 Item Price',
                'Match 2 Matching Score',
                'Match 3 Item ID',
                'Match 3 Vendor Code',
                'Match 3 Item Name',
                'Match 3 Item Price',
                'Match 3 Matching Score'
            ].join(',');

            const processedBidSheetFile = file.create({
                name: `Processed_${originalFile.name}`,
                fileType: file.Type.CSV,
                contents: `${headers},${matchHeaders}\n`
            });
            processedBidSheetFile.folder = processedBidSheetFolderId;

            let itemIds = context.values
                .flatMap(value => JSON.parse(value).matches)
                .map(match => match.itemInternalId);

            let customerItemPrices = getCustomerItemPrice(customerId, itemIds);

            let customerItemPriceMap = new Map(
                customerItemPrices
                    .map(price => [price.item_internal_id, price])
            );

            // Sort the values by rowNumber
            const sortedValues = context.values
                .map(value => JSON.parse(value))
                .sort((a, b) => a.rowData.rowNumber - b.rowData.rowNumber);

            for (let parsedValue of sortedValues) {
                const rowData = parsedValue.rowData;
                const matches = parsedValue.matches;
                let newRow = [rowData.customerItemDescription];

                for (const match of matches) {
                    newRow.push(sanitizeForCSV(match?.itemId));
                    newRow.push(sanitizeForCSV(match?.vendorCode));
                    newRow.push(sanitizeForCSV(match?.displayName));
                    newRow.push(sanitizeForCSV(customerItemPriceMap.get(match?.itemInternalId)?.rate));
                    newRow.push(sanitizeForCSV(`${match?.score}%`));
                }
                processedBidSheetFile.appendLine({ value: newRow.join(',') });
            }

            const processedBidSheetFileId = processedBidSheetFile.save();

            bidSheetRecord.setValue({
                fieldId: 'custrecord_spl_bid_sheets_finished_file',
                value: processedBidSheetFileId
            });

            bidSheetRecord.save();

            let reviewSuiteletPath = url.resolveScript({
                scriptId: 'customscript_spl_bid_sheet_review_sl',
                deploymentId: 'customdeploy_spl_bid_sheet_review_sl'
            });

            let emailBody = `
                    <p>Thank you for using the bid sheet tool! Your bid sheet has been processed and is ready for review.</p>
                    <p>Please click the link below to access the review interface:</p>

                    <p><a href="${reviewSuiteletPath}&bidsheetid=${bidSheetRecordId}" style="padding: 10px 15px; background-color: #0066cc; color: white; text-decoration: none; border-radius: 4px; display: inline-block; margin: 10px 0;">Review Bid Sheet</a></p>

                    <p>In the review interface, you can:</p>
                    <ul style="margin-left: 20px;">
                        <li>View the original bid sheet items</li>
                        <li>
                            See our top 3 product matches for each item, with:
                            <ul style="margin-left: 20px;">
                                <li>Item ID</li>
                                <li>Vendor Code</li>
                                <li>Item Name</li>
                                <li>Price</li>
                                <li>Match Score (1-100%; higher is better)</li>
                            </ul>
                        </li>
                        <li>Verify or override matches</li>
                        <li>Download the final CSV when you're done</li>
                    </ul>
                    <p><strong>IMPORTANT: </strong> This feature is still under development. Please review the matches carefully.</p>
                `;

            email.send({
                author: employeeId,
                recipients: employeeEmail,
                body: emailBody,
                subject: `${customerName} Bid Sheet Ready for Review`,
            });

            context.write({
                key: bidSheetRecordId,
                value: processedBidSheetFileId
            });
        } catch (err) {
            customErrorObject.throwError({summaryText: 'ERROR_PROCESSING_BID_SHEET', error: err});
        }
    }

    function summarize(context) {
        const StageHandling = require("../../Classes/vlmd_mr_summary_handling");
		const stageHandling = new StageHandling(context);

		stageHandling.printScriptProcessingSummary();

		stageHandling.printRecordsProcessed({
			includeLineBreak: true,
			includeKey: true
		});

		stageHandling.printErrors({
			groupErrors: true,
		});
    }

    function saveRowRecord(customerItemDescription, bestMatches, bidSheetRecordId) {

        let bidSheetItemRecord = record.create({
            type: 'customrecord_spl_bid_sheet_items',
            isDynamic: false
        });

        bidSheetItemRecord.setValue({
            fieldId: 'custrecord_spl_bsi_bid_sheet',
            value: bidSheetRecordId
        });

        bidSheetItemRecord.setValue({
            fieldId: 'custrecord_spl_bsi_row_data',
            value: customerItemDescription
        });

        bidSheetItemRecord.setValue({
            fieldId: 'custrecord_spl_bsi_potential_matches',
            value: JSON.stringify(bestMatches)
        });

        return bidSheetItemRecord.save();
    }

    function getCustomerItemPrice(customerId, itemIds) {

        let purchaserQuery = /*SQL */ `
            SELECT
                integration.custrecord_spl_prchsng_fclty purchaser_id,
                parent.id parent_id,
                customer.id customer_id,
            FROM
                customer
                LEFT JOIN customer parent ON customer.parent = parent.id
                LEFT JOIN customrecord_vlmd_edi_integration integration ON (
                    parent.custentity_spl_edi_integration_record = integration.id
                    OR customer.custentity_spl_edi_integration_record = integration.id
                )
            WHERE
                customer.id = ${customerId}`;

        let purchaserResult = query.runSuiteQL({
            query: purchaserQuery,
        }).asMappedResults()?.[0];

        let purchaserId =
            purchaserResult["purchaser_id"] ??
            purchaserResult["parent_id"] ??
            purchaserResult["customer_id"];

        if(!purchaserResult) customErrorObject.throwError({summaryText: 'ERROR_PROCESSING_BID_SHEET', error: 'No purchaser id found.'});

        let itemPricingQuery = /*SQL*/ `
                SELECT
                    ip.item as item_internal_id,
                    ip.price rate,
                    IP.level price_level_id,
                    BUILTIN.DF(IP.level) price_level_name,
                    i.pricinggroup price_group_id,
                    BUILTin.DF(pricinggroup) price_group_name,
                FROM
                    customerItemPricing AS IP
                    JOIN
                    item i
                    ON i.id = ip.item
                WHERE
                    IP.customer = ${purchaserId}
                    AND IP.item IN (\'${itemIds.join(`','`)}\')
                UNION
                SELECT
                    i.id as item_internal_id,
                    p.unitPrice rate,
                    GP.level price_level_id,
                    BUILTIN.DF( GP.level) price_level_name,
                    GP.GROUP price_group_id,
                    BUILTIN.DF( GP.GROUP) price_group_name,
                FROM
                    CustomerGroupPricing AS gp
                    INNER JOIN
                    item AS i
                    ON i.pricinggroup = gp.GROUP
                    LEFT JOIN
                    pricing p
                    ON p.pricelevel = gp.level
                    AND p.item = i.id
                WHERE
                    gp.customer = ${purchaserId}
                    AND i.id IN (\'${itemIds.join(`','`)}\')
				ORDER BY
					price_level_id `;

        let customerItemRates = query.runSuiteQL({
          query: itemPricingQuery,
        }).asMappedResults();

        if(!customerItemRates.length) customErrorObject.throwError({summaryText: 'ERROR_PROCESSING_BID_SHEET', error: 'No customer item rates found.'});

        return customerItemRates;
    }

    function sanitizeForCSV(value) {
        // Convert to string, handle null/undefined
        const str = (value ?? '').toString();
        // Escape quotes by doubling them and wrap in quotes
        return `"${str.replace(/"/g, '""')}"`;
    }

    return {
        getInputData: getInputData,
        map: map,
        reduce: reduce,
        summarize: summarize
    };
});




