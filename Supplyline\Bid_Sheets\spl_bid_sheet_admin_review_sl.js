/**
 * @description Suitelet to review and analyze processed bid sheets
 *
 * @NApiVersion 2.1
 * @NScriptType Suitelet
 *
 * <AUTHOR>
 * @module spl_bid_sheet_admin_review_sl
 */
var scriptURL,
    bidSheetId,
    serverWidget,
    record,
    url,
    query,
    runtime,
    file;

define([
    "require",
    "../../Classes/vlmd_custom_error_object",
    "N/ui/serverWidget",
    "N/record",
    "N/url",
    "N/query",
    "N/runtime",
    "N/file",
], (require) => {

    const CustomError = require("../../Classes/vlmd_custom_error_object");
    const customErrorObject = new CustomError();
    serverWidget = require("N/ui/serverWidget");
    record = require("N/record");
    url = require("N/url");
    query = require("N/query");
    runtime = require("N/runtime");
    file = require("N/file");


    /**
     * Main entry point for the suitelet
     */
    function onRequest(context) {
        try {
            scriptURL = url.resolveScript({
                scriptId: runtime.getCurrentScript().id,
                deploymentId: runtime.getCurrentScript().deploymentId,
                returnExternalURL: false,
            });

            if (context.request.method === 'GET') {
                handleGet(context);
            } else {
                handlePost(context);
            }
        } catch (err) {
            customErrorObject.throwError({
                summaryText: `ERROR_IN_BID_SHEET_REVIEW_SL`,
                error: err,
            });
        }
    }

    /**
     * GET Request Function - Renders the HTML page and handles file download
     */
    function handleGet(context) {
        bidSheetId = context.request.parameters.bidsheetid;

        if(context.request.parameters.hasOwnProperty("function")) {
            if(context.request.parameters["function"] === "downloadFile") {
                generateFile(context, bidSheetId);
            }
        } else {
            renderHTML(context, bidSheetId);
        }
    }

    /**
     * POST Request Function - Handles all POST requests and background processing for the HTML page
     */
    function handlePost(context) {
        // Process POST requests
        var requestPayload = JSON.parse(context.request.body);
        context.response.setHeader("Content-Type", "application/json");

        switch (requestPayload["function"]) {
            case "getBidSheetItems":
                getBidSheetItems(context, requestPayload);
                break;
            case "saveBidSheetItem":
                saveBidSheetItem(context, requestPayload);
                break;
            case "clearVerifiedAndOverridenItems":
                clearVerifiedAndOverridenItems(context, requestPayload);
                break;
            case "getAllItems":
                getAllItems(context, requestPayload);
                break;
            case "getItemsJsonData":
                getItemsJsonData(context);
                break;
            case "getItemIndexJsonData":
                getItemIndexJsonData(context);
                break;
            case "getWordFrequencyJsonData":
                getWordFrequencyJsonData(context);
                break;
            default:
                customErrorObject.throwError({
                    summaryText: `UNSUPPORTED_FUNCTION_${requestPayload["function"]}`,
                    error: new Error(`Unsupported function: ${requestPayload["function"]}`)
                });
        }
    }

    /**
     * POST Request Function - Get all items from the system
     */
    function getBidSheetItems(context, requestPayload) {
        let responsePayload;

        try {
            let paramsJsonObj = JSON.parse(requestPayload.paramsJsonObj);
            let bidSheetId = paramsJsonObj.bidSheetId;

            const bidSheetItemsQuery = `
                SELECT
                    ROWNUM AS row_number,
                    bsi.id,
                    bsi.custrecord_spl_bsi_row_data,
                    bsi.custrecord_spl_bsi_potential_matches,
                    bsi.custrecord_spl_bsi_item_match,
                    bsi.custrecord_spl_bsi_match_override,
                    item.id AS override_item_internal_id,
                    item.itemid AS override_item_id,
                    item.displayname AS override_display_name,
                    item.vendorname AS override_vendor_code
                FROM
                    customrecord_spl_bid_sheet_items bsi
                LEFT JOIN
                    item ON item.id = bsi.custrecord_spl_bsi_item_match
                WHERE
                    bsi.custrecord_spl_bsi_bid_sheet = ?
                ORDER BY
                    bsi.id
            `;

            const bidSheetItems = query.runSuiteQL({
                query: bidSheetItemsQuery,
                params: [bidSheetId]
            }).asMappedResults();

            responsePayload = { bidSheetItems };
        } catch (err) {

            customErrorObject.throwError({
                summaryText: `ERROR_IN_GET_BID_SHEET_ITEMS`,
                error: err,
            });

            responsePayload = { error: err };
        }

        context.response.write(JSON.stringify(responsePayload));
    }

    /**
     * POST Request Function - Gets all supplyline items
     */
    function getAllItems(context, requestPayload) {
        let responsePayload;

        try {
            const itemsQuery = `
                SELECT
                    item.id,
                    item.itemid,
                    item.displayname,
                    item.vendorname AS vendor_code,
                    BUILTIN.HIERARCHY(item.class, 'DISPLAY_JOINED') AS item_category
                FROM
                    item
                WHERE
                    item.isinactive = 'F'
                    AND BUILTIN.DF(item.subsidiary) LIKE '%Supplyline%'
                ORDER BY
                    item.itemid
            `;

            // Use runSuiteQLPaged AS seen in spl_bid_sheet_processing_mr.js
            let itemPagedResults = query.runSuiteQLPaged({
                query: itemsQuery,
                pageSize: 1000
            });

            const items = [];

            // Process all pages
            for (let i = 0; i < itemPagedResults.pageRanges.length; i++) {
                let currentPage = itemPagedResults.fetch(i);
                let currentPagedData = currentPage.data.asMappedResults();

                for (const item of currentPagedData) {
                    items.push({
                        id: item.id,
                        itemId: item.itemid,
                        displayName: item.displayname || '',
                        vendorCode: item.vendor_code || '',
                        category: item.item_category || ''
                    });
                }
            }

            responsePayload = { items };
        } catch (err) {
            customErrorObject.throwError({
                summaryText: `ERROR_IN_GET_ALL_ITEMS`,
                error: err,
            });

            responsePayload = { error: err.message };
        }

        context.response.write(JSON.stringify(responsePayload));
    }

    /**
     * POST Request Function - Saves bid sheet item record depending on the action provided
     * 'verify': only saves the item match
     * 'overide': saves the selected item and sets the override flag to true
     * 'undooverride': clears the item match and sets the override flag to false
     */
    function saveBidSheetItem(context, requestPayload) {
        let responsePayload;
        try {
            let paramsJsonObj = JSON.parse(requestPayload.paramsJsonObj);
            let action = paramsJsonObj.action;
            let itemId = paramsJsonObj.itemId;
            let bidItemId = paramsJsonObj.bidItemId;
            let bidSheetItemValues = {};

            switch (action) {
                case 'verify':
                    bidSheetItemValues.custrecord_spl_bsi_item_match = itemId;
                    break;
                case 'override':
                    bidSheetItemValues.custrecord_spl_bsi_item_match = itemId;
                    bidSheetItemValues.custrecord_spl_bsi_match_override = true;
                    break;
                case 'undooverride':
                    bidSheetItemValues.custrecord_spl_bsi_item_match = '';
                    bidSheetItemValues.custrecord_spl_bsi_match_override = false;
                    break;
                default:
                    break;
            }

            try {
                record.submitFields({
                    type: 'customrecord_spl_bid_sheet_items',
                    id: bidItemId,
                    values: bidSheetItemValues
                });
            } catch (submitErr) {
                customErrorObject.throwError({
                    summaryText: `ERROR_SAVING_BID_SHEET_ITEM_${bidItemId}_${action}`,
                    error: submitErr
                });
                throw submitErr; // Re-throw to be caught by the outer catch block
            }

            const updatedItemQuery = `
                SELECT
                    ROWNUM AS row_number,
                    bsi.id,
                    bsi.custrecord_spl_bsi_row_data,
                    bsi.custrecord_spl_bsi_potential_matches,
                    bsi.custrecord_spl_bsi_item_match,
                    bsi.custrecord_spl_bsi_match_override,
                    item.id AS override_item_internal_id,
                    item.itemid AS override_item_id,
                    item.displayname AS override_display_name,
                    item.vendorname AS override_vendor_code
                FROM
                    customrecord_spl_bid_sheet_items bsi
                LEFT JOIN
                    item ON item.id = bsi.custrecord_spl_bsi_item_match
                WHERE
                    bsi.id = ?
            `;

            const updatedItem = query.runSuiteQL({
                query: updatedItemQuery,
                params: [bidItemId]
            }).asMappedResults()[0];

            responsePayload = {
                success: true,
                updatedItem: updatedItem
            };

        } catch (err) {
            customErrorObject.throwError({
                summaryText: `ERROR_IN_SAVE_BID_SHEET_ITEM`,
                error: err
            });

            responsePayload = { error: err };
        }

        context.response.write(JSON.stringify(responsePayload));
    }

    /**
     * POST Request Function - Gets customer item price
     */
    function getCustomerItemPrice(customerId, itemIds) {
        try {
            // if all items null, return empty array
            if(itemIds.every(item => item === null)) return [];

            itemIds = itemIds.filter(item => item !== null);

            let purchaserQuery = /*SQL */ `
                SELECT
                    integration.custrecord_spl_prchsng_fclty purchaser_id,
                    parent.id parent_id,
                    customer.id customer_id,
                FROM
                    customer
                    LEFT JOIN customer parent ON customer.parent = parent.id
                    LEFT JOIN customrecord_vlmd_edi_integration integration ON (
                        parent.custentity_spl_edi_integration_record = integration.id
                        OR customer.custentity_spl_edi_integration_record = integration.id
                    )
                WHERE
                    customer.id = ${customerId}`;

            let purchaserResult = query.runSuiteQL({
                query: purchaserQuery,
            }).asMappedResults()?.[0];

            let purchaserId =
                purchaserResult["purchaser_id"] ??
                purchaserResult["parent_id"] ??
                purchaserResult["customer_id"];

            if(!purchaserResult) customErrorObject.throwError({summaryText: 'ERROR_PROCESSING_BID_SHEET', error: 'No purchaser id found.'});

            let itemPricingQuery = /*SQL*/ `
                SELECT
                    ip.item as item_internal_id,
                    ip.price rate,
                    IP.level price_level_id,
                    BUILTIN.DF(IP.level) price_level_name,
                    i.pricinggroup price_group_id,
                    BUILTin.DF(pricinggroup) price_group_name,
                FROM
                    customerItemPricing AS IP
                    JOIN
                    item i
                    ON i.id = ip.item
                WHERE
                    IP.customer = ${purchaserId}
                    AND IP.item IN (\'${itemIds.join(`','`)}\')
                UNION
                SELECT
                    i.id as item_internal_id,
                    p.unitPrice rate,
                    GP.level price_level_id,
                    BUILTIN.DF( GP.level) price_level_name,
                    GP.GROUP price_group_id,
                    BUILTIN.DF( GP.GROUP) price_group_name,
                FROM
                    CustomerGroupPricing AS gp
                    INNER JOIN
                    item AS i
                    ON i.pricinggroup = gp.GROUP
                    LEFT JOIN
                    pricing p
                    ON p.pricelevel = gp.level
                    AND p.item = i.id
                WHERE
                    gp.customer = ${purchaserId}
                    AND i.id IN (\'${itemIds.join(`','`)}\')
				ORDER BY
					price_level_id `;

            let customerItemRates = query.runSuiteQL({
              query: itemPricingQuery,
            }).asMappedResults();

            if(!customerItemRates.length) customErrorObject.throwError({summaryText: 'ERROR_PROCESSING_BID_SHEET', error: 'No customer item rates found.'});

            return customerItemRates;
        } catch (queryErr) {
            customErrorObject.throwError({
                summaryText: `ERROR_GETTING_CUSTOMER_ITEM_PRICE_${customerId}`,
                error: queryErr
            });
            throw queryErr; // Re-throw to propagate the error
        }
    }

    /**
     * POST Request Function - Sanitizes input before writing to CSV
     */
    function sanitizeForCSV(value) {
        // Convert to string, handle null/undefined
        const str = (value ?? '').toString();
        // Escape quotes by doubling them and wrap in quotes
        return `"${str.replace(/"/g, '""')}"`;
    }

    /**
     * GET Request Function - Generates CSV and saves it to both file cabinet and link it to the bid sheet record
     */
    function generateFile(context, bidSheetId) {
        try {
            const bidSheetRecord = record.load({
                type: 'customrecord_spl_bid_sheets',
                id: bidSheetId
            });

            const customerId = bidSheetRecord.getValue({ fieldId: 'custrecord_spl_bid_sheets_customer' });
            const originalFileId = bidSheetRecord.getValue('custrecord_spl_bid_sheets_file');
            const originalFile = file.load({ id: originalFileId });
            const headers = originalFile.lines.iterator().next().value;
            const matchHeaders = [
                'SPL Item ID',
                'SPL Vendor Code',
                'SPL Item Name',
                'SPL Item Price',
            ].join(',');

            const verifiedBidSheetFile = file.create({
                name: `Verified_${originalFile.name}`,
                fileType: file.Type.CSV,
                contents: `${headers},${matchHeaders}\n`
            });
            verifiedBidSheetFile.folder = 8452611;

            const verifiedItemsQuery = `
                SELECT
                    ROWNUM AS row_number,
                    bsi.id AS bid_sheet_item_id,
                    bsi.custrecord_spl_bsi_row_data AS customer_item_description,
                    bsi.custrecord_spl_bsi_item_match AS spl_item_internal_id,
                    item.itemid AS spl_item_id,
                    item.vendorname AS spl_vendor_code,
                    item.displayname AS spl_display_name
                FROM
                    customrecord_spl_bid_sheet_items bsi
                LEFT JOIN
                    item ON item.id = bsi.custrecord_spl_bsi_item_match
                WHERE
                    bsi.custrecord_spl_bsi_bid_sheet = ?
            `;

            const verifiedItems = query.runSuiteQL({
                query: verifiedItemsQuery,
                params: [bidSheetId]
            }).asMappedResults();

            const itemIds = verifiedItems.map(item => item.spl_item_internal_id);
            const customerItemRates = getCustomerItemPrice(customerId, itemIds);
            const customerItemRateMap = new Map(customerItemRates.map(rate => [rate.item_internal_id, rate.rate]));

            verifiedItems.forEach(item => {
                const customerItemPrice = customerItemRateMap.get(item.spl_item_internal_id) || '';
                const newRow = [
                    item.customer_item_description,
                    sanitizeForCSV(item.spl_item_id),
                    sanitizeForCSV(item.spl_vendor_code),
                    sanitizeForCSV(item.spl_display_name),
                    sanitizeForCSV(customerItemPrice)
                ];
                verifiedBidSheetFile.appendLine({ value: newRow.join(',') });
            });

            const verifiedBidSheetFileId = verifiedBidSheetFile.save();

            bidSheetRecord.setValue({
                fieldId: 'custrecord_spl_bid_sheets_verified_file',
                value: verifiedBidSheetFileId
            });

            bidSheetRecord.save();

            log.audit('Verified Bid Sheet File Saved', `Bid Sheet Record ID: ${bidSheetId}, File ID: ${verifiedBidSheetFileId}`);

            context.response.writeFile(verifiedBidSheetFile);

        } catch (err) {
            customErrorObject.throwError({
                summaryText: `ERROR_IN_GENERATE_FILE`,
                error: err,
            });
        }
    }

    /**
     * GET Request Function - Render the HTML page using NS serverWidget API
     */
    function renderHTML(context, bidSheetId = 76) {
        try {
            const form = serverWidget.createForm({
                title: 'Bid Sheet Admin Review'
            });

            if(bidSheetId) {

                form.clientScriptModulePath = "SuiteScripts/Supplyline/Bid_Sheets/spl_bid_sheet_review_temp_cs.js";

                let htmlField = form.addField({
                    id: "custpage_field_html",
                    type: serverWidget.FieldType.INLINEHTML,
                    label: "HTML",
                });

                htmlField.defaultValue = generateHTML(bidSheetId);

            } else {

                // add bid sheet selector here

            }

            context.response.writePage(form);
        } catch (err) {
            customErrorObject.throwError({
                summaryText: `ERROR_IN_RENDER_HTML`,
                error: err
            });
        }
    }

    /**
     * GET Request Function - Compose a string of HTML to render
     */
    function generateHTML(bidSheetId) {
        return /*html*/ `
            <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css">
            <link rel="stylesheet" href="https://code.jquery.com/ui/1.13.2/themes/base/jquery-ui.css">
            <link rel="stylesheet" href="https://cdn.datatables.net/2.3.0/css/dataTables.dataTables.min.css">
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
            <script src="https://code.jquery.com/jquery-3.7.1.js"></script>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.3/umd/popper.min.js"></script>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/4.5.2/js/bootstrap.min.js"></script>
            <script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js"></script>
            <script src="https://cdn.datatables.net/2.3.0/js/dataTables.js"></script>



            ${cssStringStyles()}
            ${htmlUI(bidSheetId)}

            <!-- Override Modal -->
            <div class="modal fade" id="overrideModal" tabindex="-1" role="dialog" aria-labelledby="overrideModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h2 class="modal-title" id="overrideModalLabel">Override Item Match</h2>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <div class="form-group">
                                <label>Customer Item Description:</label>
                                <p id="customerItemDescription"></p>
                            </div>
                            <div class="form-group">
                                <label for="itemSearchInput">Search for an item to override matches:</label>
                                <div class="search-container">
                                    <input type="text" class="form-control" id="itemSearchInput" placeholder="Enter item ID, name, or description">
                                    <div id="searchResults" class="search-results" style="display: none;"></div>
                                </div>
                            </div>
                            <div id="selectedItemDetails" style="display: none;">
                                <h6>Selected Item:</h6>
                                <div class="card">
                                    <div class="card-body">
                                        <h5 class="card-title" id="selectedItemName"></h5>
                                        <p class="card-text" id="selectedItemId"></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                            <button type="button" class="btn btn-success" id="confirmOverrideBtn" disabled>Confirm Override</button>
                        </div>
                    </div>
                </div>
            </div>

            <script>
                var bidSheetItemsResponsePayload;
                var verifyMatchResponsePayload;
                var allItemsCache = [];
                var itemsIndexed = {}; // For faster lookups by ID
                var bidSheetId = ${bidSheetId};
                var table; // DataTable instance

                // JSON data for text matching
                var itemsJsonData = [];
                var itemIndexJsonData = {};
                var wordFrequencyJsonData = {};

                ${jsStringBidSheetItemsRequestFunction()}
                ${jsStringLoadAllItemsRequestFunction()}
                ${jsStringclearVerifiedAndOverridenItemsRequestFunction()}
                ${jsStringRenderBidSheetItemsTableFunction()}
                ${jsStringEventHandlerFunction()}
                ${jsStringDataTableLogic()}
                ${jsStringModalLogic()}
                ${jsStringRematchLogic()}
                ${jsStringLoadJsonDataFunctions()}

                // Initialize UI components when document is ready
                jQuery(document).ready(function() {

                    $('#clearVerifiedAndOverridenItems').on('click', function() {
                        if (confirm('Are you sure you want to clear all verified and overridden item matches? This action cannot be undone.')) {
                            clearVerifiedAndOverridenItemsRequest(bidSheetId);
                        }
                    });

                    bidSheetItemsRequest(bidSheetId);
                    loadAllItems(); // Load all items into cache
                    loadAllJsonData(); // Load JSON data files generated by data generator MR
                    setupEventHandlers();
                });

            </script>
        `;
    }

    /**
     * GET Request Function - Div Containers for the HTML UI
     */
    function htmlUI(bidSheetId) {
        let userRole = runtime.getCurrentUser().role;

        const bidSheetInformation = getBidSheetInformation(bidSheetId);

        let htmlUI = new String();

        if(userRole == 3 || userRole == 1197) {
            htmlUI += /*html*/`
                <div class="alert alert-info" role="alert" id="adminTools">
                    <div><strong>Admin Tools:</strong></div>
                    <div style="margin-top: 5px;">
                        <button type="button" id="clearVerifiedAndOverridenItems" class="btn btn-outline-danger btn-sm">Clear Verified and Overridden Items</button>
                    </div>
                </div>
                <hr>
            `;
        }

        if(!bidSheetId) {
            htmlUI += /*html*/`
                <div class="container-fluid" style="width: 100%; max-width: 100%; padding: 0;">
                    <div id="items-container" style="width: 100%; max-width: 100%;">
                        <h1 style="text-align: center; margin-top: 50px;">No Bid Sheet ID provided</h1>
                    </div>
                </div>
            `;
        } else {

            htmlUI += /*html*/`
                <div class="mb-3">
                  <div class="d-flex flex-column flex-md-row justify-content-between">
                  <div class="mr-md-5">
                    <span class="text-muted small">Bid Sheet Number:</span>
                    <span class="ml-1"><strong>${bidSheetInformation.name}</strong></span>
                    </div>
                    <div class="mr-md-5">
                      <span class="text-muted small">Customer:</span>
                      <span class="ml-1"><strong>(${bidSheetInformation.entityid}) ${bidSheetInformation.companyname}</strong></span>
                    </div>
                    <div>
                      <span class="text-muted small">Bid Sheet Date:</span>
                      <span class="ml-1"><strong>${bidSheetInformation.created}</strong></span>
                    </div>
                  </div>
                  <hr>
                </div>
            `;

            // Button container with toggle
            htmlUI += /*html*/`
                <div class="container-fluid" style="width: 100%; max-width: 100%; padding: 0;">
                    <div id="button-container" style="width: 100%; max-width: 100%; margin-bottom: 20px; display: flex; justify-content: space-between; align-items: center;">
                        <div class="btn-download-group">
                            <button type="button" class="btn btn-success mr-2 file-download-btn" id="download-csv-btn" data-file-type="csv">Download CSV</button>
                        </div>
                        <div class="custom-control custom-switch">
                            <!-- commented for now because of a filtering bug, will work on phase 4 -->
                            <!-- <input type="checkbox" class="custom-control-input" id="show-unverified-only">
                            <label class="custom-control-label" for="show-unverified-only">Unverified rows only</label> -->
                        </div>
                    </div>
                </div>
            `;

            // Datatable
            htmlUI += /*html*/`
                <div class="container-fluid" style="width: 100%; max-width: 100%; padding: 0;">
                    <div id="matches-container" style="width: 100%; max-width: 100%;">
                        <div class="row justify-content-md-center" style="margin-top: 50px; margin-bottom: 50px;">
                            <div class="col-md-auto text-center">
                                <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
                                    <span class="sr-only">Loading...</span>
                                </div>
                                <div style="margin-top: 15px; font-size: 16px;">
                                    <strong>Loading bid sheet items...</strong>
                                    <p class="text-muted">This may take a moment while we retrieve and process the data.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Test Keyword Modal
            htmlUI += /*html*/`
                <!-- Test Keyword Modal -->
                <div class="modal fade" id="testKeywordModal" tabindex="-1" role="dialog" aria-labelledby="testKeywordModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-xl" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h2 class="modal-title h3 font-weight-bold" id="testKeywordModalLabel">Test Match</h2>
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="form-group">
                                    <label class="font-weight-bold lead">Customer Item Description:</label>
                                    <p id="testKeywordCustomerDescription" class="lead p-2 bg-light border rounded"></p>
                                </div>
                                <div id="testKeywordResults" class="mt-3">
                                    <!-- Results will be populated dynamically -->
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Test Score Modal
            htmlUI += /*html*/`
                <!-- Test Score Modal -->
                <div class="modal fade" id="testScoreModal" tabindex="-1" role="dialog" aria-labelledby="testScoreModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-xl" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h2 class="modal-title h3 font-weight-bold" id="testScoreModalLabel">Test Score</h2>
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="form-group">
                                    <label class="font-weight-bold lead">Customer Item Description:</label>
                                    <p id="testScoreCustomerDescription" class="lead p-2 bg-light border rounded"></p>
                                </div>
                                <div id="testScoreResults" class="mt-3">
                                    <!-- Results will be populated dynamically -->
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        return htmlUI;
    }

    /**
     * GET Request Function - Gets the all the necessary bid sheet information
     */
    function getBidSheetInformation (bidSheetId) {
        try {
            const bidSheetQuery = `
                SELECT
                    bidsheet.id,
                    bidsheet.name,
                    bidsheet.custrecord_spl_bid_sheets_customer,
                    bidsheet.created,
                    customer.entityid,
                    customer.companyname
                FROM
                    customrecord_spl_bid_sheets bidsheet
                JOIN
                    customer ON customer.id = bidsheet.custrecord_spl_bid_sheets_customer
                WHERE
                    bidsheet.id = ?
            `;

            const bidSheetInformation = query.runSuiteQL({
                query: bidSheetQuery,
                params: [bidSheetId]
            }).asMappedResults();

            return bidSheetInformation[0];
        } catch (err) {
            customErrorObject.throwError({
                summaryText: `ERROR_IN_GET_BID_SHEET_INFORMATION`,
                error: err,
            });
        }
    }

    /**
     * GET Request Function - Compose a string that contains the Bid Sheet Items Request function
     */
    function jsStringBidSheetItemsRequestFunction() {

        return /*js*/`
            function bidSheetItemsRequest(bidSheetId) {
                if (!bidSheetId) {
                    alert('Missing bid sheet ID.');
                    return;
                }

                let paramsJsonObj = JSON.stringify({bidSheetId});

                var requestPayload = {
                    "function": "getBidSheetItems",
                    "paramsJsonObj": paramsJsonObj,
                }

                var xhr = new XMLHttpRequest();
                xhr.open('POST', '${scriptURL}', true);
                xhr.setRequestHeader('Accept', 'application/json');
                xhr.send(JSON.stringify(requestPayload));
                xhr.onload = function() {
                    if (xhr.status === 200) {
                        try {
                            bidSheetItemsResponsePayload = JSON.parse(xhr.response);
                        } catch (err) {
                            alert('Unable to parse the response.');
                            return;
                        }

                        if(bidSheetItemsResponsePayload.bidSheetItems.length === 0) {
                            alert('No items found for the bid sheet.');
                            return;
                        }

                        if(bidSheetItemsResponsePayload.error === undefined) {
                            renderBidSheetItemsTable();
                        } else {
                            alert('Error: ' + bidSheetItemsResponsePayload.error.message);
                            return;
                        }

                    } else {
                        alert("Error: " + xhr.status);
                    }
                }

                // Add error handler for network issues
                xhr.onerror = function() {
                    alert("Network error occurred while trying to fetch bid sheet items.");
                };
            }
        `;

    }

    /**
     * GET Request Function - Compose a string that contains the Load All Items Request function
     */
    function jsStringLoadAllItemsRequestFunction() {
        return /*js*/`
            function loadAllItems() {
                // Try to load from localStorage first
                const cachedItems = localStorage.getItem('bidSheetItemsCache');
                const cacheTimestamp = localStorage.getItem('bidSheetItemsCacheTimestamp');
                const cacheExpiry = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

                // Check if we have a valid cache (not expired)
                if (cachedItems && cacheTimestamp && (Date.now() - parseInt(cacheTimestamp) < cacheExpiry)) {
                    try {
                        // Use the cached data
                        const parsedCache = JSON.parse(cachedItems);
                        allItemsCache = parsedCache;

                        // Rebuild the indexed version
                        itemsIndexed = {};
                        allItemsCache.forEach(item => {
                            itemsIndexed[item.id] = item;
                        });

                        console.log('Loaded ' + allItemsCache.length + ' items from localStorage cache');
                        return;
                    } catch (err) {
                        console.error('Error parsing cached items, will fetch from server: ' + err.message);
                        // Continue to fetch from server if parsing fails
                    }
                }

                // If no valid cache exists, fetch from server
                var requestPayload = {
                    "function": "getAllItems"
                };

                var xhr = new XMLHttpRequest();
                xhr.open('POST', '${scriptURL}', true);
                xhr.setRequestHeader('Accept', 'application/json');
                xhr.send(JSON.stringify(requestPayload));
                xhr.onload = function() {
                    if (xhr.status === 200) {
                        try {
                            const response = JSON.parse(xhr.response);
                            if(response.error === undefined) {
                                // Store items in cache
                                allItemsCache = response.items;

                                // Create indexed version for faster lookups
                                allItemsCache.forEach(item => {
                                    itemsIndexed[item.id] = item;
                                });

                                // Create search index for faster filtering
                                allItemsCache.forEach(item => {
                                    // Create a searchable string for each item
                                    item.searchText = [
                                        item.id,
                                        item.itemId,
                                        item.displayName,
                                        item.vendorCode,
                                        item.category
                                    ].filter(Boolean).join(' ').toLowerCase();
                                });

                                // Save to localStorage with timestamp
                                try {
                                    localStorage.setItem('bidSheetItemsCache', JSON.stringify(allItemsCache));
                                    localStorage.setItem('bidSheetItemsCacheTimestamp', Date.now().toString());
                                    console.log('Saved ' + allItemsCache.length + ' items to localStorage cache');
                                } catch (storageErr) {
                                    // Handle storage errors (e.g., quota exceeded)
                                    console.error('Failed to save to localStorage: ' + storageErr.message);
                                }

                                console.log('Loaded ' + allItemsCache.length + ' items from server');
                            } else {
                                console.error('Error loading items: ' + response.error.message);
                            }
                        } catch (err) {
                            console.error('Unable to parse the response: ' + err.message);
                        }
                    } else {
                        console.error("Error loading items: " + xhr.status);
                    }
                };
            }
        `;

    }

    /**
     * GET Request Function - Compose a string that contains the Event Handler function
     */
    function jsStringEventHandlerFunction() {
        return /*js*/`
            // Handle button clicks and interactions
            function setupEventHandlers() {
                // Handle verify button clicks
                jQuery(document).on('click', '.verify-link', function(e) {
                    e.preventDefault();
                    const itemId = jQuery(this).data('item-id');
                    const bidItemId = jQuery(this).data('bid-item-id');
                    if (itemId && bidItemId) {
                        let paramsJsonObj = JSON.stringify({
                            action: 'verify',
                            itemId,
                            bidItemId
                        });

                        var requestPayload = {
                            "function": "saveBidSheetItem",
                            "paramsJsonObj": paramsJsonObj,
                        }

                        // Show loading indicator for the specific row
                        const rowElement = jQuery(this).closest('tr');
                        rowElement.addClass('updating-row');

                        var xhr = new XMLHttpRequest();
                        xhr.open('POST', '${scriptURL}', true);
                        xhr.setRequestHeader('Accept', 'application/json');
                        xhr.send(JSON.stringify(requestPayload));
                        xhr.onload = function() {
                            if (xhr.status === 200) {
                                try {
                                    verifyMatchResponsePayload = JSON.parse(xhr.response);
                                } catch (err) {
                                    alert('Unable to parse the response.');
                                    return;
                                }

                                if(verifyMatchResponsePayload.error === undefined) {
                                    updateSingleRow(verifyMatchResponsePayload.updatedItem, 'verify');
                                } else {
                                    alert('Error: ' + verifyMatchResponsePayload.error.message);
                                    return;
                                }

                            } else {
                                alert("Error: " + xhr.status);
                            }
                        }
                    }
                });

                // Handle View button clicks
                jQuery(document).on('click', '.view-link', function(e) {
                    e.preventDefault();
                    const itemId = jQuery(this).data('item-id');
                    if (itemId) {
                        window.open('/app/common/item/item.nl?id=' + itemId, '_blank');
                    }
                });

                // Handle Undo Override link clicks
                jQuery(document).on('click', '.undo-override-link', function() {
                    const bidItemId = jQuery(this).data('bid-item-id');
                    const bidSheetId = jQuery(this).data('bid-sheet-id');

                    if (confirm('Are you sure you want to remove this override?')) {
                        let paramsJsonObj = JSON.stringify({
                            action: 'undooverride',
                            bidSheetId: bidSheetId,
                            bidItemId: bidItemId
                        });

                        var requestPayload = {
                            "function": "saveBidSheetItem",
                            "paramsJsonObj": paramsJsonObj,
                        };

                        // Show loading indicator for the specific row
                        const rowElement = jQuery(this).closest('tr');
                        rowElement.addClass('updating-row');

                        var xhr = new XMLHttpRequest();
                        xhr.open('POST', '${scriptURL}', true);
                        xhr.setRequestHeader('Accept', 'application/json');
                        xhr.send(JSON.stringify(requestPayload));
                        xhr.onload = function() {
                            if (xhr.status === 200) {
                                try {
                                    const response = JSON.parse(xhr.response);

                                    if(response.error === undefined && response.success) {
                                        updateSingleRow(response.updatedItem, 'undooverride');
                                    } else {
                                        alert('Error: ' + (response.error ? response.error.message : 'Unknown error'));
                                        rowElement.removeClass('updating-row');
                                    }
                                } catch (err) {
                                    alert('Unable to parse the response.');
                                    rowElement.removeClass('updating-row');
                                }
                            } else {
                                alert("Error: " + xhr.status);
                                rowElement.removeClass('updating-row');
                            }
                        };
                    }
                });

                // Handle File Download button clicks
                jQuery(document).on('click', '.file-download-btn', function() {
                    if(confirm("Any unverified or non-overridden rows will have blank SPL item information in the CSV. Continue?")) {
                        const fileType = jQuery(this).data('file-type');
                        if(fileType === 'csv') {
                            window.open('${scriptURL}&function=downloadFile&bidsheetid=' + bidSheetId);
                        } else {
                            alert('File type not supported');
                        }
                    }
                });

                // Toggle text function for description expansion
                window.toggleText = function(id) {
                    var shortSpan = document.getElementById("short-" + id);
                    var fullSpan = document.getElementById("full-" + id);
                    var button = document.getElementById("btn-" + id);
                    if (shortSpan.style.display === "inline") {
                        shortSpan.style.display = "none";
                        fullSpan.style.display = "inline";
                        button.textContent = "Show less";
                    } else {
                        shortSpan.style.display = "inline";
                        fullSpan.style.display = "none";
                        button.textContent = "Show more";
                    }
                }
            }
        `;
    }

    /**
     * GET Request Function - Compose a string that contains the Render Bid Sheet Items Table function
     */
    function jsStringRenderBidSheetItemsTableFunction() {
        let userRole = runtime.getCurrentUser().role;

        return /*js*/`
            function renderBidSheetItemsTable() {

                // Check for empty items first
                if (bidSheetItemsResponsePayload.bidSheetItems.length === 0) {
                    document.getElementById('matches-container').innerHTML =
                        '<div class="alert alert-warning">No bid sheet items found.</div>';
                    return;
                }

                // If we have items, proceed with rendering
                let itemsHtml = '';

                // Create table structure
                itemsHtml += '<div class="container-fluid" style="width: 100%; max-width: 100%; padding: 0;">';
                itemsHtml += '<div id="items-container" style="width: 100%; max-width: 100%;">';
                itemsHtml += '<table id="bidItemsTable" class="table table-bordered match-table" style="width: 100%; max-width: 100%;">';

                // Add table header
                itemsHtml += '<thead>';
                itemsHtml += '<tr>';
                itemsHtml += '<th class="align-middle py-3" style="width: 22%;">Customer Item Description</th>';
                itemsHtml += '<th class="align-middle py-3" style="width: 22%;">Best Match</th>';
                itemsHtml += '<th class="align-middle py-3" style="width: 22%;">Second Best Match</th>';
                itemsHtml += '<th class="align-middle py-3" style="width: 22%;">Third Best Match</th>';
                itemsHtml += '<th class="align-middle text-center py-3" style="width: 6%;">Override Matches</th>';
                itemsHtml += '</tr>';
                itemsHtml += '</thead>';
                itemsHtml += '<tbody>';

                // Process each bid sheet item
                bidSheetItemsResponsePayload.bidSheetItems.forEach(function(item) {

                    let customerItemDescription = item.custrecord_spl_bsi_row_data || '';
                    let rowNumber = item.row_number || '';

                    let shortDescription = customerItemDescription.length > 100 ?
                        customerItemDescription.substring(0, 100) + '...' :
                        customerItemDescription;

                    let potentialMatches = [];
                    try {
                        potentialMatches = JSON.parse(item.custrecord_spl_bsi_potential_matches || '[]');
                    } catch (e) {
                        console.error('Error parsing potential matches for item ' + item.id + ': ' + e.message);
                    }

                    const hasVerifiedMatch = item.custrecord_spl_bsi_item_match ? true : false;
                    const hasOverrideMatch = item.custrecord_spl_bsi_match_override ? ((item.custrecord_spl_bsi_match_override === 'T') ? true : false) : false;

                    // Start row with verified class if needed
                    const rowClass = hasVerifiedMatch ? 'verified-row' : '';
                    itemsHtml += '<tr class="' + rowClass + '" data-has-verified="' + hasVerifiedMatch + '">';

                    // Customer description cell
                    itemsHtml += '<td>';
                    itemsHtml += '<div class="item-header">';
                    itemsHtml += '<div style="font-size: 12px;">';
                    itemsHtml += '<div style="color: #777; font-size: 10px; margin-bottom: 3px;">Row: ' + rowNumber + '</div>';
                    itemsHtml += '<span id="short-' + item.id + '" style="display: inline;">' + shortDescription + '</span>';
                    itemsHtml += '<span id="full-' + item.id + '" style="display: none;">' + customerItemDescription + '</span>';

                    if (customerItemDescription.length > 100) {
                        itemsHtml += '<br><button type="button" onclick="toggleText(' + item.id + ')" ';
                        itemsHtml += 'id="btn-' + item.id + '" style="color: blue; background: none; border: none; ';
                        itemsHtml += 'text-decoration: underline; cursor: pointer; padding: 0; font-size: 11px;">';
                        itemsHtml += 'Show more</button>';
                    }

                    if(${userRole} == 3 || ${userRole} == 1197) {
                        itemsHtml += '<div class="admin-tools mt-2">';
                        itemsHtml += '<a href="/app/common/custom/custrecordentry.nl?rectype=3376&id=' + item.id + '" ';
                        itemsHtml += 'target="_blank" class="btn btn-outline-danger btn-sm mr-2">';
                        itemsHtml += 'Go To Bid Sheet Item</a>';

                        // itemsHtml += '<a href="#" class="test-match-link btn btn-outline-danger btn-sm mr-2" data-bid-row-id="' + item.id + '" data-toggle="modal" ';
                        // itemsHtml += 'data-target="#testKeywordModal" data-bid-sheet-id="' + bidSheetId + '" data-customer-description="' + customerItemDescription.replace(/"/g, '&quot;') + '">';
                        // itemsHtml += '<i class="fas fa-sync-alt"></i> Test Match</a>';

                        itemsHtml += '<a href="#" class="test-keyword-link btn btn-outline-danger btn-sm mr-2" data-bid-row-id="' + item.id + '" ';
                        itemsHtml += 'data-bid-sheet-id="' + bidSheetId + '" data-customer-description="' + customerItemDescription.replace(/"/g, '&quot;') + '">';
                        itemsHtml += '<i class="fas fa-sync-alt"></i> Test Keywords</a>';

                        itemsHtml += '<a href="#" class="test-score-link btn btn-outline-danger btn-sm mr-2" data-bid-row-id="' + item.id + '" ';
                        itemsHtml += 'data-bid-sheet-id="' + bidSheetId + '" data-customer-description="' + customerItemDescription.replace(/"/g, '&quot;') + '">';
                        itemsHtml += '<i class="fas fa-sync-alt"></i> Test Scores</a>';

                        itemsHtml += '</div>';
                    }

                    itemsHtml += '</div>';
                    itemsHtml += '</div>';
                    itemsHtml += '</td>';

                    for (let i = 0; i < 3; i++) {
                        let match = potentialMatches[i];

                        if(hasOverrideMatch) {

                            // Format item details
                            let itemIdHtml = '<div style="font-size: 14px; font-weight: bold;">' + item.override_item_id + '</div>';
                            let vendorCodeHtml = '<div style="font-size: 12px;">Vendor Code: ' + item.override_vendor_code + '</div>' ;
                            let displayNameHtml = '<div style="font-size: 13px;">' + item.override_display_name + '</div>';

                            // Create view item link
                            let viewItemLink = '';
                            if (item.override_item_internal_id) {
                                viewItemLink = '<a href="#" class="action-link view-link" data-item-id="' + item.override_item_internal_id + '"><i class="fas fa-eye"></i> View</a>';
                            }

                            itemsHtml += '<td class="match-cell">';
                            itemsHtml += '<div class="match-details">';
                            itemsHtml += '<div class="item-info">';
                            itemsHtml += '<div style="display: flex; justify-content: space-between; align-items: flex-start; width: 100%; overflow: hidden;">';
                            itemsHtml += '<div style="flex: 1; min-width: 0; padding-right: 10px;">';
                            itemsHtml += itemIdHtml;
                            itemsHtml += vendorCodeHtml;
                            itemsHtml += displayNameHtml;
                            itemsHtml += '</div>';
                            itemsHtml += '<div style="flex-shrink: 0;">';
                            itemsHtml += '<span class="score-pill verified-pill">Overridden</span>';
                            itemsHtml += '</div>';
                            itemsHtml += '</div>';
                            itemsHtml += '</div>';
                            itemsHtml += '<div class="action-buttons">';
                            itemsHtml += viewItemLink;
                            itemsHtml += '</div>';
                            itemsHtml += '</div>';
                            itemsHtml += '</td>';

                            // add blank cells
                            itemsHtml += '<td class="match-cell"></td>';
                            itemsHtml += '<td class="match-cell"></td>';

                            break;

                        } else if (match) {
                            // Check if this match is the verified one
                            const isVerified = hasVerifiedMatch && match.itemInternalId === item.custrecord_spl_bsi_item_match;

                            // Determine score class
                            let scoreClass = '';
                            if (isVerified) {
                                scoreClass = 'verified-pill';
                            } else if (match.score >= 80) {
                                scoreClass = 'score-high';
                            } else if (match.score >= 50) {
                                scoreClass = 'score-medium';
                            } else {
                                scoreClass = 'score-low';
                            }

                            // Format item details
                            let itemIdHtml = match.itemId ?
                                '<div style="font-size: 14px; font-weight: bold;">' + match.itemId + '</div>' : '';
                            let vendorCodeHtml = match.vendorCode ?
                                '<div style="font-size: 12px;">Vendor Code: ' + match.vendorCode + '</div>' : '';
                            let displayNameHtml = match.displayName ?
                                '<div style="font-size: 13px;">' + match.displayName + '</div>' : '';

                            itemsHtml += '<td class="match-cell ' + (hasVerifiedMatch && !isVerified ? 'unselected-match' : '') + '">';
                            itemsHtml += '<div class="match-details">';
                            itemsHtml += '<div class="item-info">';
                            itemsHtml += '<div style="display: flex; justify-content: space-between; align-items: flex-start; width: 100%; overflow: hidden;">';
                            itemsHtml += '<div style="flex: 1; min-width: 0; padding-right: 10px;">';
                            itemsHtml += itemIdHtml;
                            itemsHtml += vendorCodeHtml;
                            itemsHtml += displayNameHtml;
                            itemsHtml += '</div>';
                            itemsHtml += '<div style="flex-shrink: 0;">';

                            if(hasVerifiedMatch) {
                                if(isVerified) {
                                    itemsHtml += '<span class="score-pill verified-pill">Verified</span>';
                                }
                                else {
                                    itemsHtml += '<span class="score-pill ' + scoreClass + ' unselected-pill">' + match.score + '% match</span>';
                                }
                            } else {
                                itemsHtml += '<span class="score-pill ' + scoreClass + '">' + match.score + '% match</span>';
                            }


                            itemsHtml += '</div>';
                            itemsHtml += '</div>';
                            itemsHtml += '</div>';
                            itemsHtml += '<div class="action-buttons">';
                            itemsHtml += '<a href="#" class="action-link view-link" data-item-id="' + match.itemInternalId + '"><i class="fas fa-eye"></i> View</a>';
                            itemsHtml += '<a href="#" class="action-link verify-link" data-item-id="' + match.itemInternalId + '" data-bid-item-id="' + item.id + '">';
                            itemsHtml += '<i class="fas fa-check"></i> Verify</a>';
                            itemsHtml += '</div>';
                            itemsHtml += '</div>';
                            itemsHtml += '</td>';
                        } else {
                            itemsHtml += '<td class="match-cell' + (hasVerifiedMatch ? ' unselected-match' : '') + '"></td>';
                        }
                    }

                    // Add override column
                    itemsHtml += '<td style="text-align: center; vertical-align: middle; padding: 8px;">';
                    itemsHtml += '<a href="javascript:void(0)" class="action-link override-link" data-toggle="modal" ';
                    itemsHtml += 'data-target="#overrideModal" data-bid-item-id="' + item.id + '" data-bid-sheet-id="' + bidSheetId + '" ';
                    itemsHtml += 'data-customer-description="' + customerItemDescription.replace(/"/g, '&quot;') + '" ';
                    itemsHtml += (hasOverrideMatch ? 'style="display:none;"' : '') + '>';
                    itemsHtml += '<i class="fas fa-exchange-alt"></i> Override</a>';

                    itemsHtml += '<a href="javascript:void(0)" class="action-link undo-override-link" ';
                    itemsHtml += 'data-bid-item-id="' + item.id + '" data-bid-sheet-id="' + bidSheetId + '" ';
                    itemsHtml += (hasOverrideMatch ? '' : 'style="display:none;"') + '>';
                    itemsHtml += '<i class="fas fa-undo"></i> Undo</a>';
                    itemsHtml += '</td>';
                    itemsHtml += '</tr>';
                });

                // Close table structure
                itemsHtml += '</tbody>';
                itemsHtml += '</table>';
                itemsHtml += '</div>';
                itemsHtml += '</div>';

                // Replace the loading spinner with the table
                document.getElementById('matches-container').innerHTML = itemsHtml;

                // Initialize DataTable
                initializeDataTable();
            }
        `;
    }

    /**
     * GET Request Function - Compose a string that contains all DataTable logic and functions
     */
    function jsStringDataTableLogic() {
        return /*js*/`
            // Initialize DataTable
            function initializeDataTable() {
                table = new DataTable('#bidItemsTable', {
                    responsive: true,
                    pageLength: 10,
                    ordering: false, // Disable ordering/sorting functionality
                    language: {
                        lengthMenu: "_MENU_ _ENTRIES_ per page",
                        info: "Showing _START_ to _END_ of _TOTAL_ _ENTRIES_",
                        entries: {
                            _: 'items',
                            1: 'item'
                        }
                    },
                    layout: {
                        topStart: 'pageLength',
                        topEnd: 'search',
                        bottomStart: 'info',
                        bottomEnd: 'paging'
                    },
                    columnDefs: [
                        { responsivePriority: 1, targets: 0 }, // Customer Item Description
                        { responsivePriority: 2, targets: 1 }, // Best Match
                        { responsivePriority: 3, targets: 4 }, // Override Matches
                        { orderable: false, targets: '_all' } // Explicitly make all columns non-orderable
                    ]
                });

                return table; // Return the table instance for later use
            }

            // Function to update a single row in the DataTable
            function updateSingleRow(updatedItem, action) {
                if (!updatedItem) return;

                const rowIndex = table.rows().indexes().filter(function(value, index) {
                    return jQuery(table.row(value).node()).find('a[data-bid-item-id="' + updatedItem.id + '"]').length > 0;
                });

                const rowNode = table.row(rowIndex[0]).node();
                const $row = jQuery(rowNode);

                // Check if this is an override or undo operation
                if (action === 'override') {
                    // Override operation
                    $row.addClass('verified-row');
                    $row.attr('data-has-verified', 'true');

                    // Update the override links visibility
                    $row.find('.override-link').hide();
                    $row.find('.undo-override-link').show();

                    // Clear all match cells first
                    $row.find('.match-cell').empty();

                    const matchCell = $row.find('.match-cell').eq(0);
                    matchCell.removeClass('unselected-match');
                    matchCell.html(
                        '<div class="match-details">' +
                            '<div class="item-info">' +
                                '<div style="display: flex; justify-content: space-between; align-items: flex-start; width: 100%; overflow: hidden;">' +
                                    '<div style="flex: 1; min-width: 0; padding-right: 10px;">' +
                                        '<div style="font-size: 14px; font-weight: bold;">' + (updatedItem.override_item_id || '') + '</div>' +
                                        '<div style="font-size: 12px;">Vendor Code: ' + (updatedItem.override_vendor_code || '') + '</div>' +
                                        '<div style="font-size: 13px;">' + (updatedItem.override_display_name || '') + '</div>' +
                                    '</div>' +
                                    '<div style="flex-shrink: 0;">' +
                                        '<span class="score-pill verified-pill">Overridden</span>' +
                                    '</div>' +
                                '</div>' +
                            '</div>' +
                        '</div>'
                    );
                } else if (action === 'undooverride') {
                    // Undo override operation
                    $row.removeClass('verified-row');
                    $row.attr('data-has-verified', 'false');

                    // Update the override links visibility
                    $row.find('.override-link').show();
                    $row.find('.undo-override-link').hide();

                    $row.removeClass('updating-row');
                    updatedItem.custrecord_spl_bsi_potential_matches = JSON.parse(updatedItem.custrecord_spl_bsi_potential_matches);
                    updatedItem.custrecord_spl_bsi_potential_matches.forEach(function(match, index) {
                        const $cell = $row.find('.match-cell').eq(index);
                        $cell.removeClass('unselected-match');
                        $cell.html(
                            '<div class="match-details">' +
                                '<div class="item-info">' +
                                    '<div class="d-flex justify-content-between align-items-start w-100 overflow-hidden">' +
                                        '<div class="flex-grow-1 min-width-0 pr-2">' +
                                            '<div class="font-weight-bold" style="font-size: 14px;">' + (match.itemId || '') + '</div>' +
                                            '<div class="small">Vendor Code: ' + (match.vendorCode || '') + '</div>' +
                                            '<div style="font-size: 13px;">' + (match.displayName || '') + '</div>' +
                                        '</div>' +
                                        '<div class="flex-shrink-0">' +
                                            '<span class="score-pill ' + (match.score >= 80 ? 'score-high' : match.score >= 50 ? 'score-medium' : 'score-low') + '">' + match.score + '% match</span>' +
                                        '</div>' +
                                    '</div>' +
                                '</div>' +
                            '</div>'
                        );
                    });
                } else if (action === 'verify') {
                    $row.addClass('verified-row');
                    $row.attr('data-has-verified', 'true');

                    // Replace the score pill with "Verified" text
                    const $cell = $row.find('.match-cell:has(.verify-link[data-item-id="' + updatedItem.custrecord_spl_bsi_item_match + '"])');
                    $cell.find('.score-pill').text('Verified').addClass('verified-pill');
                    $cell.removeClass('unselected-match unselected-pill');

                    // Apply unselected-match class to all other match cells in this row
                    $row.find('.match-cell').not($cell).addClass('unselected-match')
                        .find('.score-pill').addClass('unselected-pill');

                    $row.removeClass('updating-row');
                }
            }
        `;
    }

    /**
     * GET Request Function - Compose a string that contains all Modal logic and functions
     */
    function jsStringModalLogic() {
        return /*js*/`

            // Handle Override modal interactions
            jQuery(document).on('click', '.override-link', function() {
                const bidItemId = jQuery(this).data('bid-item-id');
                const bidSheetId = jQuery(this).data('bid-sheet-id');
                const customerItemDescription = jQuery(this).data('customer-description');

                // Set values in the modal
                jQuery('#overrideModal').data('bid-item-id', bidItemId);
                jQuery('#overrideModal').data('bid-sheet-id', bidSheetId);

                // Set the customer item description
                jQuery('#customerItemDescription').text(customerItemDescription || 'No description available');

                // Clear previous search and selection
                jQuery('#itemSearchInput').val('');
                jQuery('#searchResults').hide();
                jQuery('#selectedItemDetails').hide();
                jQuery('#confirmOverrideBtn').prop('disabled', true);

                // Show the modal
                jQuery('#overrideModal').modal('show');
            });

            // Handle item search input with optimized client-side filtering
            jQuery('#itemSearchInput').on('input', function() {
                const searchTerm = jQuery(this).val().trim().toLowerCase();
                if (searchTerm.length >= 2) {
                    // Filter items from cache
                    clearTimeout(window.searchTimeout);
                    window.searchTimeout = setTimeout(function() {

                        const filteredItems = allItemsCache
                            .filter(item => item.searchText.includes(searchTerm))
                            .slice(0, 50);

                        let resultsHtml = '';

                        if (filteredItems.length > 0) {
                            filteredItems.forEach(item => {
                                resultsHtml +=
                                '<div class="search-result-item p-2 border-bottom" ' +
                                        'data-item-id="' + item.id + '" ' +
                                        'data-item-name="' + item.itemId + '"' +
                                        'data-item-display-name="' + (item.displayName || '') + '"' +
                                        'data-item-vendor="' + (item.vendorCode || '') + '"' +
                                        'data-item-category="' + (item.category || '') + '">' +
                                    '<div class="d-flex justify-content-between">' +
                                        '<strong>' + item.itemId + '</strong>' +
                                        '<span class="badge badge-secondary">' + (item.category || 'Uncategorized') + '</span>' +
                                    '</div>' +
                                    '<div class="small text-muted">' +
                                        (item.displayName || '') + ' ' + (item.vendorCode ? '| Vendor: ' + item.vendorCode : '') +
                                    '</div>' +
                                '</div>';
                            });
                        } else {
                            resultsHtml = '<div class="p-3 text-center">No items found. Try different search terms.</div>';
                        }

                        jQuery('#searchResults').html(resultsHtml);
                        jQuery('#searchResults').show();
                    }, 200);
                } else {
                    jQuery('#searchResults').hide();
                }
            });

            // Handle search result selection
            jQuery(document).on('click', '.search-result-item', function() {
                const itemId = jQuery(this).data('item-id');
                const itemName = jQuery(this).data('item-name');
                const displayName = jQuery(this).data('item-display-name');
                const vendorCode = jQuery(this).data('item-vendor');
                const category = jQuery(this).data('item-category');

                // Display selected item
                jQuery('#selectedItemName').text(itemName);
                jQuery('#selectedItemId').text('Item ID: ' + itemId);
                jQuery('#selectedItemDetails').html(
                    '<strong>Display Name:</strong> ' + (displayName || 'N/A') + '<br>' +
                        '<strong>Vendor Code:</strong> ' + (vendorCode || 'N/A') + '<br>' +
                        '<strong>Category:</strong> ' + (category || 'N/A')
                );
                jQuery('#selectedItemDetails').show();

                // Store the selected item ID in the modal
                jQuery('#overrideModal').data('selected-item-id', itemId);

                // Enable confirm override button
                jQuery('#confirmOverrideBtn').prop('disabled', false);

                // Hide search results
                jQuery('#searchResults').hide();
            });

            // Handle confirm override button
            jQuery('#confirmOverrideBtn').on('click', function() {
                const bidItemId = jQuery('#overrideModal').data('bid-item-id');
                const bidSheetId = jQuery('#overrideModal').data('bid-sheet-id');
                const itemId = jQuery('#overrideModal').data('selected-item-id');

                if (itemId && bidItemId) {
                    let paramsJsonObj = JSON.stringify({
                        action: 'override',
                        itemId,
                        bidItemId,
                        bidSheetId
                    });

                    var requestPayload = {
                        "function": "saveBidSheetItem",
                        "paramsJsonObj": paramsJsonObj,
                    }

                    // Close the modal
                    jQuery('#overrideModal').modal('hide');

                    // Show loading indicator for the specific row
                    const rowElement = jQuery('a.override-link[data-bid-item-id="' + bidItemId + '"]').closest('tr');
                    rowElement.addClass('updating-row');

                    var xhr = new XMLHttpRequest();
                    xhr.open('POST', '${scriptURL}', true);
                    xhr.setRequestHeader('Accept', 'application/json');
                    xhr.send(JSON.stringify(requestPayload));
                    xhr.onload = function() {
                        if (xhr.status === 200) {
                            try {
                                const response = JSON.parse(xhr.response);

                                if(response.error === undefined && response.success) {
                                    updateSingleRow(response.updatedItem, 'override');
                                } else {
                                    alert('Error: ' + (response.error ? response.error.message : 'Unknown error'));
                                }
                            } catch (err) {
                                alert('Unable to parse the response.');
                            } finally {
                                // Remove loading indicator
                                rowElement.removeClass('updating-row');
                                rowElement.find('.loading-spinner-cell').remove();
                            }
                        } else {
                            alert("Error: " + xhr.status);
                            rowElement.removeClass('updating-row');
                        }
                    }
                }
            });
        `;
    }

    /**
     * GET Request Function - Compose a string that contains the Rematch function
     */
    function jsStringRematchLogic() {
        return /*js*/`
            // Handle Test Keyword modal interactions
            jQuery(document).on('click', '.test-keyword-link', function() {
                const bidRowId = jQuery(this).data('bid-row-id');
                const bidSheetId = jQuery(this).data('bid-sheet-id');
                const customerItemDescription = jQuery(this).data('customer-description');

                // Set values in the modal
                jQuery('#testKeywordModal').data('bid-row-id', bidRowId);
                jQuery('#testKeywordModal').data('bid-sheet-id', bidSheetId);
                jQuery('#testKeywordModal').data('customer-description', customerItemDescription);
                jQuery('#testKeywordCustomerDescription').text(customerItemDescription);
                
                // Clear previous results
                jQuery('#testKeywordResults').empty();
                
                // Show the modal
                jQuery('#testKeywordModal').modal('show');
            });

            jQuery(document).on('click', '.test-score-link', function() {
                const bidRowId = jQuery(this).data('bid-row-id');
                const bidSheetId = jQuery(this).data('bid-sheet-id');
                const customerItemDescription = jQuery(this).data('customer-description');

                // Set values in the modal
                jQuery('#testScoreModal').data('bid-row-id', bidRowId);
                jQuery('#testScoreModal').data('bid-sheet-id', bidSheetId);
                jQuery('#testScoreModal').data('customer-description', customerItemDescription);
                jQuery('#testScoreCustomerDescription').text(customerItemDescription);
                
                // Clear previous results
                jQuery('#testScoreResults').empty();
                
                // Show the modal
                jQuery('#testScoreModal').modal('show');
            });

            jQuery('#testScoreModal').on('shown.bs.modal', function() {
                const customerItemDescription = jQuery(this).data('customer-description');                
                const bidRowId = jQuery(this).data('bid-row-id');
                
                // Show loading indicator
                jQuery('#testScoreResults').html('<div class="text-center"><i class="fas fa-spinner fa-spin fa-2x"></i><p class="mt-2">Processing match...</p></div>');

                // Create a new text matcher instance
                if (window.textMatcherClass) {
                    console.log('customerItemDescription', customerItemDescription);

                    let textMatcher = new window.textMatcherClass({
                        stringOne: customerItemDescription,
                        buildTokens: true
                    });
                    
                    let wordFrequency = new window.wordFrequencyAnalyzerClass({
                        textProcessor: textMatcher,
                        wordFrequencyMap: wordFrequencyJsonData
                    });

                    let itemRepository = new window.itemRepositoryClass({
                        itemsIndexed: itemIndexJsonData,
                    });

                    let customerDescriptionKeywords = wordFrequency.extractKeywords(customerItemDescription);
                    let matchingItems = itemRepository.searchItems(customerDescriptionKeywords.map(keywordDetail => keywordDetail.keyword), 0.1);

                    console.log('matchingItems before', matchingItems);
                    console.log('customerItemDescription', customerItemDescription);

                    const validMatchingItems = [];
                    
                    for (let i = 0; i < matchingItems.length; i++) {
                        const matchResult = matchingItems[i];
                        const result = textMatcher.calculateDistance({
                            stringOne: customerItemDescription, 
                            stringTwo: matchResult.itemId + ' ' + matchResult.displayName + ' ' + matchResult.vendorCode + ' ' + matchResult.category,
                            threshold: 0
                        });
                        
                        if (result) {
                            matchResult.score = result.score;
                            matchResult.matchDetails = result.details;
                            matchResult.rawMatchData = result;
                            validMatchingItems.push(matchResult);
                        }
                    }
                    
                    matchingItems = validMatchingItems
                        .sort((a, b) => Number(b.score) - Number(a.score));

                    console.log('matchingItems after', matchingItems);
                    
                    // Build visualization for the results
                    let resultsHtml = '';
                    
                    if (matchingItems.length > 0) {
                        resultsHtml += '<div class="list-group list-group-flush">';
                        matchingItems.forEach((matchResult, index) => {
                            const item = matchResult;
                            const score = item.score;
                            const matchDetails = item.matchDetails;
                            
                            // Determine score class
                            let scoreClass = 'score-low';
                            if (score >= 0.8) scoreClass = 'score-high';
                            else if (score >= 0.5) scoreClass = 'score-medium';
                            
                            resultsHtml += '<div class="list-group-item p-3' + (index % 2 === 0 ? ' bg-light' : '') + '">';
                            
                            // Item header with score
                            resultsHtml += '<div class="d-flex justify-content-between align-items-center mb-2">';
                            resultsHtml += '<h6 class="mb-0 font-weight-bold">' + (item.itemId || 'Unknown Item') + '</h6>';
                            resultsHtml += '<span class="score-pill ' + scoreClass + '">' + score + '% match</span>';
                            resultsHtml += '</div>';
                            
                            // Item details
                            resultsHtml += '<div class="mb-2">';
                            resultsHtml += '<div><strong>Display Name:</strong> ' + (item.displayName || 'N/A') + '</div>';
                            resultsHtml += '<div><strong>Vendor Code:</strong> ' + (item.vendorCode || 'N/A') + '</div>';
                            resultsHtml += '<div><strong>Category:</strong> ' + (item.category || 'N/A') + '</div>';
                            resultsHtml += '</div>';
                            
                            // Add a "Raw Data" section to each item in the results
                            if (matchResult.rawMatchData) {
                                resultsHtml += '<div class="mt-3">';
                                resultsHtml += '<h6 class="font-weight-bold">Complete Match Data</h6>';
                                resultsHtml += '<div class="p-2 border rounded bg-light">';
                                resultsHtml += '<pre class="mb-0" style="max-height: 300px; overflow-y: auto;">';
                                resultsHtml += JSON.stringify(matchResult.rawMatchData, null, 2);
                                resultsHtml += '</pre>';
                                resultsHtml += '</div>';
                                resultsHtml += '</div>';
                            }
                            
                            resultsHtml += '</div>';
                        });
                        resultsHtml += '</div>';
                    } else {
                        resultsHtml += '<div class="p-4 text-center">';
                        resultsHtml += '<p class="text-muted mb-0">No matching items found.</p>';
                        resultsHtml += '</div>';
                    }
                    
                    jQuery('#testScoreResults').html(resultsHtml);
                }
            });

            jQuery('#testKeywordModal').on('shown.bs.modal', function() {
                const customerItemDescription = jQuery(this).data('customer-description');                
                const bidRowId = jQuery(this).data('bid-row-id');
                
                // Show loading indicator
                jQuery('#testKeywordResults').html('<div class="text-center"><i class="fas fa-spinner fa-spin fa-2x"></i><p class="mt-2">Processing match...</p></div>');

                // Create a new text matcher instance
                if (window.textMatcherClass) {

                    console.log('customerItemDescription', customerItemDescription);

                    let textMatcher = new window.textMatcherClass({
                        stringOne: customerItemDescription,
                        buildTokens: true
                    });
                    
                    let wordFrequency = new window.wordFrequencyAnalyzerClass({
                        textProcessor: textMatcher,
                        wordFrequencyMap: wordFrequencyJsonData
                    });

                    let itemRepository = new window.itemRepositoryClass({
                        itemsIndexed: itemIndexJsonData,
                    });

                    let customerDescriptionKeywords = wordFrequency.extractKeywords(customerItemDescription);
                    console.log('customerDescriptionKeywords', customerDescriptionKeywords);

                    let matchingItems = itemRepository.searchItems(customerDescriptionKeywords.map(keywordDetail => keywordDetail.keyword), 0.1);


                    // Build visualization for the results
                    let resultsHtml = '';
                    
                    // 1. Display extracted keywords with frequency counts
                    resultsHtml += '<div class="card mb-4">';
                    resultsHtml += '<div class="card-header bg-light"><h5 class="mb-0">Extracted Keywords</h5></div>';
                    resultsHtml += '<div class="card-body">';
                    
                    if (customerDescriptionKeywords.length > 0) {
                        resultsHtml += '<div class="d-flex flex-wrap">';
                        customerDescriptionKeywords.forEach(keywordDetail => {
                            // Display each keyword with its frequency count
                            resultsHtml += '<span class="badge badge-light m-1">' + keywordDetail.keyword + 
                                ' <small>(' + keywordDetail.count + ')</small></span>';
                        });
                        resultsHtml += '</div>';
                    } else {
                        resultsHtml += '<p class="text-muted">No significant keywords found.</p>';
                    }
                    resultsHtml += '</div></div>';
                    
                    // 2. Display matching items
                    resultsHtml += '<div class="card">';
                    resultsHtml += '<div class="card-header bg-light"><h5 class="mb-0">Matching Items</h5></div>';
                    resultsHtml += '<div class="card-body p-0">';
                    
                    if (matchingItems.length > 0) {
                        resultsHtml += '<div class="list-group list-group-flush">';
                        matchingItems.forEach((matchResult, index) => {
                            const item = matchResult;
                            const score = item.score;
                            const matchedKeywords = item.matchedKeywords || [];
                            
                            // Determine score class
                            let scoreClass = 'score-low';
                            if (score >= 0.8) scoreClass = 'score-high';
                            else if (score >= 0.5) scoreClass = 'score-medium';
                            
                            resultsHtml += '<div class="list-group-item p-3' + (index % 2 === 0 ? ' bg-light' : '') + '">';
                            
                            // Item header with score
                            resultsHtml += '<div class="d-flex justify-content-between align-items-center mb-2">';
                            resultsHtml += '<h6 class="mb-0 font-weight-bold">' + (item.itemId || 'Unknown Item') + '</h6>';
                            resultsHtml += '<span class="score-pill ' + scoreClass + '">' + Math.round(score * 100) + '% match</span>';
                            resultsHtml += '</div>';
                            
                            // Item details
                            resultsHtml += '<div class="mb-2">';
                            resultsHtml += '<div><strong>Display Name:</strong> ' + (item.displayName || 'N/A') + '</div>';
                            resultsHtml += '<div><strong>Vendor Code:</strong> ' + (item.vendorCode || 'N/A') + '</div>';
                            resultsHtml += '<div><strong>Category:</strong> ' + (item.category || 'N/A') + '</div>';
                            resultsHtml += '</div>';
                            
                            // Matched keywords
                            resultsHtml += '<div class="mt-2">';
                            resultsHtml += '<strong>Matched Keywords:</strong> ';
                            if (matchedKeywords.length > 0) {
                                resultsHtml += '<div class="d-flex flex-wrap mt-1">';
                                matchedKeywords.forEach(keyword => {
                                    resultsHtml += '<span class="badge badge-info m-1">' + keyword + '</span>';
                                });
                                resultsHtml += '</div>';
                            } else {
                                resultsHtml += '<span class="text-muted">None</span>';
                            }
                            resultsHtml += '</div>';
                            
                            // Action buttons
                            resultsHtml += '<div class="mt-3">';
                            resultsHtml += '<a href="javascript:void(0)" class="action-link view-link" data-item-id="' + item.id + '">' +
                                '<i class="fas fa-eye"></i> View</a> ';
                            resultsHtml += '<a href="javascript:void(0)" class="action-link verify-link" ' +
                                'data-item-id="' + item.id + '" data-bid-item-id="' + jQuery('#testKeywordModal').data('bid-row-id') + '">' +
                                '<i class="fas fa-check"></i> Verify</a>';
                            resultsHtml += '</div>';
                            
                            resultsHtml += '</div>';
                        });
                        resultsHtml += '</div>';
                    } else {
                        resultsHtml += '<div class="p-4 text-center">';
                        resultsHtml += '<p class="text-muted mb-0">No matching items found.</p>';
                        resultsHtml += '</div>';
                    }
                    
                    resultsHtml += '</div></div>';
                    
                    // Display the results
                    jQuery('#testKeywordResults').html(resultsHtml);
                } else {
                    jQuery('#testKeywordResults').html('<div class="alert alert-danger">Text matcher not available</div>');
                }
            });
        `;
    }

    /**
     * GET Request Function - Compose a string that contains the CSS styles
     */
    function cssStringStyles() {
        return `
            <style>
                .dt-search label,
                .dt-search input {
                    font-size: 14px !important;
                }

                .dt-input {
                    margin-right: 7px !important;
                }

                .dt-length label,
                .dt-length select {
                    font-size: 14px !important;
                }

                .dt-info {
                    font-size: 13px !important;
                }

                .dt-paging {
                    font-size: 13px !important;
                }

                .match-table {
                    border: 2px solid #bbb;
                }

                .match-table th {
                    background-color: #e6e6e6;
                    border: 1px solid #aaa; /* Darker border color */
                    padding: 10px;
                    font-weight: bold;
                    font-size: 13px;
                    vertical-align: middle;
                }

                .match-table td {
                    border: 1px solid #aaa; /* Darker border color */
                    padding: 12px;
                    vertical-align: top;
                    font-size: 14px;
                    min-height: 100px;
                }

                .score-pill {
                    display: inline-block;
                    padding: 2px 8px;
                    border-radius: 10px;
                    font-size: 12px;
                    font-weight: bold;
                    margin-left: 0;
                    white-space: nowrap;
                }

                .score-high { background-color: #d4edda; color: #155724; }
                .score-medium { background-color: #fff3cd; color: #856404; }
                .score-low { background-color: #f8d7da; color: #721c24; }

                .action-link {
                    display: inline-block;
                    margin-left: 4px;
                    padding: 4px 8px;
                    border-radius: 4px;
                    border: 1px solid #ccc;
                    background-color: #f8f9fa;
                    cursor: pointer;
                    text-decoration: none;
                    font-size: 12px;
                }

                .action-link:hover { background-color: #e9ecef; text-decoration: none; }
                .view-link { color: #0056b3; }
                .verify-link { color: #28a745; }
                .override-link { color: #000000; }
                .undo-override-link { color: #ff6b6b; }

                .truncated-description {
                    max-height: 40px;
                    overflow: hidden;
                    position: relative;
                    font-size: 14px;
                    line-height: 1.4;
                }

                .full-description { max-height: none; overflow: visible; }

                .show-more-btn {
                    color: #0056b3;
                    cursor: pointer;
                    font-size: 12px;
                    font-weight: bold;
                    margin-top: 5px;
                    display: inline-block;
                    text-decoration: underline;
                }

                .match-details {
                    position: relative;
                    min-height: 100px;
                    padding-bottom: 40px;
                    display: flex;
                    flex-direction: column;
                    height: 100%;
                }

                .item-info { flex-grow: 1; }

                .action-buttons {
                    position: absolute;
                    bottom: 5px;
                    left: 0;
                    right: 0;
                    display: flex;
                    justify-content: flex-end;
                    align-items: center;
                    padding: 5px 0;
                }

                .match-cell:hover .action-buttons { opacity: 1; }

                .match-cell .action-buttons {
                    opacity: 0;
                    transition: opacity 0.2s ease-in-out;
                }

                .search-results {
                    max-height: 50vh;
                    overflow-y: auto;
                    border: 1px solid #dee2e6;
                    border-radius: 4px;
                    background-color: white;
                    position: absolute;
                    z-index: 1000;
                    width: 100%;
                    top: 100%;
                }

                #itemSearchInput {
                    width: 100%;
                    position: relative;
                }

                .search-container {
                    position: relative;
                    width: 100%;
                }

                .search-result-item {
                    padding: 8px;
                    border-bottom: 1px solid #dee2e6;
                    cursor: pointer;
                }

                .search-result-item:hover { background-color: #e9ecef; }
                .match-table tbody tr:nth-child(odd) {
                    background-color: #f0f0f0;
                }

                .match-table tbody tr:nth-child(even) {
                    background-color: #ffffff;
                }

                .match-table tbody tr:hover {
                    background-color: #e8f4f8; /* Highlight on hover */
                }

                .match-table thead tr {
                    background-color: #e6e6e6;
                    border-bottom: 2px solid #ccc;
                }

                .modal-title {
                    font-size: 15px;
                    font-weight: bold;
                    margin: 0;
                }

                .modal-body {
                    padding: 20px;
                }

                .modal-body h6 {
                    font-size: 16px;
                    font-weight: bold;
                    margin-top: 0;
                    margin-bottom: 8px;
                }

                .modal-body p { font-size: 15px; line-height: 1.5; margin-bottom: 20px; }
                .modal-body .form-group { margin-bottom: 20px; }

                .modal-body label {
                    font-size: 16px;
                    font-weight: 600;
                    margin-bottom: 8px;
                    display: block;
                }

                .modal-body input { font-size: 15px; padding: 8px 12px; height: auto; }

                .modal-footer {
                    padding: 15px 20px;
                    border-top: 1px solid #dee2e6;
                }

                .modal-footer .btn {
                    font-size: 15px;
                    padding: 8px 16px;
                    min-width: 100px;
                }

                .verified-row {
                    background-color: #d4edda !important;
                    transition: background-color 0.3s ease;
                    border: 2px solid #28a745 !important;
                }

                .verified-pill {
                    background-color: #28a745 !important;
                    color: white !important;
                    font-weight: bold;
                }

                .unselected-match:not(.action-link) {
                    background-color: #f0f0f0 !important;
                    opacity: 0.5 !important
                }

                .unselected-match .unselected-pill {
                    background-color: #d0d0d0 !important;
                    color: #707070 !important;
                }

                .filter-controls {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    margin-bottom: 15px;
                    padding: 8px 0;
                }

                .custom-switch {
                    padding-left: 2.5rem;
                }

                .custom-control-input:checked ~ .custom-control-label::before {
                    background-color: #28a745;
                    border-color: #28a745;
                }

                .custom-control-label {
                    font-size: 14px;
                    cursor: pointer;
                    padding-top: 2px;
                }

                #overrideModal .modal-header {
                    align-items: center;
                    padding: 0.75rem 1rem;
                    border-bottom: 1px solid #dee2e6;
                }

                #overrideModal .modal-footer {
                    padding: 0.75rem 1rem;
                    border-top: 1px solid #dee2e6;
                }

                #overrideModal label,
                #overrideModal .search-result-item strong {
                    font-size: 1.1rem;
                }

                #overrideModal .search-result-item {
                    padding: 0.75rem !important;
                }

                #overrideModal .search-result-item .small {
                    font-size: 0.95rem !important;
                }

                #overrideModal .badge {
                    font-size: 0.8rem;
                    padding: 0.4rem 0.4rem;
                    font-weight: normal;
                }

                #overrideModal #selectedItemDetails,
                #overrideModal #selectedItemName,
                #overrideModal #selectedItemId {
                    font-size: 1.1rem;
                }

                #itemSearchInput {
                    font-size: 1.1rem;
                    padding: 0.5rem;
                    height: auto;
                }

                #overrideModal .btn {
                    font-size: 1.1rem;
                    padding: 0.5rem 1rem;
                }

                .admin-tools {
                    margin-top: 5px;
                    margin-bottom: 1px;
                }

                .updating-row td {
                    background-color: rgba(200, 200, 200, 0.3) !important;
                }

                .updating-row .action-link,
                .updating-row .btn {
                    opacity: 0.5;
                    cursor: not-allowed;
                }
                .row-spinner-overlay {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background-color: rgba(255, 255, 255, 0.7);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    z-index: 1000;
                }

                .updating-row {
                    position: relative;
                    opacity: 0.5;
                    pointer-events: none;
                }

                /* Customer and Bid Sheet Info Styling */
                .text-muted.small {
                    font-size: 14px;
                }

                .d-flex.flex-column.flex-md-row strong {
                    font-size: 16px;
                }

                @media (max-width: 767.98px) {
                    .flex-column.flex-md-row > div:not(:first-child) {
                        margin-top: 0.5rem;
                    }
                }

                /* Custom larger modal size */
                @media (min-width: 1200px) {
                    #testMatchModal .modal-xl {
                        max-width: 95%; /* Increase from Bootstrap's default 1140px to 95% of viewport */
                        width: 95%;
                        margin: 1.75rem auto;
                    }
                }

                /* Make sure modal content has proper height */
                #testMatchModal .modal-content {
                    height: 90vh; /* 90% of viewport height */
                    display: flex;
                    flex-direction: column;
                }

                /* Allow modal body to scroll if content is too large */
                #testMatchModal .modal-body {
                    overflow-y: auto;
                    flex: 1 1 auto;
                    padding: 1.5rem;
                }

                /* Ensure table doesn't overflow horizontally */
                #testMatchModal .table-responsive {
                    overflow-x: auto;
                }

                /* Increase text size in test keyword modal */
                #testKeywordModal .card-body,
                #testKeywordModal .list-group-item,
                #testKeywordModal .badge,
                #testKeywordModal p,
                #testKeywordModal div {
                    font-size: 14px !important;
                }

                #testKeywordModal .card-header h5 {
                    font-size: 18px !important;
                }

                #testKeywordModal h6 {
                    font-size: 16px !important;
                }

                #testKeywordModal .badge {
                    font-size: 13px !important;
                    padding: 5px 8px;
                }

                #testKeywordModal .modal-title {
                    font-size: 22px !important;
                }

                #testKeywordCustomerDescription {
                    font-size: 16px !important;
                    line-height: 1.5;
                }

                /* Make badges more readable */
                #testKeywordModal .badge-light {
                    background-color: #f8f9fa;
                    border: 1px solid #dee2e6;
                    color: #495057;
                    margin: 3px;
                    padding: 6px 10px;
                    font-weight: normal;
                }
            </style>
        `;
    }

    /**
     * GET Request Function - Compose a string that contains the Load JSON Data functions
     */
    function jsStringLoadJsonDataFunctions() {
        return /*js*/`
            // Load items JSON data from file cabinet
            function loadItemsJsonData() {
                var requestPayload = {
                    "function": "getItemsJsonData"
                };

                var xhr = new XMLHttpRequest();
                xhr.open('POST', '${scriptURL}', true);
                xhr.setRequestHeader('Accept', 'application/json');
                xhr.send(JSON.stringify(requestPayload));
                xhr.onload = function() {
                    if (xhr.status === 200) {
                        try {
                            const response = JSON.parse(xhr.response);
                            if(response.error === undefined && response.success) {
                                // Store items data
                                itemsJsonData = response.items;
                                console.log('Loaded items JSON data: ' + itemsJsonData.length + ' items');
                            } else {
                                console.error('Error loading items JSON data: ' + (response.error ? response.error.message : 'Unknown error'));
                            }
                        } catch (err) {
                            console.error('Unable to parse the items JSON data response: ' + err.message);
                        }
                    } else {
                        console.error("Error loading items JSON data: " + xhr.status);
                    }
                };
            }

            // Load item index JSON data from file cabinet
            function loadItemIndexJsonData() {
                var requestPayload = {
                    "function": "getItemIndexJsonData"
                };

                var xhr = new XMLHttpRequest();
                xhr.open('POST', '${scriptURL}', true);
                xhr.setRequestHeader('Accept', 'application/json');
                xhr.send(JSON.stringify(requestPayload));
                xhr.onload = function() {
                    if (xhr.status === 200) {
                        try {
                            const response = JSON.parse(xhr.response);
                            if(response.error === undefined && response.success) {
                                // Store item index data
                                itemIndexJsonData = response.itemsIndexed;
                                console.log('Loaded item index JSON data');
                            } else {
                                console.error('Error loading item index JSON data: ' + (response.error ? response.error.message : 'Unknown error'));
                            }
                        } catch (err) {
                            console.error('Unable to parse the item index JSON data response: ' + err.message);
                        }
                    } else {
                        console.error("Error loading item index JSON data: " + xhr.status);
                    }
                };
            }

            // Load word frequency JSON data from file cabinet
            function loadWordFrequencyJsonData() {
                var requestPayload = {
                    "function": "getWordFrequencyJsonData"
                };

                var xhr = new XMLHttpRequest();
                xhr.open('POST', '${scriptURL}', true);
                xhr.setRequestHeader('Accept', 'application/json');
                xhr.send(JSON.stringify(requestPayload));
                xhr.onload = function() {
                    if (xhr.status === 200) {
                        try {
                            const response = JSON.parse(xhr.response);
                            if(response.error === undefined && response.success) {
                                // Store word frequency data
                                wordFrequencyJsonData = response.wordFrequencyMap;
                                console.log('Loaded word frequency JSON data');
                            } else {
                                console.error('Error loading word frequency JSON data: ' + (response.error ? response.error.message : 'Unknown error'));
                            }
                        } catch (err) {
                            console.error('Unable to parse the word frequency JSON data response: ' + err.message);
                        }
                    } else {
                        console.error("Error loading word frequency JSON data: " + xhr.status);
                    }
                };
            }

            // Load all JSON data files
            function loadAllJsonData() {
                loadItemsJsonData();
                loadItemIndexJsonData();
                loadWordFrequencyJsonData();
            }
        `;
    }

    /**
     * Admin Tools
     * GET Request Function - Compose a string that contains the Clear Verified and Overridden Items function
     */
    function jsStringclearVerifiedAndOverridenItemsRequestFunction() {
        return /*js*/`
            function clearVerifiedAndOverridenItemsRequest(bidSheetId) {
                if (!bidSheetId) {
                    alert('Missing bid sheet ID.');
                    return;
                }

                let paramsJsonObj = JSON.stringify({bidSheetId});

                var requestPayload = {
                    "function": "clearVerifiedAndOverridenItems",
                    "paramsJsonObj": paramsJsonObj,
                }

                var xhr = new XMLHttpRequest();
                xhr.open('POST', '${scriptURL}', true);
                xhr.setRequestHeader('Accept', 'application/json');
                xhr.send(JSON.stringify(requestPayload));
                xhr.onload = function() {
                    if (xhr.status === 200) {
                        try {
                            let response = JSON.parse(xhr.response);
                            if(response.error === undefined) {
                                alert('All verified and overridden items have been cleared.');
                                // Reload the bid sheet items to refresh the table
                                bidSheetItemsRequest(bidSheetId);
                            } else {
                                alert('Error: ' + response.error.message);
                            }
                        } catch (err) {
                            alert('Unable to parse the response.');
                        }
                    } else {
                        alert("Error: " + xhr.status);
                    }
                }
            }
        `;
    }

    /**
     * Admin Tools
     * POST Request Function - Clear all verified and overridden items
     */
    function clearVerifiedAndOverridenItems(context, requestPayload) {
        let responsePayload;

        try {
            let paramsJsonObj = JSON.parse(requestPayload.paramsJsonObj);
            let bidSheetId = paramsJsonObj.bidSheetId;

            // Get all bid sheet items with verified matches
            const bidSheetItemsQuery = `
                SELECT
                    id
                FROM
                    customrecord_spl_bid_sheet_items
                WHERE
                    custrecord_spl_bsi_bid_sheet = ? AND
                    custrecord_spl_bsi_item_match IS NOT NULL
            `;

            const bidSheetItems = query.runSuiteQL({
                query: bidSheetItemsQuery,
                params: [bidSheetId]
            }).asMappedResults();

            // Clear the item match field for each item
            for (let item of bidSheetItems) {
                record.submitFields({
                    type: 'customrecord_spl_bid_sheet_items',
                    id: item.id,
                    values: {
                        custrecord_spl_bsi_item_match: '',
                        custrecord_spl_bsi_match_override: false
                    }
                });
            }

            responsePayload = {
                success: true,
                clearedItems: bidSheetItems.length
            };
        } catch (err) {
            customErrorObject.throwError({
                summaryText: `ERROR_IN_CLEAR_VERIFIED_AND_OVERRIDEN_ITEMS`,
                error: err
            });

            responsePayload = { error: err };
        }

        context.response.write(JSON.stringify(responsePayload));
    }

    /**
     * POST Request Function - Get items JSON data from file cabinet
     */
    function getItemsJsonData(context) {
        let responsePayload;

        try {
            // Load the items JSON file from the file cabinet
            // The file ID is set to the value provided by the user
            const ITEMS_FILE_ID = 11221741; // Items file ID

            try {
                const itemsFile = file.load({
                    id: ITEMS_FILE_ID
                });

                const fileContent = itemsFile.getContents();
                const itemsData = JSON.parse(fileContent);

                responsePayload = {
                    success: true,
                    items: itemsData.items,
                    lastUpdated: itemsData.lastUpdated
                };
            } catch (fileErr) {
                // If file not found, return empty data
                log.error('Error loading items JSON file', fileErr);
                responsePayload = {
                    success: false,
                    error: {
                        message: 'Items data file not found or invalid',
                        details: fileErr.message
                    },
                    items: []
                };
            }
        } catch (err) {
            customErrorObject.throwError({
                summaryText: `ERROR_IN_GET_ITEMS_JSON_DATA`,
                error: err
            });

            responsePayload = {
                success: false,
                error: err
            };
        }

        context.response.write(JSON.stringify(responsePayload));
    }

    /**
     * POST Request Function - Get item index JSON data from file cabinet
     */
    function getItemIndexJsonData(context) {
        let responsePayload;

        try {
            // Load the item index JSON file from the file cabinet
            const ITEM_INDEX_FILE_ID = 11221742; // Item indexes file ID

            try {
                const itemIndexFile = file.load({
                    id: ITEM_INDEX_FILE_ID
                });

                const fileContent = itemIndexFile.getContents();
                const itemIndexData = JSON.parse(fileContent);

                responsePayload = {
                    success: true,
                    itemsIndexed: itemIndexData.itemsIndexed,
                    lastUpdated: itemIndexData.lastUpdated
                };
            } catch (fileErr) {
                // If file not found, return empty data
                log.error('Error loading item index JSON file', fileErr);
                responsePayload = {
                    success: false,
                    error: {
                        message: 'Item index data file not found or invalid',
                        details: fileErr.message
                    },
                    itemsIndexed: {}
                };
            }
        } catch (err) {
            customErrorObject.throwError({
                summaryText: `ERROR_IN_GET_ITEM_INDEX_JSON_DATA`,
                error: err
            });

            responsePayload = {
                success: false,
                error: err
            };
        }

        context.response.write(JSON.stringify(responsePayload));
    }

    /**
     * POST Request Function - Get word frequency JSON data from file cabinet
     */
    function getWordFrequencyJsonData(context) {
        let responsePayload;

        try {
            // Load the word frequency JSON file from the file cabinet
            const WORD_FREQUENCY_FILE_ID = 11221743;

            try {
                const wordFrequencyFile = file.load({
                    id: WORD_FREQUENCY_FILE_ID
                });

                const fileContent = wordFrequencyFile.getContents();
                const wordFrequencyData = JSON.parse(fileContent);

                responsePayload = {
                    success: true,
                    wordFrequencyMap: wordFrequencyData.wordFrequencyMap,
                    lastUpdated: wordFrequencyData.lastUpdated
                };
            } catch (fileErr) {
                // If file not found, return empty data
                log.error('Error loading word frequency JSON file', fileErr);
                responsePayload = {
                    success: false,
                    error: {
                        message: 'Word frequency data file not found or invalid',
                        details: fileErr.message
                    },
                    wordFrequencyMap: {}
                };
            }
        } catch (err) {
            customErrorObject.throwError({
                summaryText: `ERROR_IN_GET_WORD_FREQUENCY_JSON_DATA`,
                error: err
            });

            responsePayload = {
                success: false,
                error: err
            };
        }

        context.response.write(JSON.stringify(responsePayload));
    }

    return {
        onRequest: onRequest
    };
});
