/**
 * @description File utilities for bid sheet review functionality
 *
 * @NApiVersion 2.1
 *
 * <AUTHOR>
 * @module spl_bid_sheet_review_file_utils_class
 */
define([
    "exports",
    "../../../../Classes/vlmd_custom_error_object",
    "N/record",
    "N/file",
    "N/query",
    "N/runtime"
], (
    /** @type {any} */ exports,
    /** @type {any} */ CustomErrorObject,
    /** @type {any} */ record,
    /** @type {any} */ file,
    /** @type {any} */ query
) => {

    class BidSheetReviewFileUtils {
        constructor(dataAccess) {
            this.customErrorObject = new CustomErrorObject();
            this.dataAccess = dataAccess;
        }

        /**
         * Sanitize input for CSV
         * @param {string} value - The value to sanitize
         * @returns {string} - Sanitized value
         */
        sanitizeForCSV(value) {
            // Convert to string, handle null/undefined
            const str = (value ?? '').toString();
            // Escape quotes by doubling them and wrap in quotes
            return `"${str.replace(/"/g, '""')}"`;
        }

        /**
         * Generate CSV file for bid sheet
         * @param {string} bidSheetId - The ID of the bid sheet
         * @param {string} fileName - The name of the file
         * @returns {Object} - The generated file
         */
        generateFile(bidSheetId, fileName) {
            try {
                const bidSheetRecord = record.load({
                    type: 'customrecord_spl_bid_sheets',
                    id: bidSheetId
                });

                const customerId = bidSheetRecord.getValue({ fieldId: 'custrecord_spl_bid_sheets_customer' });
                const originalFileId = bidSheetRecord.getValue('custrecord_spl_bid_sheets_file');
                const originalFile = file.load({ id: originalFileId });
                const headers = originalFile.lines.iterator().next().value;
                const matchHeaders = [
                    'SPL Item ID',
                    'SPL Vendor Code',
                    'SPL Item Name',
                    'SPL Item UOM',
                    'SPL Item Price',
                    'SPL Base Unit Price',
                    'SPL Purchase Price'
                ].join(',');

                const verifiedBidSheetFile = file.create({
                    name: `${fileName}.csv`,
                    fileType: file.Type.CSV,
                    contents: `${headers},${matchHeaders}\n`
                });
                const folderId = runtime.getCurrentScript().getParameter({ name: 'custscript_bid_sheet_folder_id' });
                verifiedBidSheetFile.folder = folderId;

                const verifiedItemsQuery = /*sql*/`
                    SELECT
                        ROWNUM AS row_number,
                        bsi.id AS bid_sheet_item_id,
                        bsi.custrecord_spl_bsi_row_data AS customer_item_description,
                        bsi.custrecord_spl_bsi_item_match AS spl_item_internal_id,
                        item.itemid AS spl_item_id,
                        item.vendorname AS spl_vendor_code,
                        item.displayname AS spl_display_name,
                        item.lastpurchaseprice AS spl_purchase_price,
                        item.saleunit AS spl_item_sales_unit_id,
                        uom.unitname AS spl_item_uom,
                    FROM
                        customrecord_spl_bid_sheet_items bsi
                    LEFT JOIN
                        item ON item.id = bsi.custrecord_spl_bsi_item_match
                    INNER JOIN
                        unitsTypeUom uom ON uom.internalid = item.purchaseunit
                    WHERE
                        bsi.custrecord_spl_bsi_bid_sheet = ?
                `;

                const verifiedItems = query.runSuiteQL({
                    query: verifiedItemsQuery,
                    params: [bidSheetId]
                }).asMappedResults();

                const itemIds = verifiedItems.map(item => item.spl_item_internal_id);
                const customerItemRates = this.dataAccess.getCustomerItemPrice(customerId, itemIds);
                const customerItemRateMap = new Map(customerItemRates.map(rate => [rate.item_internal_id, rate.rate]));

                verifiedItems.forEach(item => {
                    const customerItemPrice = customerItemRateMap.get(item.spl_item_internal_id) || '';
                    const splItemBaseUOMPrice = this.getBasePrice({
                        uomId: item.spl_item_sales_unit_id,
                        basePrice: customerItemPrice
                    }) || '';
                    
                    const newRow = [
                        item.customer_item_description,
                        this.sanitizeForCSV(item.spl_item_id),
                        this.sanitizeForCSV(item.spl_vendor_code),
                        this.sanitizeForCSV(item.spl_display_name),
                        this.sanitizeForCSV(item.spl_item_uom),
                        this.sanitizeForCSV(customerItemPrice),
                        this.sanitizeForCSV(splItemBaseUOMPrice),
                        this.sanitizeForCSV(item.spl_purchase_price)
                    ];
                    verifiedBidSheetFile.appendLine({ value: newRow.join(',') });
                });

                const verifiedBidSheetFileId = verifiedBidSheetFile.save();

                bidSheetRecord.setValue({
                    fieldId: 'custrecord_spl_bid_sheets_verified_file',
                    value: verifiedBidSheetFileId
                });

                bidSheetRecord.save();

                log.audit('Verified Bid Sheet File Saved', `Bid Sheet Record ID: ${bidSheetId}, File ID: ${verifiedBidSheetFileId}`);

                return verifiedBidSheetFile;
            } catch (err) {
                this.customErrorObject.throwError({
                    summaryText: `ERROR_IN_GENERATE_FILE`,
                    error: err,
                });
                throw err;
            }
        }


        getBasePrice(options = {}) {
            const { 
                uomId = '',
                basePrice = 0.00,
            } = options;

            if (!uomId) return '';
            if (!basePrice) return '';
            
            const uomQuery = /*sql*/`
                SELECT
                    conversionrate
                FROM
                    unitstypeuom
                WHERE
                    internalid = ?
            `;
            const uomResult = query.runSuiteQL({
                query: uomQuery,
                params: [uomId]
            }).asMappedResults()?.[0];

            const conversionRate = uomResult?.conversionrate || 1;
            
            return basePrice / conversionRate;
        }
    }

    // Export the class
    exports.BidSheetReviewFileUtils = BidSheetReviewFileUtils;

    return BidSheetReviewFileUtils;
});
