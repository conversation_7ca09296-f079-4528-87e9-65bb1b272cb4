/**
 * @description UI generator for bid sheet review functionality
 *
 * @NApiVersion 2.1
 *
 * <AUTHOR>
 * @module spl_bid_sheet_review_ui_generator_class
 */
define([
    "exports",
    "../../../../Classes/vlmd_custom_error_object",
    "N/runtime",
], (
    /** @type {any} */ exports,
    /** @type {any} */ CustomErrorObject,
    /** @type {any} */ runtime
) => {

    class BidSheetReviewUIGenerator {
        /**
         * @param {Object} [clientScripts] - Optional instance of BidSheetReviewClientScripts
         */
        constructor(clientScripts) {
            this.customErrorObject = new CustomErrorObject();
            this.clientScripts = clientScripts || null;
        }

        /**
         * Generate HTML for the UI
         * @param {string} bidSheetId - The ID of the bid sheet
         * @param {string} scriptURL - The URL of the script
         * @param {Object} bidSheetInformation - Bid sheet information
         * @returns {string} - HTML string
         */
        generateHTML(bidSheetId, scriptURL, bidSheetInformation) {
            return /*html*/ `
                <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css">
                <link rel="stylesheet" href="https://code.jquery.com/ui/1.13.2/themes/base/jquery-ui.css">
                <link rel="stylesheet" href="https://cdn.datatables.net/2.3.0/css/dataTables.dataTables.min.css">
                <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
                <script src="https://code.jquery.com/jquery-3.7.1.js"></script>
                <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.3/umd/popper.min.js"></script>
                <script src="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/4.5.2/js/bootstrap.min.js"></script>
                <script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js"></script>
                <script src="https://cdn.datatables.net/2.3.0/js/dataTables.js"></script>

                ${this.cssStringStyles()}
                ${this.htmlUI(bidSheetId, bidSheetInformation)}

                ${this.htmlOverrideModal()}

                <script>
                    var bidSheetItemsResponsePayload;
                    var verifyMatchResponsePayload;
                    var allItemsCache = [];
                    var itemsIndexed = {}; // For faster lookups by ID
                    var bidSheetId = ${bidSheetId};
                    var table; // DataTable instance

                    ${this.clientScripts ? this.clientScripts.jsStringBidSheetItemsRequestFunction(scriptURL) : '/* Client scripts not provided */'}
                    ${this.clientScripts ? this.clientScripts.jsStringLoadAllItemsRequestFunction(scriptURL) : ''}
                    ${this.clientScripts ? this.clientScripts.jsStringclearVerifiedAndOverridenItemsRequestFunction(scriptURL) : ''}
                    ${this.clientScripts ? this.clientScripts.jsStringRenderBidSheetItemsTableFunction() : ''}
                    ${this.clientScripts ? this.clientScripts.jsStringEventHandlerFunction(scriptURL) : ''}
                    ${this.clientScripts ? this.clientScripts.jsStringDataTableLogic() : ''}
                    ${this.clientScripts ? this.clientScripts.jsStringModalLogic(scriptURL) : ''}

                    // Initialize UI components when document is ready
                    jQuery(document).ready(function() {
                        $('#clearVerifiedAndOverridenItems').on('click', function() {
                            if (confirm('Are you sure you want to clear all verified and overridden item matches? This action cannot be undone.')) {
                                clearVerifiedAndOverridenItemsRequest(bidSheetId);
                            }
                        });

                        bidSheetItemsRequest(bidSheetId);
                        loadAllItems(); // Load all items into cache
                        setupEventHandlers();
                    });

                </script>
            `;
        }

        /**
         * Generate HTML for the UI
         * @param {string} bidSheetId - The ID of the bid sheet
         * @param {Object} bidSheetInformation - Bid sheet information
         * @returns {string} - HTML string
         */
        htmlUI(bidSheetId, bidSheetInformation) {
            let userRole = runtime.getCurrentUser().role;
            let htmlUI = new String();

            if(userRole == 3 || userRole == 1197) {
                htmlUI += /*html*/`
                    <div class="alert alert-info" role="alert" id="adminTools">
                        <div><strong>Admin Tools:</strong></div>
                        <div style="margin-top: 5px;">
                            <button type="button" id="clearVerifiedAndOverridenItems" class="btn btn-outline-danger btn-sm">Clear Verified and Overridden Items</button>
                        </div>
                    </div>
                    <hr>
                `;
            }


            if(!bidSheetId) {
                htmlUI += /*html*/`
                    <div class="container-fluid" style="width: 100%; max-width: 100%; padding: 0;">
                        <div id="items-container" style="width: 100%; max-width: 100%;">
                            <h1 style="text-align: center; margin-top: 50px;">No Bid Sheet ID provided</h1>
                        </div>
                    </div>
                `;
            } else {
                if (bidSheetInformation) {
                    htmlUI += /*html*/`
                        <div class="mb-3">
                          <div class="d-flex flex-column flex-md-row justify-content-between">
                            <div class="mr-md-5">
                                <span class="text-muted small">Bid Sheet Number:</span>
                                <span class="ml-1"><strong>${bidSheetInformation.name}</strong></span>
                            </div>
                            <div class="mr-md-5">
                                <span class="text-muted small">Bid Sheet Total Items:</span>
                                <span class="ml-1"><strong>${bidSheetInformation.item_total}</strong></span>
                            </div>
                            <div class="mr-md-5">
                                <span class="text-muted small">Customer:</span>
                                <span class="ml-1"><strong>(${bidSheetInformation.entityid}) ${bidSheetInformation.companyname}</strong></span>
                            </div>
                            <div>
                                <span class="text-muted small">Bid Sheet Date:</span>
                                <span class="ml-1"><strong>${bidSheetInformation.created}</strong></span>
                            </div>
                          </div>
                          <hr>
                        </div>
                    `;
                }

                // Button container with toggle
                htmlUI += /*html*/`
                    <div class="container-fluid" style="width: 100%; max-width: 100%; padding: 0;">
                        <div class="row justify-content-center">
                            <div class="col-12 col-xl-11">
                                <div id="button-container" class="mx-auto d-flex justify-content-between align-items-center mb-4 w-100">
                                    <div class="btn-download-group">
                                        <button type="button" class="btn btn-success mr-2 file-download-btn" id="download-csv-btn" data-file-type="csv">Download CSV</button>
                                    </div>
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input" id="show-unverified-only">
                                        <label class="custom-control-label" for="show-unverified-only">Unverified rows only</label>
                                        </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                // Datatable
                htmlUI += /*html*/`
                    <div class="container-fluid" style="width: 100%; max-width: 100%; padding: 0;">
                        <div class="row justify-content-center">
                            <div class="col-12 col-xl-11">
                                <div id="matches-container" class="mx-auto" style="width: 100%; max-width: 100%;">
                                    <div class="row justify-content-md-center" style="margin-top: 50px; margin-bottom: 50px;">
                                        <div class="col-md-auto text-center">
                                            <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
                                                <span class="sr-only">Loading...</span>
                                            </div>
                                            <div style="margin-top: 15px; font-size: 16px;">
                                                <strong>Loading bid sheet items...</strong>
                                                <p class="text-muted">This may take a moment while we retrieve and process the data.</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }

            return htmlUI;
        }

        /**
         * Generate HTML for the override modal
         * @returns {string} - HTML string
         */
        htmlOverrideModal() {
            return /*html*/`
                <!-- Override Modal -->
                <div class="modal fade" id="overrideModal" tabindex="-1" role="dialog" aria-labelledby="overrideModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h2 class="modal-title" id="overrideModalLabel">Override Item Match</h2>
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="form-group">
                                    <label>Customer Item Description:</label>
                                    <p id="customerItemDescription"></p>
                                </div>
                                <div class="form-group">
                                    <label for="itemSearchInput">Search for an item to override matches:</label>
                                    <div class="search-container">
                                        <input type="text" class="form-control" id="itemSearchInput" placeholder="Enter item ID, name, or description">
                                        <div id="searchResults" class="search-results" style="display: none;"></div>
                                    </div>
                                </div>
                                <div id="selectedItemDetails" style="display: none;">
                                    <h6>Selected Item:</h6>
                                    <div class="card">
                                        <div class="card-body">
                                            <h5 class="card-title" id="selectedItemName"></h5>
                                            <p class="card-text" id="selectedItemId"></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                                <button type="button" class="btn btn-success" id="confirmOverrideBtn" disabled>Confirm Override</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        /**
         * Generate CSS styles
         * @returns {string} - CSS string
         */
        cssStringStyles() {
            return `
                <style>
                    .dt-search label,
                    .dt-search input {
                        font-size: 14px !important;
                    }

                    .dt-input {
                        margin-right: 7px !important;
                    }

                    .dt-length label,
                    .dt-length select {
                        font-size: 14px !important;
                    }

                    .dt-info {
                        font-size: 13px !important;
                    }

                    .dt-paging {
                        font-size: 13px !important;
                    }

                    .match-table {
                        border: 2px solid #bbb;
                    }

                    .match-table th {
                        background-color: #e6e6e6;
                        border: 1px solid #aaa; /* Darker border color */
                        padding: 10px;
                        font-weight: bold;
                        font-size: 13px;
                        vertical-align: middle;
                    }

                    .match-table td {
                        border: 1px solid #aaa; /* Darker border color */
                        padding: 12px;
                        vertical-align: top;
                        font-size: 14px;
                        min-height: 100px;
                    }

                    .score-pill {
                        display: inline-block;
                        padding: 2px 8px;
                        border-radius: 10px;
                        font-size: 12px;
                        font-weight: bold;
                        margin-left: 0;
                        white-space: nowrap;
                    }

                    .score-high { background-color: #d4edda; color: #155724; }
                    .score-medium { background-color: #fff3cd; color: #856404; }
                    .score-low { background-color: #f8d7da; color: #721c24; }

                    .action-link {
                        display: inline-block;
                        padding: 6px 10px;
                        border-radius: 4px;
                        margin-right: 5px;
                        margin-bottom: 5px;
                        text-align: center;
                        text-decoration: none !important;
                        font-size: 12px !important;
                        min-width: 120px;
                        width: 120px;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        border: 1px solid #dee2e6; /* Add border */
                        background-color: #f8f9fa; /* Light background */
                        transition: all 0.2s ease; /* Smooth transition for hover effects */
                    }

                    .action-link:hover {
                        background-color: #e9ecef; /* Slightly darker on hover */
                        text-decoration: none; /* Remove underline on hover */
                    }

                    .view-link {
                        color: #0056b3;
                        border-color: #0056b3;
                    }
                    .view-link:hover {
                        color: #0056b3;
                        background-color: #e6f0ff;
                    }

                    .verify-link {
                        color: #28a745;
                        border-color: #28a745;
                    }
                    .verify-link:hover {
                        color: #28a745;
                        background-color: #e6ffe6;
                    }

                    .override-link {
                        color: #000000;
                        border-color: #6c757d;
                    }
                    .override-link:hover {
                        color: #000000;
                        background-color: #e9ecef;
                    }

                    .undo-override-link {
                        color: #ff6b6b;
                        border-color: #ff6b6b;
                    }
                    .undo-override-link:hover {
                        color: #ff6b6b;
                        background-color: #ffe6e6;
                    }

                    .skip-link {
                        color: #ff9800;
                        border-color: #ff9800;
                    }
                    .skip-link:hover {
                        color: #ff9800;
                        background-color: #fff3e0;
                    }

                    .truncated-description {
                        max-height: 40px;
                        overflow: hidden;
                        position: relative;
                        font-size: 14px;
                        line-height: 1.4;
                    }

                    .full-description { max-height: none; overflow: visible; }

                    .show-more-btn {
                        color: #0056b3;
                        cursor: pointer;
                        font-size: 12px;
                        font-weight: bold;
                        margin-top: 5px;
                        display: inline-block;
                        text-decoration: underline;
                    }

                    .match-details {
                        position: relative;
                        min-height: 100px;
                        display: flex;
                        flex-direction: column;
                        height: 100%;
                    }

                    .item-info { flex-grow: 1; }

                    .action-buttons {
                        margin-top: 10px;
                        display: flex;
                        justify-content: flex-start;
                        align-items: center;
                        padding: 5px 0;
                        gap: 5px;
                    }

                    .match-cell:hover .action-buttons { opacity: 1; }

                    .match-cell .action-buttons {
                        opacity: 0;
                        transition: opacity 0.2s ease-in-out;
                    }

                    .search-results {
                        max-height: 50vh;
                        overflow-y: auto;
                        border: 1px solid #dee2e6;
                        border-radius: 4px;
                        background-color: white;
                        position: absolute;
                        z-index: 1000;
                        width: 100%;
                        top: 100%;
                    }

                    #itemSearchInput {
                        width: 100%;
                        position: relative;
                    }

                    .search-container {
                        position: relative;
                        width: 100%;
                    }

                    .search-result-item {
                        padding: 8px;
                        border-bottom: 1px solid #dee2e6;
                        cursor: pointer;
                    }

                    .search-result-item:hover { background-color: #e9ecef; }
                    .match-table tbody tr {
                        background-color: #f0f7ff;
                    }

                    .match-table tbody tr:hover {
                        background-color: #e8f4f8; /* Highlight on hover */
                    }

                    .match-table thead tr {
                        background-color: #e6e6e6;
                    }

                    .modal-title {
                        font-size: 15px;
                        font-weight: bold;
                        margin: 0;
                    }

                    .modal-body {
                        padding: 20px;
                    }

                    .modal-body h6 {
                        font-size: 16px;
                        font-weight: bold;
                        margin-top: 0;
                        margin-bottom: 8px;
                    }

                    .modal-body p { font-size: 15px; line-height: 1.5; margin-bottom: 20px; }
                    .modal-body .form-group { margin-bottom: 20px; }

                    .modal-body label {
                        font-size: 16px;
                        font-weight: 600;
                        margin-bottom: 8px;
                        display: block;
                    }

                    .modal-body input { font-size: 15px; padding: 8px 12px; height: auto; }

                    /* Button container to ensure proper spacing */
                    .modal-footer {
                        display: flex;
                        justify-content: flex-end;
                        gap: 10px; /* Consistent spacing between buttons */
                        padding: 15px 20px;
                        border-top: 1px solid #dee2e6;
                    }

                    .modal-footer .btn {
                        font-size: 15px;
                        padding: 8px 16px;
                        min-width: 100px;
                    }

                    /* Ensure buttons don't collapse on small screens */
                    @media (max-width: 576px) {
                        .modal-footer {
                            flex-direction: column;
                        }

                        .modal-footer .btn {
                            margin-left: 0 !important;
                        }
                    }

                    .verified-row {
                        background-color: #d4edda !important;
                        transition: background-color 0.3s ease;
                        border: 2px solid #28a745 !important;
                    }

                    .verified-pill {
                        background-color: #28a745 !important;
                        color: white !important;
                        font-weight: bold;
                    }

                    .unselected-match:not(.action-link) {
                        background-color: #f0f0f0 !important;
                        opacity: 0.5 !important
                    }

                    .unselected-match .unselected-pill {
                        background-color: #d0d0d0 !important;
                        color: #707070 !important;
                    }

                    .filter-controls {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        margin-bottom: 15px;
                        padding: 8px 0;
                    }

                    .custom-switch {
                        padding-left: 2.5rem;
                    }

                    .custom-control-input:checked ~ .custom-control-label::before {
                        background-color: #28a745;
                        border-color: #28a745;
                    }

                    .custom-control-label {
                        font-size: 14px;
                        cursor: pointer;
                        padding-top: 2px;
                    }

                    #overrideModal .modal-header {
                        align-items: center;
                        padding: 0.75rem 1rem;
                        border-bottom: 1px solid #dee2e6;
                    }

                    #overrideModal .modal-footer {
                        padding: 0.75rem 1rem;
                        border-top: 1px solid #dee2e6;
                    }

                    #overrideModal label,
                    #overrideModal .search-result-item strong {
                        font-size: 1.1rem;
                    }

                    #overrideModal .search-result-item {
                        padding: 0.75rem !important;
                    }

                    #overrideModal .search-result-item .small {
                        font-size: 0.95rem !important;
                    }

                    #overrideModal .badge {
                        font-size: 0.8rem;
                        padding: 0.4rem 0.4rem;
                        font-weight: normal;
                    }

                    #overrideModal #selectedItemDetails,
                    #overrideModal #selectedItemName,
                    #overrideModal #selectedItemId {
                        font-size: 1.1rem;
                    }

                    #itemSearchInput {
                        font-size: 1.1rem;
                        padding: 0.5rem;
                        height: auto;
                    }

                    #overrideModal .btn {
                        font-size: 1.1rem;
                        padding: 0.5rem 1rem;
                    }

                    .admin-tools {
                        margin-top: 5px;
                        margin-bottom: 1px;
                    }

                    .updating-row td {
                        background-color: rgba(200, 200, 200, 0.3) !important;
                    }

                    .updating-row .action-link,
                    .updating-row .btn {
                        opacity: 0.5;
                        cursor: not-allowed;
                    }
                    .row-spinner-overlay {
                        position: absolute;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        background-color: rgba(255, 255, 255, 0.7);
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        z-index: 1000;
                    }

                    .updating-row {
                        position: relative;
                        opacity: 0.5;
                        pointer-events: none;
                    }

                    /* Customer and Bid Sheet Info Styling */
                    .text-muted.small {
                        font-size: 14px;
                    }

                    .d-flex.flex-column.flex-md-row strong {
                        font-size: 16px;
                    }

                    @media (max-width: 767.98px) {
                        .flex-column.flex-md-row > div:not(:first-child) {
                            margin-top: 0.5rem;
                        }
                    }

                    /* Ensure consistent button sizing in modal footer */
                    #overrideModal .modal-footer .btn {
                        font-size: 15px;
                        padding: 8px 16px;
                        min-width: 120px;
                        width: 120px; /* Fixed width */
                    }

                    /* Responsive adjustments for smaller screens */
                    @media (max-width: 576px) {
                        .action-link,
                        #overrideModal .modal-footer .btn {
                            width: 100%; /* Full width on very small screens */
                            min-width: 100px;
                            margin-bottom: 8px;
                        }
                    }
                </style>
            `;
        }
    }

    // Export the class
    exports.BidSheetReviewUIGenerator = BidSheetReviewUIGenerator;

    return BidSheetReviewUIGenerator;
});




