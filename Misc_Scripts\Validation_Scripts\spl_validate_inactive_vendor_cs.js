/**
 * 
 * Contains logic to prevent generating purchase order for
 * inactive Vendors
 * 
 * @NApiVersion 2.1
 * @NScriptType ClientScript
 * <AUTHOR>
 */

define([
	"N/search",
	"N/ui/message"
], function (
	search,
	message
) {

    /**
	 * Display banner message on save of purchase order
	 *
	 * @param {string} vendorName name of the vendor
	 * @returns {null} 
     * 
	 */
    
	const showInactiveVendorError = (vendorName) => {
        //Show Error when vendor is inactive
		message.create({
			title: "Inactive Vendor",
			message: "Vendor "+ vendorName + " is inactive",
			type: message.Type.ERROR
		}).show();
	}

	const saveRecord = (scriptContext) => {

		let currentRecord = scriptContext.currentRecord;
		let vendorId = currentRecord.getValue({fieldId: 'entity'});

        //only run this if vendor field is populated
		if(vendorId){
			let vendorName = currentRecord.getText({fieldId: 'entity'});

            //get isinactive status of vendor
			let vendorLookup = search.lookupFields({
				type: search.Type.VENDOR,
				id: vendorId,
				columns: ["isinactive"]
			});

            //prevent saving of purchase order if isinactive is true
			if(vendorLookup.isinactive)
			{
				showInactiveVendorError(vendorName);
				return false;
			}
		}

        return  true;
	}

	return {
		saveRecord
	};
});
