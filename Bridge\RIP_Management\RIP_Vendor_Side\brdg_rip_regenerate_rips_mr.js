/**
 * @description Takes in an item id and checks for all bills with this item and recreates all RIP information
 * or deletes/recreates rip info for one specific bill id passed in
 *
 * </br><b>Schedule:</b> Called by the check new rip UE script or by the regenerate rips ue (button on a vendor bill)
 *
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * @NAmdConfig /SuiteScripts/config.json
 *
 * <AUTHOR>
 * @module brdg_rip_regenerate_rips_mr
 */

define([
  "require",
  "N/log",
  "N/record",
  "N/runtime",
  "N/query",
  "N/email",
  "N/task",
  "../../../Classes/vlmd_custom_error_object",
  "../Libraries/brdg_rip_records_helper_lib",
], (/** @type {any} */ require) => {
  const log = require("N/log");
  const record = require("N/record");
  const runtime = require("N/runtime");
  const query = require("N/query");
  const email = require("N/email");
  const task = require("N/task");

  /**@type {import ("../../../Classes/vlmd_custom_error_object").CustomErrorObject}} */
  const CustomErrorObject = require("../../../Classes/vlmd_custom_error_object");
  const customErrorObject = new CustomErrorObject();

  /**@type {import ("../Libraries/brdg_rip_records_helper_lib")}} */
  const ripRecordLib = require("../Libraries/brdg_rip_records_helper_lib");

  function sendResultsEmail(resultsObj) {
    const { billsUpdatedSuccessfully, billsWithErrors } = resultsObj;
    const currentScript = runtime.getCurrentScript();

    const itemId = currentScript.getParameter({
      name: "custscript_updated_item_id",
    });

    if (!itemId) {
      return;
    }

    let emailText = `An <a href= '/app/common/item/item.nl?id=${itemId}'>item's </a>vendor code was updated, triggering bills with that item to be updated as well.<br /> `;
    if (billsUpdatedSuccessfully.length > 0) {
      let sucessTable = `<table style = "padding: 10px">
    <caption>${billsUpdatedSuccessfully.length} ${
        billsUpdatedSuccessfully.length > 1 ? " bills" : " bill"
      } updated successfully:</caption>
				<tr>
				<th  style = "border: 1px solid black; border-collapse: collapse">BILL</th>
				<th  style = "border: 1px solid black; border-collapse: collapse">RELATED VENDOR CREDIT</th>
				</tr>`;

      for (i = 0; i < billsUpdatedSuccessfully.length; i++) {
        ("<tr>");
        sucessTable += `<td style = "border: 1px solid black; border-collapse: collapse">
          <a href='/app/accounting/transactions/vendbill.nl?id=${
            billsUpdatedSuccessfully[i].billId
          }'> Vendor Bill </a>
						</td>
            <td style = "border: 1px solid black; border-collapse: collapse">
          ${
            billsUpdatedSuccessfully[i].relatedCreditDeleted != null
              ? `<a href='/app/accounting/transactions/vendbill.nl?id=${billsUpdatedSuccessfully[i].relatedCreditDeleted}'> Bill Credit </a> `
              : "No matching vendor credit"
          }
						</td>
            </tr>`;
      }
      sucessTable += "</table>";
      emailText += `<br/><br/>${sucessTable}`;
    }

    if (billsWithErrors.length > 0) {
      let errorTable = `<table style = "padding: 15px">
      <caption>${billsWithErrors.length} ${
        billsWithErrors.length > 1 ? " bills" : " bill"
      } could not update:</caption>
				<tr>
				<th  style = "border: 1px solid black; border-collapse: collapse">BILL</th>
				<th  style = "border: 1px solid black; border-collapse: collapse">ERROR</th>
				</tr>`;
      for (i = 0; i < billsWithErrors.length; i++) {
        ("<tr>");
        errorTable += `<td style = "border: 1px solid black; border-collapse: collapse">
          <a href='/app/accounting/transactions/vendbill.nl?id=${
            billsWithErrors[i].bill
          }'> Vendor Bill </a>
						</td>
            <td style = "border: 1px solid black; border-collapse: collapse">
          ${
            billsWithErrors[i].errorMessage ?? "No specific error message found"
          }
						</td>
            </tr>`;
      }
      errorTable += "</table>";
      emailText += `<br/><br/>${errorTable}`;
    }

    email.send({
      author: 223244, //Requests
      recipients: "<EMAIL>",
      body: emailText,
      subject: `Results from an updated vendor code on an item`,
    });
  }

  function getInputData(context) {
    //Get all the bills that have this (updated) item on it
    try {
      const currentScript = runtime.getCurrentScript();

      const itemId = currentScript.getParameter({
        name: "custscript_updated_item_id",
      });

      const billId = currentScript.getParameter({
        name: "custscript_specific_bill_id",
      });

      const vendorCreditId = currentScript.getParameter({
        name: "custscript_specific_vendor_credit_id",
      });

      if ((!itemId || itemId == "null") && (!billId || billId == "null")) {
        throw customErrorObject.updateError({
          errorType: customErrorObject.ErrorTypes.MISSING_VALUE,
          summary: "MISSING_ID",
          details: `No item or bill id found to update rips with!`,
        });
      }

      if (billId) {
        const billArr = [];
        billArr.push({ billId: billId, vendorCreditId: vendorCreditId });
        return { billArr };
      }
      const billsToUpdateQuery = `
    SELECT ID, custbody_associated_bill_credit FROM transaction where type = 'VendBill' and id in (
        SELECT transaction from transactionline where item = ?)
           `;
      return {
        type: "suiteql",
        query: billsToUpdateQuery,
        params: [itemId],
      };
    } catch (/** @type {any} */ err) {
      customErrorObject.throwError({
        summaryText: "6279b473-c471-42cd-9fec-84063a0aaa24: GET_INPUT_DATA_ERROR",
        error: err,
        recordId: null,
        recordName: null,
        recordType: null,
        errorWillBeGrouped: true,
      });
    }
  }

  function map(context) {
    const mapErrorObject = new CustomErrorObject();

    var parsedResult = JSON.parse(context.value);
    if (parsedResult[0] && parsedResult[0] != "") {
      var { billId, vendorCreditId } = parsedResult[0];
    } else {
      var billId = JSON.parse(context.value).values[0];
      vendorCreditId = JSON.parse(context.value).values[1];
    }
    if (!billId) {
      throw mapErrorObject.updateError({
        errorType: mapErrorObject.ErrorTypes.MISSING_VALUE,
        summary: "MISSING_BILL_ID_VALUE",
        details: "Could not find bill id to update.",
      });
    }
    try {
      //Delete all related rip records to this bill. Goes in order of dependent records
      //Get the rip accrual records - for each one delete all related tier quantity records and then delete the accrual record
      const returnedObj = ripRecordLib.deleteRipRecords(billId, vendorCreditId);

      // The BRDG RIP Create Records UE does not run on Map/Reduce context anymore.
      // We will offload the bill credit creation to an MR to cover for the limitations of running the process in a UE.
      const createCreditMrTask = task.create({
        taskType: task.TaskType.MAP_REDUCE,
        scriptId: "customscript_brdg_rip_create_credit_mr",
        params: {
          custscript_rip_create_credit_bill_id: billId
        }
      });
      const taskId = createCreditMrTask.submit();
      log.audit({
        title: "MAP: Submitting BRDG RIP Create Credit Records MR",
        details: taskId,
      });

      context.write({
        key: "Bill Updated",
        value: returnedObj,
      });
    } catch (/** @type {any} */ err) {
      context.write({
        key: "Bill Not Updated",
        value: { bill: billId, errorMessage: err.message },
      });
      mapErrorObject.throwError({
        summaryText: `MAP_ERROR`,
        error: err,
        recordId: billId,
        recordName: null,
        recordType: null,
        errorWillBeGrouped: true,
      });
    }
  }

  function summarize(context) {
    try {
      const billsUpdatedSuccessfully = [];
      const billsWithErrors = [];
      context.output.iterator().each(function (key, value) {
        switch (key) {
          case "Bill Updated":
            billsUpdatedSuccessfully.push(JSON.parse(value));
            break;

          case "Bill Not Updated":
            billsWithErrors.push(JSON.parse(value));
            break;
        }
        return true; //To continue iterating the context.output
      });

      if (billsUpdatedSuccessfully.length > 0 || billsWithErrors.length > 0) {
        log.audit("BILLS UPDATED SUCCESSFULLY", billsUpdatedSuccessfully);
        log.audit("BILLS WITH ERRORS", billsWithErrors);
        sendResultsEmail({ billsUpdatedSuccessfully, billsWithErrors });
      }
    } catch (/** @type {any} */ err) {
      customErrorObject.throwError({
        summaryText: "dcf12656-7dae-44af-9838-2c7af6ca22ef: SUMMARIZE_ERROR",
        error: err,
        recordId: null,
        recordName: null,
        recordType: null,
        errorWillBeGrouped: true,
      });
    }
  }
  return {
    getInputData,
    map,
    summarize,
  };
});
