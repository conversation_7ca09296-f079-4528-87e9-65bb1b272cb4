/**
 * @description Bridge RIP Vendor class for Royal Wine Corporation
 *
 * @NApiVersion 2.1
 * <AUTHOR>
 */

define([
  "exports",
  "require",
  "N/error",
  "./brdg_rip_vendor",
], function (/** @type {any} */ exports, /** @type {any} */ require) {
  const error = require("N/error");
  const { BridgeRIPVendor } = require("./brdg_rip_vendor");
  const LINE_LENGTH_THRESHOLD = 30;

  // Includes the added element for line count
  const ROW_LENGTH = 19;

  // Date Format e.g. 1/1/2025, 12/5/2020
  const DATE_FORMAT = /\b\d{1,2}\/\d{1,2}\/\d{4}\b/;

  /**
   * Bridge RIP Royal Class
   */
  class BridgeRIPRoyal extends BridgeRIPVendor {
    /** @param {{[key:string]: any}} props Constructor params */
    constructor(props) {
      super(props);
    }

    /**
     * Split the row string from the CSV file and attach the line index
     *
     * @param {import("N/file").File} ripFile NetSuite File
     * @returns {any[]} Row strings split by commas
     */
    splitLines(ripFile) {
      const fileIterator = ripFile.lines.iterator();

      let lineCount = 0;
      const expectedHeader =
        "RIP CODE,FROM DATE (MM/DD/YYYY),TO DATE (MM/DD/YYYY),RIP DESCRIPTION,SKU,R UNIT 1,R QTY 1,$ R AMT 1,R UNIT 2,R QTY 2,$ R AMT 2,R UNIT 3,R QTY 3,$ R AMT 3,R UNIT 4,R QTY 4,$ R AMT 4,COMMENTS INSTRUCTIONS EXPLANATIONS";
      let extractedHeader = "";

      // Since we iterate through the file from top to bottom, we cannot skip the header
      // Iterate over the header by returning false after reading it
      fileIterator.each((/** @type {any} */ header) => {
        extractedHeader = header.value.trim();
        return false;
      });

      if (expectedHeader !== extractedHeader) {
        throw this.customErrorObject.updateError({
          errorType: this.customErrorObject.ErrorTypes.INVALID_DATA,
          summary: "334cd5af-bfa1-4005-a3e5-c2b361adc0fd: SPLIT_LINES_ERROR",
          details: `The header from the input file (${extractedHeader}) doesn't match the expected header for Royal Wine Corporation (${expectedHeader}).`,
        });
      }

      // Continue reading the file, and store the contents to a data lines array
      const /** @type {any[]} */ dataArr = [];
      fileIterator.each((/** @type {any} */ line) => {
        //Likely ',,,,,,,,' etc. - an empty line that the file picked up to read.
        if (line.value.length <= LINE_LENGTH_THRESHOLD) {
          return;
        }

        const lineArr = line.value.trim().split(",");

        if (lineArr.length <= 1) {
          throw this.customErrorObject.updateError({
            errorType: this.customErrorObject.ErrorTypes.INVALID_DATA_TYPE,
            summary: "10744ef9-2610-46c2-b6a8-f1104b2169a5: INVALID_LINE_DATA",
            details: `Error with ${JSON.stringify(
              line.value
            )}. Check that there are no \\ or line spaces in any header cells`,
          });
        }

        dataArr.push([...lineArr, lineCount]);
        lineCount++;

        return true;
      });

      return dataArr;
    }

    /**
     * Create an object from the line extracted from the CSV file
     * - Account for missing UOM values
     * - Add rip level 5 since only 4 levels are provided
     * - Add a default value of 1 for countAs
     *
     * @param {any[]} fields Array of column values
     * @returns {{[key:string]: any}} Key-value pairs of column values
     */
    parseLine(fields) {
      if (fields.length !== ROW_LENGTH) {
        throw this.customErrorObject.updateError({
          errorType: this.customErrorObject.ErrorTypes.INVALID_DATA,
          summary:
            "15e9a2ee-8a6f-4fea-b177-5e86b8de76a0: INVALID_ROW_LENGTH",
          details: "Row should have exactly 18 values.",
        });
      }

      const fieldNames = [
        "ripCode",
        "fromDate",
        "toDate",
        "description",
        "sku",
        "uom1",
        "qty1",
        "amt1",
        "uom2",
        "qty2",
        "amt2",
        "uom3",
        "qty3",
        "amt3",
        "uom4",
        "qty4",
        "amt4",
        "comments",
        "lineCount",
      ];
      const trimIfString = (/** @type {any} */ x) =>
        typeof x === "string" ? x.trim() : x;
      const values = fields.map(trimIfString);
      const rowObj = Object.fromEntries(
        fieldNames.map((key, index) => [key, values[index]])
      );

      const fromDateMatch = rowObj.fromDate.match(DATE_FORMAT);
      if (!fromDateMatch) {
        this.customErrorObject.updateError({
          errorType: this.customErrorObject.ErrorTypes.SYNTAX_ERROR,
          summary:
            "53243bf7-c1f9-4a12-8260-52955d62c31a: FROMDATE_FORMAT_MISMATCH",
          details: `FROMDATE from the parsed row does not match the expected format: ${rowObj.fromDate}`,
        });
      }

      const toDateMatch = rowObj.toDate.match(DATE_FORMAT);
      if (!toDateMatch) {
        this.customErrorObject.updateError({
          errorType: this.customErrorObject.ErrorTypes.SYNTAX_ERROR,
          summary:
            "baa28e5b-3331-499b-a159-134515b2a432: TODATE_FORMAT_MISMATCH",
          details: `TODATE from the parsed row does not match the expected format: ${rowObj.toDate}`,
        });
      }

      // Columns might be misaligned because of missing UOM
      // UOM is always "CASE", disregard UOM value then retain non-empty values
      const ripLevelComponents = [
        rowObj.uom1,
        rowObj.qty1,
        rowObj.amt1,
        rowObj.uom2,
        rowObj.qty2,
        rowObj.amt2,
        rowObj.uom3,
        rowObj.qty3,
        rowObj.amt3,
        rowObj.uom4,
        rowObj.qty4,
        rowObj.amt4,
      ].filter(
        /** @type {string} */ (component) =>
          !!component && !component.toLowerCase().includes("case")
      );

      if (ripLevelComponents.length % 2 !== 0) {
        this.customErrorObject.updateError({
          errorType: this.customErrorObject.ErrorTypes.MISSING_VALUE,
          summary: "c3c8f396-f918-44eb-878e-aa96ea222f10: MISSING_VALUE",
          details: `Error with ${JSON.stringify(
            ripLevelComponents
          )}. RIP Level should have both amount ($ R AMT) and quantity (R QTY)`,
        });
      }

      if (this.customErrorObject.details) {
        this.customErrorObject.throwError({
          summaryText: "49f0fd22-0857-4197-bd5b-703f679da177: PARSING_ERROR",
          error: error.create({
            name: "PARSING_ERROR",
            message: `Errors enountered while parsing the row from the input file.`,
          }),
          recordId: null,
          recordName: null,
          recordType: null,
          errorWillBeGrouped: false,
        });
      }

      // Re-align the columns and add missing UOM "CASE"
      // Stop at level 4 since Royal only provides 4 RIP levels
      for (let i = 0; i < 4; i++) {
        if (ripLevelComponents.length / 2 > i) {
          rowObj[`uom${i + 1}`] = "CASE";
          rowObj[`qty${i + 1}`] = ripLevelComponents[i * 2];
          rowObj[`amt${i + 1}`] = ripLevelComponents[i * 2 + 1];
        } else {
          rowObj[`uom${i + 1}`] = null;
          rowObj[`qty${i + 1}`] = null;
          rowObj[`amt${i + 1}`] = null;
        }
      }

      // Arrange the properties based on the expected order by Bridge RIP Import Records MR
      return {
        sku: rowObj.sku,
        ripCode: rowObj.ripCode,
        brandRegistration: null,
        fromDate: rowObj.fromDate,
        toDate: rowObj.toDate,
        description: rowObj.description,
        uom1: rowObj.uom1,
        qty1: rowObj.qty1,
        amt1: rowObj.amt1,
        uom2: rowObj.uom2,
        qty2: rowObj.qty2,
        amt2: rowObj.amt2,
        uom3: rowObj.uom3,
        qty3: rowObj.qty3,
        amt3: rowObj.amt3,
        uom4: rowObj.uom4,
        qty4: rowObj.qty4,
        amt4: rowObj.amt4,
        uom5: null,
        qty5: null,
        amt5: null,
        countAs: 1,
        comments: rowObj.comments,
        lineCount: rowObj.lineCount,
      };
    }

    /**
     * Merge RIP Levels into a single row
     *
     * @param {string[]} levels RIP levels in JSON String format
     * @returns {any[]} Merged levels
     */
    mergeLevels(levels) {
      // Levels from Royal are already merged
      return Object.values(JSON.parse(levels[0]));
    }
  }

  exports.BridgeRIPRoyal = BridgeRIPRoyal;
});
