/**
 * Copy Employee on SO Transaction Emails
 * 
 * </br><b>Deployed On:</b> Message
 * </br><b>Execution Context:</b> VIEW UE
 * </br><b>Event Type/Mode:</b> CREATE UE
 * </br><b>Entry Points:</b> beforeLoad
 *
 * @NApiVersion 2.1
 * @NScriptType UserEventScript
 * @NAmdConfig /SuiteScripts/config.json
 * 
 * <AUTHOR>
 * @module spl_copy_employee_on_email_ue
 */

define(
    ["N/record","N/ui/serverWidget","N/search","N/log"], 
    (record, serverWidget, search, log) => {

        const QUOTES_PREFIX = "QUOTES"; //Prefix for Quotes Number

        //Update Case with Quotes Number
        //param {object} newRecord - The record
        //param {string} quotesNumber - The quotes number
        //return {number} - The updated case ID
        const updateCaseWithQuotesNumber = (newRecord, quotesNumber) => {

            try{
                
                let updatedCaseId = record.submitFields({
                    type: record.Type.SUPPORT_CASE,
                    id: newRecord.id,
                    values: {
                        casenumber: quotesNumber
                    }
                });

                return updatedCaseId
                
            }
            catch(error){

                log.error({
                    title: "698aee3c-2ff7-486f-a033-7862887db578: Unexpected Error",
                    details: error.toString()
                });

            }
            
        }

        //Generate Quotes Number from Case Number
        //param {string} caseNumber - The case number
        //return {string} - The quotes number
        const generateQuotesNumber = (caseNumber) => {

            return caseNumber.replace("CASE",QUOTES_PREFIX);

        }

        //Disable Form Field
        //param {object} context - The context
        //param {string} fieldId - The field ID
        //return {void}
        const disableField = (context, fieldIds) => {

            try{

                if(fieldIds){

                    for(let i = 0; i < fieldIds.length; i++){

                        let field = context.form.getField({
                            id: fieldIds[i] // Replace with the actual ID of your field
                        });

                        field.updateDisplayType({
                            displayType: serverWidget.FieldDisplayType.DISABLED
                        }); 
                    }

                }
            }
            catch(error){

                log.error({
                    title: "acc7face-f94c-4d20-b2f5-f2995ee52082: Unexpected Error",
                    details: error.toString()
                });
                
            }
        }
        
        const beforeLoad = (scriptContext) => {

            //Only run script on create
            if (scriptContext.type == scriptContext.UserEventType.CREATE) {

                let newRecord = scriptContext.newRecord;

                newRecord.setValue({
                    fieldId: "autoname",
                    value: true
                });

                disableField(scriptContext, ["autoname"]);

            }
            if (scriptContext.type == scriptContext.UserEventType.EDIT) {

                disableField(scriptContext, ["casenumber", "autoname"]);

            }

        };

        const afterSubmit = (scriptContext) => {

            //Only run script on create
            if (scriptContext.type == scriptContext.UserEventType.CREATE) {

                const quotesProfileId = 3; //Supplyline Quotes
                
                let newRecord = scriptContext.newRecord;

                let profileId = newRecord.getValue({ fieldId: "profile" });

                log.debug({
                    title: "newRecord",
                    details: newRecord.id
                });

                log.debug({
                    title: "profileId",
                    details: profileId
                });

                //Only run script if profile is Supplyline Quotes
                if(profileId == quotesProfileId){

                    //Need to lookup record as case number is not yet available on after submit after creation
                    let caseRecord = search.lookupFields({
                        type: search.Type.SUPPORT_CASE,
                        id: newRecord.id,
                        columns: ["casenumber"]
                    });

                    //let caseNumber = newRecord.getValue({ fieldId: "casenumber" });
                    
                    log.debug({
                        title: "caseNumber",
                        details: caseRecord.casenumber
                    });

                    let quotesNumber = generateQuotesNumber(caseRecord.casenumber);

                    log.debug({
                        title: "quotesNumber",
                        details: quotesNumber
                    });

                    if(quotesNumber){

                        let updatedCaseId = updateCaseWithQuotesNumber(newRecord, quotesNumber);

                        log.audit({
                            title: "updatedCaseId",
                            details: updatedCaseId
                        });

                    }
                }
            }
        };

        return { 
            beforeLoad, 
            afterSubmit 
        };
    }
);
