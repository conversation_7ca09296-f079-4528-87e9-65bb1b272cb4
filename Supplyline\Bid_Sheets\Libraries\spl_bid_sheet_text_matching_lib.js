/**
 * Library containing functions for string similarity and token normalization
 * 
 * @NApiVersion 2.1
 * 
 * <AUTHOR>
 * @module spl_bid_sheet_text_matching_lib
 */

define([], function() {
    /**
     * Normalizes a token string by converting to lowercase, handling units and removing filler words
     * @param {string} token - The string to normalize
     * @returns {string[]} Array of normalized tokens
     */ 
    function normalizeToken(token) {
        token = token.toLowerCase().trim();
        token = token.replace(/[#"]/g, ''); 
        let tokens = token.split(/[\s,]+/).filter(t => t.length > 0);
        
        const unitReplacements = {
            'w/': 'with',
            'f/': 'for',
            'pcs': 'piece',
            'pc': 'piece',
            'ea': 'each',
            'pr': 'pair',
            'pk': 'pack',
            'bx': 'box',
            'cs': 'case'
        };
    
        tokens = tokens.map(t => {
            let processed = t;
            for (const [abbr, full] of Object.entries(unitReplacements)) {
                if (processed.toLowerCase() === abbr.toLowerCase()) {
                    return full;
                }
            }
            return processed;
        });
    
        const fillerWords = new Set([
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of'
        ]);
        
        return tokens.filter(t => !fillerWords.has(t));
    }
    
    /**
     * Enhanced token normalization with support for measurements and dimensions
     * @param {string} token - The string to normalize
     * @returns {string[]} Array of normalized tokens
     */
    function enhancedNormalizeToken(token) {
        token = token.toLowerCase().trim();
        
        token = token
            .replace(/\bw\/\b/g, ' w/ ')
            .replace(/\bw\//g, ' w/ ')
            .replace(/\b\/w\b/g, ' w/ ')
            .replace(/\b\/w\//g, ' w/ ')
            .replace(/\bf\/\b/g, ' f/ ')
            .replace(/\bf\//g, ' f/ ')
            .replace(/\b\/f\b/g, ' f/ ')
            .replace(/\b\/f\//g, ' f/ ');
        
        token = token
            .replace(/(\d+\.?\d*)("|''|in|inch)(l|w|h|d)x?/g, '$1inch $3 x ')
            .replace(/(\d+\.?\d*)("|''|in|inch)(l|w|h|d)$/g, '$1inch $3')
            .replace(/(\d+\.?\d*)("|''|in|inch)x/g, '$1inch x ')
            .replace(/x(\d+\.?\d*)("|''|in|inch)/g, 'x $1inch');
            
        token = token
            .replace(/\s+l\s+/g, ' length ')
            .replace(/\s+w\s+/g, ' width ')
            .replace(/\s+h\s+/g, ' height ')
            .replace(/\s+d\s+/g, ' depth ');
        
        token = token
            .replace('½', '.5').replace('1/2', '.5')
            .replace('¼', '.25').replace('1/4', '.25')
            .replace('¾', '.75').replace('3/4', '.75')
            .replace('⅓', '.33').replace('1/3', '.33')
            .replace('⅔', '.67').replace('2/3', '.67')
            .replace(/-inch/g, 'inch')
            .replace(/-in/g, 'inch')
            .replace(/(\d+\.?\d*)\"{1}/g, '$1inch')
            .replace(/(\d+\.?\d*)''/g, '$1inch')
            .replace(/(\d+\.?\d*)'/g, '$1feet');
        
        let tokens = token.split(/[\s,]+/).filter(t => t.length > 0);
        
        const unitReplacements = {
            'pcs': 'piece',
            'pc': 'piece',
            'ea': 'each',
            'pr': 'pair',
            'pk': 'pack',
            'bx': 'box',
            'cs': 'case',
            'inches': 'inch',
            'in.': 'inch',
            'in': 'inch',
            '"': 'inch',
            '\'\'': 'inch',
            'ft.': 'feet',
            'ft': 'feet',
            'oz.': 'ounce',
            'oz': 'ounce',
            'fl': 'fluid',
            'ml.': 'milliliter',
            'ml': 'milliliter',
            'ltr': 'liter',
            'gal.': 'gallon',
            'gal': 'gallon',
            'lb.': 'pound',
            'lb': 'pound',
            'lbs': 'pound',
            'kg.': 'kilogram',
            'kg': 'kilogram',
            'mm': 'millimeter',
            'cm': 'centimeter',
            'dia.': 'diameter',
            'dia': 'diameter',
            'ø': 'diameter',
            'l': 'length',
            'w': 'width',
            'h': 'height',
            'd': 'depth'
        };
    
        tokens = tokens.map(t => {
            let processed = t;
            const numberUnitMatch = processed.match(/^(\d+\.?\d*)([a-zA-Z]+)$/);
            if (numberUnitMatch) {
                const [_, number, unit] = numberUnitMatch;
                const replacedUnit = unitReplacements[unit.toLowerCase()] || unit;
                return [number, replacedUnit];
            }
            
            for (const [abbr, full] of Object.entries(unitReplacements)) {
                if (processed.toLowerCase() === abbr.toLowerCase()) {
                    return full;
                }
            }
            return processed;
        }).flat();
    
        const fillerWords = new Set([
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of',
            'w/', '/w', '/w/', 'f/', '/f', '/f/'
        ]);
        
        return tokens.filter(t => !fillerWords.has(t) && t !== 'x');
    }

    /**
     * Calculates similarity score between two strings using basic token matching
     * @param {string} str1 - First string to compare
     * @param {string} str2 - Second string to compare
     * @returns {number} Similarity score between 0 and 1
     */
    function getTokenDistance(str1, str2) {
        const tokens1 = normalizeToken(str1);
        const tokens2 = normalizeToken(str2);
    
        if (tokens1.length === 0 || tokens2.length === 0) return 0;
    
        let exactMatches = 0;
        let consecutiveMatches = 0;
    
        tokens1.forEach(token => {
            if (tokens2.includes(token)) {
                exactMatches += 2;
            }
        });
    
        for (let i = 0; i < tokens1.length - 1; i++) {
            const pair = tokens1.slice(i, i + 2);
            if (tokens2.join(' ').includes(pair.join(' '))) {
                consecutiveMatches += 0.5;
            }
        }
    
        const score = (exactMatches + consecutiveMatches) / (tokens1.length * 2);
        return Math.min(1, score);
    }

    /**
     * Calculates enhanced similarity score between two strings using multiple matching strategies
     * @param {string} str1 - First string to compare
     * @param {string} str2 - Second string to compare
     * @returns {number} Similarity score between 0 and 1
     */
    function getEnhancedTokenDistance(str1, str2) {
        const tokens1 = enhancedNormalizeToken(str1);
        const tokens2 = enhancedNormalizeToken(str2);

        if (tokens1.length === 0 || tokens2.length === 0) return 0;

        let exactMatches = 0;
        let numberMatches = 0;
        let similarWordMatches = 0;
        let partialMatches = 0;
        let consecutiveMatches = 0;

        // Count numerical tokens for more accurate max score calculation
        const numericTokenCount = tokens1.filter(token => /^\d+(\.\d+)?$/.test(token)).length;
        const nonNumericTokenCount = tokens1.length - numericTokenCount;

        tokens1.forEach(token => {
            if (/^\d+(\.\d+)?$/.test(token)) {
                if (tokens2.includes(token)) {
                    numberMatches += 3;
                }
                return;
            }

            if (tokens2.includes(token)) {
                exactMatches += 2;
                return;
            }

            const hasSimilarWord = tokens2.some(t2 => areSimilarWords(token, t2));
            if (hasSimilarWord) {
                similarWordMatches += 1.5;
                return;
            }

            if (token.length >= 5) {
                const hasPartialMatch = tokens2.some(t2 => 
                    t2.length >= 5 && (t2.includes(token) || token.includes(t2))
                );
                if (hasPartialMatch) {
                    partialMatches += 1;
                }
            }
        });

        for (let i = 0; i < tokens1.length - 1; i++) {
            const pair = tokens1.slice(i, i + 2);
            const pairStr = pair.join(' ');
            if (tokens2.join(' ').includes(pairStr)) {
                const containsNumber = pair.some(token => /^\d+(\.\d+)?$/.test(token));
                consecutiveMatches += containsNumber ? 1.0 : 0.5;
            }
        }

        const totalScore = numberMatches + exactMatches + similarWordMatches + 
                          partialMatches + consecutiveMatches;
        
        const maxPossibleScore = (numericTokenCount * 3) + (nonNumericTokenCount * 2);
        const score = maxPossibleScore > 0 ? totalScore / maxPossibleScore : 0;
        
        // Convert score to percentage (0-100) and round up to whole number
        return Math.min(100, Math.ceil(score * 100));
    }

    /**
     * Checks if two words are similar based on common abbreviations and character similarity
     * @param {string} word1 - First word to compare
     * @param {string} word2 - Second word to compare
     * @returns {boolean} True if words are considered similar
     */
    function areSimilarWords(word1, word2) {
        if (word1 + 's' === word2 || word2 + 's' === word1) return true;
        
        const commonAbbreviations = {
            'inch': 'in',
            'inches': 'in',
            'pound': 'lb',
            'pounds': 'lbs',
            'feet': 'ft',
            'foot': 'ft',
            'millimeter': 'mm',
            'diameter': 'dia',
            'width': 'w',
            'height': 'h',
            'length': 'l',
            'ounce': 'oz',
            'ounces': 'oz',
            'fluid': 'fl',
            'fluidounce': 'floz',
            'fluidounces': 'floz',
            'fluid-ounce': 'floz',
            'fluid-ounces': 'floz',
            'fl-oz': 'floz',
            'fl-ounce': 'floz',
            'fl-ounces': 'floz'
        };

        if (commonAbbreviations[word1] === word2 || commonAbbreviations[word2] === word1) return true;
        
        const similarity = getCharacterLevelSimilarity(word1, word2);
        return similarity > 0.8;
    }

    /**
     * Calculates character-level similarity between two strings using bigram matching
     * @param {string} str1 - First string to compare
     * @param {string} str2 - Second string to compare
     * @returns {number} Similarity score between 0 and 1
     */
    function getCharacterLevelSimilarity(str1, str2) {
        if (str1 === str2) return 1.0;
        if (str1.length < 2 || str2.length < 2) return 0.0;

        let matches = 0;
        for (let i = 0; i < str1.length - 1; i++) {
            const str1Bigram = str1.substring(i, i + 2);
            for (let j = 0; j < str2.length - 1; j++) {
                const str2Bigram = str2.substring(j, j + 2);
                if (str1Bigram === str2Bigram) matches++;
            }
        }
        
        return (2.0 * matches) / (str1.length + str2.length - 2);
    }

    return {
        normalizeToken,
        enhancedNormalizeToken,
        getTokenDistance,
        getEnhancedTokenDistance,
        areSimilarWords,
        getCharacterLevelSimilarity
    };
});