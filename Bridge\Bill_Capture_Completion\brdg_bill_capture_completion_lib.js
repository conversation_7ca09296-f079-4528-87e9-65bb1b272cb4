/**
 * @description Library for Bill Capture Completion functionality
 * Contains helper functions for creating POs and Vendor Bills from Bill Capture vendor bills 
 *
 * @NApiVersion 2.1
 *
 * <AUTHOR>
 * @module brdg_bill_capture_completion_lib
 */
define([
	"require",
	"N/record",
	"N/runtime",
	"N/search",
	"N/file",
	"../../Classes/vlmd_custom_error_object",
], (require) => {
	const record = require("N/record");
	const runtime = require("N/runtime");
	const search = require("N/search");
	const file = require("N/file");
	
	const CustomErrorObject = require("../../Classes/vlmd_custom_error_object");
	const customErrorObject = new CustomErrorObject();

	/**
	 * Creates a comprehensive Purchase Order from a Vendor Bill with full field mapping
	 *
	 * @param {Object} vendorBillRecord - Source vendor bill record
	 * @param {string|number} vendorBillId - Vendor bill internal ID
	 * @returns {string|number} Created purchase order internal ID
	 */
	function createPurchaseOrderFromVendorBill(vendorBillRecord, vendorBillId) {
		try {
			log.audit('Creating Purchase Order', {
				sourceVendorBillId: vendorBillId
			});

			// Create PO record
			const poRecord = record.create({
				type: record.Type.PURCHASE_ORDER,
				isDynamic: false,
			});

			// Map header fields from Vendor Bill to Purchase Order
			mapVendorBillHeaderToPO(vendorBillRecord, poRecord, vendorBillId);

			// Map line items from Vendor Bill to Purchase Order
			mapVendorBillLinesToPO(vendorBillRecord, poRecord, vendorBillId);

			// Save the Purchase Order
			try {
				const poId = poRecord.save({
					enableSourcing: true,
					ignoreMandatoryFields: true,
				});		
				
				log.audit('Purchase Order Created Successfully', {
					sourceVendorBillId: vendorBillId,
					purchaseOrderId: poId
				});		

				return poId;
			} catch (err) {
				customErrorObject.throwError({
					summaryText: `9c316d89-a9cc-461b-b1e7-72d7ad756137 : PO_SAVE_FAILED`,
					error: err,
				});
			}


		} catch (err) {
			customErrorObject.throwError({
				summaryText: `9d6deddc-93b2-452d-9f84-fc6b08ea57cd : PURCHASE_ORDER_CREATION_FAILED`,
				error: err,
			});
		}
	}

	/**
	 * Maps header fields from Vendor Bill to Purchase Order
	 *
	 * @param {Object} vendorBillRecord - Source vendor bill record
	 * @param {Object} poRecord - Target purchase order record
	 * @param {string|number} vendorBillId - Vendor bill internal ID for logging
	 */
	function mapVendorBillHeaderToPO(vendorBillRecord, poRecord, vendorBillId) {
		const fieldMappings = [
			// Core required fields
			{ source: "entity", target: "entity", required: true },
			{ source: "subsidiary", target: "subsidiary", required: true },
			{ source: "trandate", target: "trandate", required: true },

			// Standard fields
			{ source: "memo", target: "memo", required: false },
			{ source: "terms", target: "terms", required: false },
			{ source: "currency", target: "currency", required: false },
			{ source: "exchangerate", target: "exchangerate", required: false },
			{ source: "location", target: "location", required: false },
			{ source: "department", target: "department", required: false },
			{ source: "class", target: "class", required: false },
		];

		const mappingResults = [];

		fieldMappings.forEach(mapping => {
			try {
				const sourceValue = vendorBillRecord.getValue({ fieldId: mapping.source });

				if (sourceValue !== null && sourceValue !== undefined && sourceValue !== "") {
					poRecord.setValue({
						fieldId: mapping.target,
						value: sourceValue,
					});
					mappingResults.push({
						field: mapping.source,
						value: sourceValue,
						status: "SUCCESS"
					});
				} else if (mapping.required) {
					throw new Error(`Required field ${mapping.source} is missing or empty`);
				} else {
					mappingResults.push({
						field: mapping.source,
						value: null,
						status: "SKIPPED_EMPTY"
					});
				}
			} catch (err) {
				if (mapping.required) {
					customErrorObject.throwError({
						summaryText: `859ceabf-2706-42e6-8b80-886a383c5159 : PURCHASE_ORDER_CREATION_FAILED`,
						error: err,
					});
				} else {
					mappingResults.push({
						field: mapping.source,
						status: "ERROR",
						error: err.message
					});
				}
			}
		});

		poRecord.setValue({
			fieldId: "memo",
			value: `Created from Vendor Bill ID ${vendorBillId}.`
		});

		// Prevent automatic emails from being sent
		// Currently untested - sandbox is not triggering the emailing part
		poRecord.setValue({
			fieldId: "tobeemailed",
			value: false
		});
	}

	/**
	 * Maps line items from Vendor Bill to Purchase Order
	 *
	 * @param {Object} vendorBillRecord - Source vendor bill record
	 * @param {Object} poRecord - Target purchase order record
	 * @param {string|number} vendorBillId - Vendor bill internal ID for logging
	 */
	function mapVendorBillLinesToPO(vendorBillRecord, poRecord, vendorBillId) {
		const lineCount = vendorBillRecord.getLineCount({ sublistId: "item" });
		const lineResults = [];

		for (let i = 0; i < lineCount; i++) {
			try {
				const lineFieldMappings = [
					{ source: "item", target: "item", required: true },
					{ source: "quantity", target: "quantity", required: true },
					{ source: "rate", target: "rate", required: true },
					{ source: "amount", target: "amount", required: false },
					{ source: "description", target: "description", required: false },
					{ source: "units", target: "units", required: false },
					{ source: "location", target: "location", required: false },
					{ source: "department", target: "department", required: false },
					{ source: "class", target: "class", required: false },
				];

				const lineResult = { line: i + 1, mappings: [] };

				lineFieldMappings.forEach(mapping => {
					try {
						const sourceValue = vendorBillRecord.getSublistValue({
							sublistId: "item",
							fieldId: mapping.source,
							line: i,
						});

						if (sourceValue !== null && sourceValue !== undefined && sourceValue !== "") {
							poRecord.setSublistValue({
								sublistId: "item",
								fieldId: mapping.target,
								line: i,
								value: sourceValue,
							});
							lineResult.mappings.push({
								field: mapping.source,
								value: sourceValue,
								status: "SUCCESS"
							});
						} else if (mapping.required) {
							throw new Error(`Required line field ${mapping.source} is missing on line ${i + 1}`);
						}
					} catch (err) {
						if (mapping.required) {
							customErrorObject.throwError({
								summaryText: `ddeae70f-a079-4f00-b89f-8c9b7323a334 : Line Item Mapping Failed`,
								error: err,
							});
						} else {
							lineResult.mappings.push({
								field: mapping.source,
								status: "ERROR",
								error: err.message
							});
						}
					}
				});

				lineResults.push(lineResult);

			} catch (err) {
				customErrorObject.throwError({
					summaryText: `6aefc14d-2a6f-4b28-9d8a-394fa4e7bd90 : LINE_ITEM_MAPPING_FAILED`,
					error: err,
				});
			}
		}

		log.audit('Line Items Mapping Completed', {
			vendorBillId: vendorBillId,
			totalLines: lineCount
		});
	}

	/**
	 * Creates a new Vendor Bill from a Purchase Order and attaches files from the original Bill Capture vendor bill
	 *
	 * @param {string|number} poId - Purchase order internal ID
	 * @param {Object} originalVendorBillRecord - Original Bill Capture vendor bill record
	 * @param {string|number} originalVendorBillId - Original vendor bill internal ID
	 * @returns {string|number} Created vendor bill internal ID
	 */
	function createVendorBillFromPurchaseOrder(poId, originalVendorBillRecord, originalVendorBillId) {
		try {
			log.audit('Creating Vendor Bill from Purchase Order', {
				purchaseOrderId: poId,
				originalVendorBillId: originalVendorBillId
			});

			const additionalFieldMappings = [
				// Additional required fields
				{ source: "tranid", target: "tranid", required: true }
			];

			// Transform Purchase Order to Vendor Bill
			const newVendorBill = record.transform({
				fromType: record.Type.PURCHASE_ORDER,
				fromId: poId,
				toType: record.Type.VENDOR_BILL,
			});

			// Set reference to original Bill Capture vendor bill
			const newMemo = `Created from Original Bill ID: ${originalVendorBillId}.`;

			additionalFieldMappings.forEach(mapping => {
				const sourceValue = originalVendorBillRecord.getValue({ fieldId: mapping.source });

				if (sourceValue !== null && sourceValue !== undefined && sourceValue !== "") {
					newVendorBill.setValue({
						fieldId: mapping.target,
						value: sourceValue,
					});
				} else if (mapping.required) {
					customErrorObject.throwError({
						summaryText: `2faef418-72e2-4cf4-83d5-0d9ff5d20ff9 : REQUIRED_FIELD_MISSING`,
						error: new Error(`Required field ${mapping.source} is missing or empty`),
					});
				}
			});
			
			// Hard code next approver
			// 43398 = Miri Landau (Same id for prod and sandbox)
			newVendorBill.setValue({
				fieldId: "nextapprover",
				value: 43398,
			});

			newVendorBill.setValue({
				fieldId: "memo",
				value: newMemo
			});

			// Hard Code Approval Status
			// 1 = Approved
			// 2 = Pending Approval
			// 3 = Rejected
			newVendorBill.setValue({
				fieldId: "approvalstatus",
				value: 1
			});

			// Save the new vendor bill
			const newVendorBillId = newVendorBill.save({
				enableSourcing: true,
				ignoreMandatoryFields: true,
			});

			log.audit('New Vendor Bill Created Successfully', {
				originalVendorBillId: originalVendorBillId,
				purchaseOrderId: poId,
				newVendorBillId: newVendorBillId
			});

			// Attach files from original Bill Capture vendor bill to new vendor bill
			attachFilesFromOriginalBill(originalVendorBillId, newVendorBillId);

			return newVendorBillId;
		} catch (err) {
			customErrorObject.throwError({
				summaryText: `0ba2abf6-3418-4aa5-b9e5-5b16a035976f : VENDOR_BILL_CREATION_FROM_PO_FAILED`,
				error: err,
			});
		}
	}

	/**
	 * Attaches files from the original Bill Capture vendor bill to the new vendor bill
	 *
	 * @param {string|number} originalVendorBillId - Original vendor bill internal ID
	 * @param {string|number} newVendorBillId - New vendor bill internal ID
	 */
	function attachFilesFromOriginalBill(originalVendorBillId, newVendorBillId) {
		try {
			// Get files attached to the original vendor bill
			const fileResults = search.lookupFields({
				type: search.Type.TRANSACTION,
				id: originalVendorBillId,
				columns: "file.internalid",
			})["file.internalid"];

			// Get target folder ID where bill capture file will be moved
			const targetFolderId = runtime.getCurrentScript().getParameter({
				name: "custscript_brdg_bll_cptr_fl_trgt_fldr_id"
			});

			if (fileResults && fileResults.length > 0) {
				const attachmentResults = [];
				fileResults.forEach((fileResult) => {
					try {
						const fileId = fileResult.value;

						// Move file to target folder first, then attach to new vendor bill
						const fileObj = file.load({ id: fileId });
						fileObj.folder = targetFolderId;
						fileObj.save();

						record.attach({
							record: {
								type: 'file',
								id: fileId
							},
							to: {
								type: record.Type.VENDOR_BILL,
								id: newVendorBillId
							}
						});

						attachmentResults.push({
							fileId: fileId,
							status: "SUCCESS"
						});
					} catch (err) {
						customErrorObject.throwError({
							summaryText: `c116daff-8741-4946-8581-dee271f9cba6 : FILE_ATTACHMENT_FAILED`,
							error: err,
						});

						attachmentResults.push({
							fileId: fileResult.value,
							status: "ERROR",
							error: err.message
						});
					}
				});

				log.audit('File Attachment Process Completed', {
					originalVendorBillId: originalVendorBillId,
					newVendorBillId: newVendorBillId,
					totalFiles: fileResults.length,
					attachmentResults: attachmentResults
				});

			} else {
				log.audit('No Files Found to Attach', {
					originalVendorBillId: originalVendorBillId,
					newVendorBillId: newVendorBillId
				});
			}

		} catch (err) {
			customErrorObject.throwError({
				summaryText: `dba6de34-cd5a-410d-b8c4-b8a79e1f4647 : FILE_ATTACHMENT_PROCESS_FAILED`,
				error: err,
			});
			// Don't throw error here - file attachment failure shouldn't stop the main process
		}
	}

	/**
	 * Marks the original Bill Capture vendor bill for cancellation by setting approval status to Rejected
	 *
	 * @param {string|number} originalVendorBillId - Original vendor bill internal ID
	 */
	function markOriginalBillForCancellation(originalVendorBillId) {
		try {
			record.submitFields({
				type: record.Type.VENDOR_BILL,
				id: originalVendorBillId,
				values: {
					'cancelvendbill': 'T'
				},
				options: {
					ignoreMandatoryFields: true
				}
			});
			
			log.audit('Original Bill Successfully Marked for Cancellation', {
				originalVendorBillId: originalVendorBillId
			});
		} catch (err) {
			customErrorObject.throwError({
				summaryText: `35ca2044-483a-439a-8a3f-f8a3dd0bf963 : FAILED_TO_MARK_ORIGINAL_BILL_FOR_CANCELLATION`,
				error: err,
			});
		}
	}

	/**
	 * Reverts the bill capture completion process by deleting created records
	 *
	 * @param {string|number} [poId] - Purchase Order ID to delete (optional)
	 * @param {string|number} [newVendorBillId] - New Vendor Bill ID to delete (optional)
	 */
	function revertProcess(poId, newVendorBillId) {
		// Delete new vendor bill first (if provided)
		if (newVendorBillId) {
			try {
				record.delete({
					type: record.Type.VENDOR_BILL,
					id: newVendorBillId
				});
				log.audit('Vendor Bill Deleted', { deletedVendorBillId: newVendorBillId });
			} catch (err) {
				customErrorObject.throwError({
					summaryText: `4eba114d-347e-4a6c-b0a9-716298e3a331 : FAILED_TO_DELETE_VENDOR_BILL_IN_REVERT`,
					error: err,
				});
			}
		}

		// Delete purchase order (if provided)
		if (poId) {
			try {
				record.delete({
					type: record.Type.PURCHASE_ORDER,
					id: poId
				});
				log.audit('Purchase Order Deleted', { deletedPurchaseOrderId: poId });
			} catch (err) {
				customErrorObject.throwError({
					summaryText: `721b9704-1887-4bd6-847b-931ec7003025 : FAILED_TO_DELETE_PURCHASE_ORDER_IN_REVERT`,
					error: err,
				});
			}
		}
	}

	return {
		createPurchaseOrderFromVendorBill,
		mapVendorBillHeaderToPO,
		mapVendorBillLinesToPO,
		createVendorBillFromPurchaseOrder,
		attachFilesFromOriginalBill,
		markOriginalBillForCancellation,
		revertProcess
	};
});
