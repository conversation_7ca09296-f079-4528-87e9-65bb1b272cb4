/**
 * @description TextMatching class for string similarity and token normalization
 *
 * @NApiVersion 2.1
 *
 * <AUTHOR>
 * @module spl_bid_sheet_text_matching_class
 */

define([
	"exports",
	"./spl_bid_sheet_text_matching_constants",
	"../../../../Classes/vlmd_custom_error_object",
], (
	/** @type {any} */ exports,
	/** @type {any} */ textMatchingConstants,
	/** @type {any} */ CustomErrorObject
) => {

	/**
	 * TextMatching class for string similarity and token normalization
	 *
	 * @class
	 */
    class TextMatching {
        /**
         * Create a new TextMatching instance
         * @param {Object} options - Configuration options
         * @param {string} [options.stringOne=''] - First string to compare
         * @param {string} [options.stringTwo=''] - Second string to compare
         * @param {boolean} [options.buildTokens=false] - Whether to build tokens on instantiation
         */
        constructor(options = {}) {
            const {
                stringOne = '',
                stringTwo = '',
                buildTokens = false
            } = options;

            this.customErrorObject = new CustomErrorObject();

            // Initialize strings
            this.stringOne = '';
            this.stringTwo = '';

            // Initialize token structures
            this.tokensOne = {
                all: [],
                words: [],
                dimensions: [],
                measurements: [],
                packaging: []
            };

            this.tokensTwo = {
                all: [],
                words: [],
                dimensions: [],
                measurements: [],
                packaging: []
            };

            // Import constants from the constants file
            this.fillerWords = textMatchingConstants.FILLER_WORDS;
            this.unitReplacements = textMatchingConstants.UNIT_REPLACEMENTS;
            this.units = textMatchingConstants.UNITS;
            this.dimensionPatterns = textMatchingConstants.DIMENSION_PATTERNS;
            this.packagingPatterns = textMatchingConstants.PACKAGING_PATTERNS;

            // Set strings and generate tokens if provided
            this.setStrings({stringOne, stringTwo, buildTokens});
        }

        /**
         * Sets the strings to compare for the TextMatching instance
         * @param {Object} options - Configuration options
         * @param {string} [options.stringOne=''] - String one to compare
         * @param {string} [options.stringTwo=''] - String two to compare
         * @param {boolean} [options.buildTokens=true] - Whether to build the tokens after setting the strings
         */
        setStrings(options = {}) {
            const {
                stringOne = '',
                stringTwo = '',
                buildTokens = true
            } = options;

            this.stringOne = stringOne;
            this.stringTwo = stringTwo;

            if(buildTokens) {
                this.tokensOne = this.generateTokens({stringToTokenize: stringOne});
                this.tokensTwo = this.generateTokens({stringToTokenize: stringTwo});
            }
        }

        /**
         * Generates tokens from a string by sanitizing, normalizing, and tokenizing
         * @param {Object} options - Tokenization options
         * @param {string} [options.stringToTokenize=''] - String to tokenize
         * @param {boolean} [options.removeFillerWords=true] - Whether to remove filler words
         * @param {boolean} [options.categorizeTokens=false] - Whether to categorize tokens
         * @returns {Array|Object} Tokens array or categorized tokens object
         */
        generateTokens(options = {}) {
            const {
                stringToTokenize = '',
                removeFillerWords = true,
                categorizeTokens = false
            } = options;

            if (!stringToTokenize || typeof stringToTokenize !== 'string') {
                return categorizeTokens ?
                    { all: [], words: [], dimensions: [], measurements: [], packaging: [] } :
                    [];
            }

            try {
                // First Part: Sanitation and Normalization
                let normalizedString = this.sanitizeAndLowerCase(stringToTokenize);
                normalizedString = this.handleUnitsAndAbbreviations(normalizedString);
                normalizedString = this.removeFreeStandingSpecialCharacters(normalizedString);

                // Second Part: Filtering
                if (removeFillerWords) {
                    normalizedString = this.removeFillerWords(normalizedString);
                }

                // Third Part: Tokenization
                if (categorizeTokens) {
                    // Initialize the categorized tokens object
                    const categorizedTokens = {
                        all: [],
                        words: [],
                        dimensions: [],
                        measurements: [],
                        packaging: []
                    };

                    // Add word tokens
                    const { tokens: wordTokens, remainingString: remainingStringAfterWords } = this.tokenizeWords(normalizedString);
                    categorizedTokens.words = [...new Set(wordTokens)];
                    categorizedTokens.all.push(...categorizedTokens.words);

                    // Add measurement tokens
                    const { tokens: measurementTokens, remainingString: remainingStringAfterDimensions } =
                        this.tokenizeMeasurementsAndDimensions(remainingStringAfterWords);
                    categorizedTokens.measurements = [...new Set(measurementTokens)];
                    categorizedTokens.all.push(...categorizedTokens.measurements);

                    // Add packaging tokens
                    const { tokens: packagingTokens } = this.tokenizePackaging(remainingStringAfterDimensions);
                    categorizedTokens.packaging = [...new Set(packagingTokens)];
                    categorizedTokens.all.push(...categorizedTokens.packaging);
                    
                    return categorizedTokens;
                } else {
                    let tokens = normalizedString.split(' ');
                    tokens = [...new Set(tokens)];
                    return tokens.filter(token => token.length > 0);
                }
            } catch (err) {
                this.customErrorObject.throwError({
                    summaryText: "Error generating tokens",
                    error: err
                });
                return categorizeTokens ?
                    { all: [], words: [], dimensions: [], measurements: [], packaging: [] } :
                    [];
            }
        }

        /**
         * Lower cases the string and removes extra spaces
         * @param {string} stringToNormalize - The string to normalize
         * @returns {string} sanitized string
         */
        sanitizeAndLowerCase(stringToNormalize) {
            // Lowercase and trim
            stringToNormalize = stringToNormalize.toLowerCase().trim();

            // Remove shortcuts like w/ and f/
            stringToNormalize = stringToNormalize.replace(/\bw\/\b/g, 'with ');
            stringToNormalize = stringToNormalize.replace(/\bf\/\b/g, 'from ');

            // Replace double double-quotes with a single quote (escaped quotes in CSV)
            stringToNormalize = stringToNormalize.replace(/""/g, '"');

            // Remove quote if not preceded by a digit (i.e. likely not a measurement like 18")
            stringToNormalize = stringToNormalize.replace(/(?<!\d)"/g, '');

            // Correct double single quotes into a single double quote
            stringToNormalize = stringToNormalize.replace(/''/g, '"');

            // Remove parentheses
            stringToNormalize = stringToNormalize.replace(/[\(\)]/g, '');

            // Remove slashes that are not part of a fraction
            stringToNormalize = stringToNormalize.replace(/([a-zA-Z])\s*\/\s*([a-zA-Z])/g, '$1 $2');

            // Collapse multiple spaces
            stringToNormalize = stringToNormalize.replace(/\s{2,}/g, ' ');

            // Replace separators (",", ";", ":", "|") with spaces
            stringToNormalize = stringToNormalize.replace(/[,;:|]/g, ' ');

            return stringToNormalize;
        }

        /**
         * Handles units and abbreviations in the string
         * @param {string} stringToNormalize - The string to normalize
         * @returns {string} sanitized string
         */
        handleUnitsAndAbbreviations(stringToNormalize) {
            // Add spaces around dimension separator 'x' if missing
            // First, ensure there's a space before each 'x'
            stringToNormalize = stringToNormalize.replace(/(\d+(?:[a-zA-Z"']+)?)([xX])/g, '$1 $2');
            // Then, ensure there's a space after each 'x'
            stringToNormalize = stringToNormalize.replace(/([xX])(\d+(?:[a-zA-Z"']+)?)/g, '$1 $2');

            const tokens = stringToNormalize.split(' ');
            let normalizedTokens = tokens.map(token => {
                // Match measurement units
                const measurementMatch = token.match(/^((?:\d+(?:\.\d+)?)|(?:\d+\/\d+)|(?:\d+\-\d+\/\d+))([a-zA-Z]*["']?[lwhdLWHD]?)$/);

                if (measurementMatch) {
                    const [_, number, unit] = measurementMatch;
                    let normalizedUnit = unit;

                    // Handle special case of quotes followed by dimension indicators
                    if (unit.includes('"') || unit.includes("'")) {
                        // Extract dimension indicator if present (l, w, h, d)
                        const dimensionIndicator = unit.match(/[lwhdLWHD]$/)?.[0] || '';
                        // Get the base unit (likely inches or feet)
                        const baseUnit = unit.replace(/[lwhdLWHD]$/, '');
                        // Normalize the base unit
                        const normalizedBaseUnit = this.unitReplacements[baseUnit] || baseUnit;

                        // Map dimension indicators to full words
                        const dimensionMap = {
                            'l': ' length',
                            'w': ' width',
                            'h': ' height',
                            'd': ' depth',
                            'L': ' length',
                            'W': ' width',
                            'H': ' height',
                            'D': ' depth'
                        };

                        // Combine with full dimension word if indicator is present
                        if (dimensionIndicator && dimensionMap[dimensionIndicator]) {
                            return `${number}${normalizedBaseUnit}${dimensionMap[dimensionIndicator]}`;
                        } else {
                            // Just use the normalized unit if no dimension indicator
                            return `${number}${normalizedBaseUnit}`;
                        }
                    } else {
                        normalizedUnit = this.unitReplacements[unit] || unit;
                        return `${number}${normalizedUnit}`;
                    }
                }

                // Handle standalone units
                return this.unitReplacements[token] || token;
            });

            // Remove X or x
            normalizedTokens = normalizedTokens.filter(token => token !== 'x' && token !== 'X');

            return normalizedTokens.join(' ');
        }

        /**
         * Removes free standing special characters from the string
         */
        removeFreeStandingSpecialCharacters(stringToNormalize) {
            stringToNormalize = stringToNormalize.replace(/[#`~!@$%^&*()_+<>?:{}|,./;'[\]=-]/g, '');

            return stringToNormalize;
        }

        /**
         * Removes filler words from the string
         * @param {string} stringToNormalize - The string to normalize
         * @returns {string} sanitized string
         */
        removeFillerWords(stringToNormalize) {
            const tokens = stringToNormalize.split(' ');
            const filteredTokens = tokens.filter(token => {
                return !this.fillerWords.has(token);
            });
            return filteredTokens.join(' ');
        }


        /**
         * Tokenizes words in the string and removes them from the original string
         * Also combines numbers and units into a single token
         * @param {string} stringToNormalize - The string to normalize
         * @returns {{tokens: string[], remainingString: string}} Word tokens and remaining string
         */
        tokenizeWords(stringToNormalize) {
            // Check if there are words with hypens, duplicate word but without the hypen.
            // Improves matching probability by duplicating hypenated words into hypenated and non-hypenated versions.example "gel-rite" -> "gel-rite gelrite"
            stringToNormalize = stringToNormalize.replace(/([a-zA-Z]+)-([a-zA-Z]+)/g, '$1-$2 $1$2');

            // Convert to array of tokens
            const individualTokens = stringToNormalize.split(' ').filter(t => t.length > 0);

            // Separate word tokens from non-word tokens
            const wordTokens = [];
            const nonWordTokens = [];
            let previousToken = '';
            for (const token of individualTokens) {

                // Do nothing if token is a number
                if(!isNaN(token)) {
                    previousToken = token;
                    continue;
                }

                // Check if previous token was a number and this token is a unit
                if ((!isNaN(previousToken) || previousToken.includes('/')) && this.units.has(token)) {
                    nonWordTokens.push(`${previousToken}${token}`);
                    previousToken = '';
                    continue;
                }

                // Check if current token is a unit and previous token was also a unit
                else if (this.units.has(token) && this.units.has(previousToken)) {
                    nonWordTokens.push(`${token}`);
                    previousToken = '';
                    continue;
                }

                // Check if token is a word (only letters) and not a number and not a unit AND not 'X'
                else if (/^[a-zA-Z]+(-[a-zA-Z]+)*$/.test(token) && token !== 'x') {
                    wordTokens.push(token);
                    continue;
                }

                // Handle 'X' as this is part of dimensions
                else if (token === 'x' || token === 'X') {
                    nonWordTokens.push(token);
                    continue;
                }

                else {
                    nonWordTokens.push(token);
                    previousToken = token;
                }

            }

            // Return both the word tokens and the remaining string
            return {
                tokens: wordTokens,
                remainingString: nonWordTokens.join(' ')
            };
        }

        /**
         * Handles measurements and dimensions in the string
         * @param {string} stringToNormalize - The string to normalize
         * @returns {string} sanitized string
         */
        tokenizeMeasurementsAndDimensions(stringToNormalize) {

            // Convert to array of tokens
            let individualTokens = stringToNormalize.split(' ').filter(t => t.length > 0);

            // Ignore any tokens that has the word each in it or ea
            individualTokens = individualTokens.filter(token => !token.includes('ea') && !token.includes('each'));

            return {
                tokens: individualTokens,
                remainingString: ''
            };
        }

        /**
         * Handles packaging in the string
         * @param {string} stringToNormalize - The string to normalize
         * @returns {Object} Object containing tokens and remaining string
         */
        tokenizePackaging(stringToNormalize) {
            try {
                let processedString = stringToNormalize;
                const packagingTokens = [];

                // Apply packaging patterns from constants
                this.packagingPatterns.forEach(pattern => {
                    // Find all matches
                    const matches = [...processedString.matchAll(pattern.pattern)];

                    // Process each match
                    matches.forEach(match => {
                        // Get the full match
                        const fullMatch = match[0];

                        // Create the replacement
                        const replacement = pattern.replacement.replace(/\$(\d+)/g, (_, group) => match[group] || '');

                        // Add to tokens
                        packagingTokens.push(replacement);

                        // Remove from string to avoid double-processing
                        processedString = processedString.replace(fullMatch, '');
                    });
                });

                return {
                    tokens: packagingTokens,
                    remainingString: processedString
                };
            } catch (err) {
                this.customErrorObject.throwError({
                    summaryText: "Error tokenizing packaging",
                    error: err
                });
                return {
                    tokens: [],
                    remainingString: stringToNormalize
                };
            }
        }

        /**
         * Calculates similarity between two strings using Jaccard similarity
         * @param {string} stringOne - First string to compare
         * @param {string} stringTwo - Second string to compare
         * @returns {number} Similarity score (0-1)
         */
        calculateSimilarity(stringOne, stringTwo) {
            try {
                // Generate tokens
                const tokensOne = this.generateTokens({
                    stringToTokenize: stringOne,
                    categorizeTokens: true
                });
                const tokensTwo = this.generateTokens({
                    stringToTokenize: stringTwo,
                    categorizeTokens: true
                });

                // Calculate Jaccard similarity
                const tokenSetOne = new Set(tokensOne);
                const tokenSetTwo = new Set(tokensTwo);

                const intersection = new Set([...tokenSetOne].filter(x => tokenSetTwo.has(x)));
                const union = new Set([...tokenSetOne, ...tokenSetTwo]);

                return union.size > 0 ? intersection.size / union.size : 0;
            } catch (err) {
                this.customErrorObject.throwError({
                    summaryText: "Error calculating similarity",
                    error: err
                });
                return 0;
            }
        }

        /**
         * Calculates distance score between two strings using multiple matching strategies
         * @param {Object} options - Configuration options
         * @param {string} [options.stringOne=this.stringOne] - First string to compare
         * @param {string} [options.stringTwo=this.stringTwo] - Second string to compare
         * @param {number} [options.threshold=0] - Minimum score threshold (0-100). If the calculated score is below this threshold, returns null
         * @returns {Object|null} Object containing score and detailed matching information, or null if score is below threshold
         */
        calculateDistance(options = {}) {
            const {stringOne = this.stringOne, stringTwo = this.stringTwo, threshold = 0} = options;
            try {
                // Generate tokens using the class's existing generateTokens method with categorization
                const tokensOne = this.generateTokens({
                    stringToTokenize: stringOne,
                    categorizeTokens: true
                });
                const tokensTwo = this.generateTokens({
                    stringToTokenize: stringTwo,
                    categorizeTokens: true
                });

                // Get the "all" tokens
                const tokens1 = tokensOne.all;
                const tokens2 = tokensTwo.all;

                if (tokens1.length === 0 || tokens2.length === 0) {
                    // If threshold is greater than 0, return null for empty tokens
                    return threshold > 0 ? null : { score: 0, details: { reason: "Empty tokens" } };
                }

                // Initialize match counters and details
                let exactMatches = 0;
                let numberMatches = 0;
                let dimensionMatches = 0;
                let similarWordMatches = 0;
                let partialMatches = 0;
                let consecutiveMatches = 0;

                // Track detailed match information
                const matchDetails = {
                    exactMatches: [],
                    numberMatches: [],
                    dimensionMatches: [],
                    similarWordMatches: [],
                    partialMatches: [],
                    consecutiveMatches: []
                };

                // Count tokens by type for more accurate max score calculation
                const numericTokenCount = tokens1.filter(token => /^\d+(\.\d+)?$/.test(token)).length;
                const dimensionTokenCount = tokensOne.dimensions.length + tokensOne.measurements.length;
                const wordTokenCount = tokensOne.words.length;
                const packagingTokenCount = tokensOne.packaging.length;
                const otherTokenCount = tokens1.length - numericTokenCount - dimensionTokenCount - wordTokenCount - packagingTokenCount;

                // Assign weights to different token types
                const weights = {
                    numeric: 3,      // Numbers are important for exact specifications
                    dimension: 5,    // Dimensions are very important for product matching
                    word: 2,         // Regular words
                    packaging: 1,    
                    other: 1.5,      // Other tokens
                    similar: 0.25,   // Multiplier for similar matches (75% of full value)
                    partial: 0.5     // Multiplier for partial matches (50% of full value)
                };

                // Process dimension and measurement tokens first (highest priority)
                [...tokensOne.dimensions, ...tokensOne.measurements].forEach(token => {
                    if (tokens2.includes(token)) {
                        dimensionMatches += weights.dimension;
                        matchDetails.dimensionMatches.push(token);
                    } else {
                        // Check for similar dimension matches
                        const similarDimension = tokens2.find(t2 =>
                            this.areSimilarDimensions(token, t2)
                        );
                        if (similarDimension) {
                            dimensionMatches += weights.dimension * weights.similar;
                            matchDetails.dimensionMatches.push({ token, similarTo: similarDimension, partial: true });
                        }
                    }
                });

                // Process numeric tokens
                tokens1.filter(token => /^\d+(\.\d+)?$/.test(token)).forEach(token => {
                    if (tokens2.includes(token)) {
                        numberMatches += weights.numeric;
                        matchDetails.numberMatches.push(token);
                    }
                });

                // Process word tokens
                tokensOne.words.forEach(token => {
                    if (tokens2.includes(token)) {
                        exactMatches += weights.word;
                        matchDetails.exactMatches.push(token);
                        return;
                    }

                    const similarWord = tokens2.find(t2 => this.areSimilarWords(token, t2));
                    if (similarWord) {
                        similarWordMatches += weights.word * weights.similar;
                        matchDetails.similarWordMatches.push({ token, similarTo: similarWord });
                        return;
                    }

                    if (token.length >= 4) {
                        const partialMatch = tokens2.find(t2 =>
                            t2.length >= 4 && (t2.includes(token) || token.includes(t2))
                        );
                        if (partialMatch) {
                            partialMatches += weights.word * weights.partial;
                            matchDetails.partialMatches.push({ token, partialMatch });
                        }
                    }
                });

                // Process packaging tokens
                tokensOne.packaging.forEach(token => {
                    if (tokens2.includes(token)) {
                        exactMatches += weights.packaging;
                        matchDetails.exactMatches.push(token);
                    }
                });

                // Process consecutive tokens for phrase matching
                for (let i = 0; i < tokens1.length - 1; i++) {
                    const pair = tokens1.slice(i, i + 2);
                    const pairStr = pair.join(' ');
                    if (tokens2.join(' ').includes(pairStr)) {
                        // Higher weight for consecutive matches with numbers or dimensions
                        const containsNumber = pair.some(token => /^\d+(\.\d+)?$/.test(token));
                        const containsDimension = pair.some(token =>
                            tokensOne.dimensions.includes(token) || tokensOne.measurements.includes(token)
                        );

                        if (containsDimension) {
                            consecutiveMatches += 2.0;
                        } else if (containsNumber) {
                            consecutiveMatches += 1.5;
                        } else {
                            consecutiveMatches += 1.0;
                        }

                        matchDetails.consecutiveMatches.push({
                            pair,
                            containsNumber,
                            containsDimension
                        });
                    }
                }

                // Calculate total score
                const totalScore = numberMatches + dimensionMatches + exactMatches +
                                  similarWordMatches + partialMatches + consecutiveMatches;

                // Calculate maximum possible score with weighted token types
                const maxPossibleScore = (numericTokenCount * weights.numeric) +
                                        (dimensionTokenCount * weights.dimension) +
                                        (wordTokenCount * weights.word) +
                                        (packagingTokenCount * weights.packaging) +
                                        (otherTokenCount * weights.other);

                // Calculate a more balanced normalization factor
                // Instead of using the minimum length directly, use a weighted approach
                // that considers both string lengths but doesn't penalize longer strings too much
                const lengthRatio = tokens2.length / tokens1.length;
                let normalizationFactor;

                if (lengthRatio >= 1) {
                    // Second string is longer or equal - no adjustment needed
                    normalizationFactor = 1.0;
                } else {
                    // Second string is shorter - apply a softer adjustment
                    // This formula creates a curve that reduces the penalty as strings get closer in length
                    // For very short strings compared to long ones, we still adjust but not as drastically
                    normalizationFactor = 0.7 + (0.3 * lengthRatio);
                }

                const adjustedMaxScore = maxPossibleScore * normalizationFactor;

                // Calculate final score (0-100)
                const score = adjustedMaxScore > 0 ? (totalScore / adjustedMaxScore) : 0;
                const finalScore = Math.min(100, Math.ceil(score * 100));

                // Check if the score is below the threshold
                if (finalScore < threshold) {
                    return null;
                }

                return {
                    score: finalScore,
                    details: {
                        tokens1 : {
                            all: tokens1,
                            words: tokensOne.words,
                            dimensions: tokensOne.dimensions,
                            measurements: tokensOne.measurements,
                            packaging: tokensOne.packaging
                        },
                        tokens2: {
                            all: tokens2,
                            words: tokensTwo.words,
                            dimensions: tokensTwo.dimensions,
                            measurements: tokensTwo.measurements,
                            packaging: tokensTwo.packaging
                        },
                        tokenCounts: {
                            numeric: numericTokenCount,
                            dimension: dimensionTokenCount,
                            word: wordTokenCount,
                            packaging: packagingTokenCount,
                            other: otherTokenCount
                        },
                        exactMatches: { count: exactMatches, tokens: matchDetails.exactMatches },
                        numberMatches: { count: numberMatches, tokens: matchDetails.numberMatches },
                        dimensionMatches: { count: dimensionMatches, tokens: matchDetails.dimensionMatches },
                        similarWordMatches: { count: similarWordMatches, matches: matchDetails.similarWordMatches },
                        partialMatches: { count: partialMatches, matches: matchDetails.partialMatches },
                        consecutiveMatches: { count: consecutiveMatches, matches: matchDetails.consecutiveMatches },
                        totalScore,
                        maxPossibleScore,
                        adjustedMaxScore
                    }
                };
            } catch (err) {
                this.customErrorObject.throwError({
                    summaryText: "Error calculating distance",
                    error: err
                });
                // If threshold is greater than 0, return null for errors
                return threshold > 0 ? null : { score: 0, details: { error: err.message } };
            }
        }

        /**
         * Checks if two words are similar based on common abbreviations and character similarity
         * @param {string} word1 - First word to compare
         * @param {string} word2 - Second word to compare
         * @returns {boolean} True if words are considered similar
         */
        areSimilarWords(word1, word2) {
            if (word1 + 's' === word2 || word2 + 's' === word1) return true;

            const commonAbbreviations = {
                'inch': 'in',
                'inches': 'in',
                'pound': 'lb',
                'pounds': 'lbs',
                'feet': 'ft',
                'foot': 'ft',
                'millimeter': 'mm',
                'diameter': 'dia',
                'width': 'w',
                'height': 'h',
                'length': 'l',
                'ounce': 'oz',
                'ounces': 'oz',
                'fluid': 'fl',
                'fluidounce': 'floz',
                'fluidounces': 'floz',
                'fluid-ounce': 'floz',
                'fluid-ounces': 'floz',
                'fl-oz': 'floz',
                'fl-ounce': 'floz',
                'fl-ounces': 'floz'
            };

            if (commonAbbreviations[word1] === word2 || commonAbbreviations[word2] === word1) return true;

            const similarity = this.getCharacterLevelSimilarity(word1, word2);
            return similarity > 0.8;
        }

        /**
         * Calculates character-level similarity between two strings using bigram matching
         * @param {string} str1 - First string to compare
         * @param {string} str2 - Second string to compare
         * @returns {number} Similarity score between 0 and 1
         */
        getCharacterLevelSimilarity(str1, str2) {
            if (str1 === str2) return 1.0;
            if (str1.length < 2 || str2.length < 2) return 0.0;

            let matches = 0;
            for (let i = 0; i < str1.length - 1; i++) {
                const str1Bigram = str1.substring(i, i + 2);
                for (let j = 0; j < str2.length - 1; j++) {
                    const str2Bigram = str2.substring(j, j + 2);
                    if (str1Bigram === str2Bigram) matches++;
                }
            }

            return (2.0 * matches) / (str1.length + str2.length - 2);
        }

        /**
         * Checks if two dimension strings are similar
         * @param {string} dim1 - First dimension string
         * @param {string} dim2 - Second dimension string
         * @returns {boolean} True if dimensions are similar
         */
        areSimilarDimensions(dim1, dim2) {
            // Extract numeric part and unit part
            const dim1Match = dim1.match(/^(\d+(?:\.\d+)?)(.*)/);
            const dim2Match = dim2.match(/^(\d+(?:\.\d+)?)(.*)/);

            if (!dim1Match || !dim2Match) return false;

            const [_, num1, unit1] = dim1Match;
            const [__, num2, unit2] = dim2Match;

            // Check if numbers are close (within 10%)
            const numValue1 = parseFloat(num1);
            const numValue2 = parseFloat(num2);

            const numDiff = Math.abs(numValue1 - numValue2);
            const numAvg = (numValue1 + numValue2) / 2;
            const numSimilarity = numDiff / numAvg;

            // Numbers are similar if within 10% of each other
            const numbersSimilar = numSimilarity <= 0.1;

            // Units are similar if they're the same after normalization
            const unitsSimilar = this.normalizeUnit(unit1) === this.normalizeUnit(unit2);

            return numbersSimilar && unitsSimilar;
        }

        /**
         * Normalizes a unit string for comparison
         * @param {string} unit - Unit string to normalize
         * @returns {string} Normalized unit
         */
        normalizeUnit(unit) {
            // Remove any non-alphanumeric characters
            const cleanUnit = unit.replace(/[^a-zA-Z0-9]/g, '').toLowerCase();

            // Map common unit variations
            const unitMap = {
                'in': 'inch',
                'inches': 'inch',
                '"': 'inch',
                'ft': 'foot',
                'feet': 'foot',
                '\'': 'foot',
                'mm': 'millimeter',
                'cm': 'centimeter',
                'm': 'meter',
                'kg': 'kilogram',
                'lb': 'pound',
                'lbs': 'pound',
                'oz': 'ounce'
            };

            return unitMap[cleanUnit] || cleanUnit;
        }

    }

    // Export the class
    exports.TextMatching = TextMatching;

    return TextMatching;
});
